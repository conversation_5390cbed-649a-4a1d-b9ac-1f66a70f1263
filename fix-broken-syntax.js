#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixBrokenSyntax(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix broken Tabs syntax
    const tabsPattern = /<Tabs\.>/g;
    if (content.match(tabsPattern)) {
      content = content.replace(tabsPattern, '<Tabs.List>');
      modified = true;
    }

    const tabsEndPattern = /<\/Tabs\.>/g;
    if (content.match(tabsEndPattern)) {
      content = content.replace(tabsEndPattern, '</Tabs.List>');
      modified = true;
    }

    // Fix broken Menu syntax
    const menuPattern = /<Menu\.\/>/g;
    if (content.match(menuPattern)) {
      content = content.replace(menuPattern, '<Menu.Dropdown />');
      modified = true;
    }

    // Fix broken icon properties (missing icon:)
    const iconPattern = /icon:\s*}/g;
    if (content.match(iconPattern)) {
      content = content.replace(iconPattern, 'icon: <IconCar size={16} />}');
      modified = true;
    }

    // Fix broken icon properties (missing icon: with comma)
    const iconCommaPattern = /icon:\s*},/g;
    if (content.match(iconCommaPattern)) {
      content = content.replace(iconCommaPattern, 'icon: <IconCar size={16} />},');
      modified = true;
    }

    // Fix broken return statements
    const returnPattern = /returncase/g;
    if (content.match(returnPattern)) {
      content = content.replace(returnPattern, 'return; case');
      modified = true;
    }

    // Fix broken return default
    const returnDefaultPattern = /returndefault:/g;
    if (content.match(returnDefaultPattern)) {
      content = content.replace(returnDefaultPattern, 'return; default:');
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed broken syntax in: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDirectory(filePath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fixBrokenSyntax(filePath);
    }
  });
}

// Start processing from src directory
const srcDir = path.join(__dirname, 'src');
if (fs.existsSync(srcDir)) {
  processDirectory(srcDir);
  console.log('Finished fixing broken syntax');
} else {
  console.error('src directory not found');
}
