#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixModalCloseButtons(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Find Modal components that have onClose but no withCloseButton
    const modalPattern = /<Modal\s+([^>]*?)>/g;
    let match;
    
    while ((match = modalPattern.exec(content)) !== null) {
      const modalProps = match[1];
      
      // Check if this Modal has onClose but not withCloseButton
      if (modalProps.includes('onClose') && !modalProps.includes('withCloseButton')) {
        // Replace the Modal opening tag to include withCloseButton
        const originalModal = match[0];
        const newModal = originalModal.replace('>', '\n        withCloseButton\n      >');
        
        content = content.replace(originalModal, newModal);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed Modal close buttons in: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDirectory(filePath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fixModalCloseButtons(filePath);
    }
  });
}

// Start processing from src directory
const srcDir = path.join(__dirname, 'src');
if (fs.existsSync(srcDir)) {
  console.log('🔧 Fixing Modal close buttons...');
  processDirectory(srcDir);
  console.log('✅ Finished fixing Modal close buttons');
} else {
  console.error('❌ src directory not found');
}
