// Universal fix for MantineonChange type issues
export const createChangeHandler = (setActiveTab: (value: string) => void) => {
  return (value: string | null) => {
    if (value !== null) {
      setActiveTab(value)
    }
  }
}

// Alternative: Direct inline fix
export const handleChange = (setActiveTab: (value: string) => void) => (value: string | null) => {
  if (value !== null) {
    setActiveTab(value)
  }
}
