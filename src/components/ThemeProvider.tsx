import React, { useEffect, useState } from 'react'
import { MantineProvider, MantineColorScheme, useMantineColorScheme, MantineColorsTuple } from '@mantine/core'
import { useAppStore } from '../store/useAppStore'
import { theme } from '../theme/theme'

// Function to generate color palette from a base color
function generateColorPalette(baseColor: string): MantineColorsTuple {
  // Convert hex to RGB
  const hex = baseColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)

  // Generate lighter and darker shades
  const generateShade = (factor: number) => {
    const newR = Math.round(r + (255 - r) * factor)
    const newG = Math.round(g + (255 - g) * factor)
    const newB = Math.round(b + (255 - b) * factor)
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
  }

  const generateDarkShade = (factor: number) => {
    const newR = Math.round(r * (1 - factor))
    const newG = Math.round(g * (1 - factor))
    const newB = Math.round(b * (1 - factor))
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
  }

  return [
    generateShade(0.9),   // Lightest tint
    generateShade(0.8),   // Very light tint
    generateShade(0.6),   // Light tint
    generateShade(0.4),   // Light tint
    generateShade(0.2),   // Medium light tint
    generateShade(0.1),   // Medium tint
    baseColor,            // Primary color (index 6)
    generateDarkShade(0.1), // Slightly darker
    generateDarkShade(0.2), // Darker
    generateDarkShade(0.3), // Darkest shade
  ]
}

interface ThemeProviderProps {
  children: React.ReactNode
}

function ThemeController() {
  const { settings } = useAppStore()
  const { setColorScheme } = useMantineColorScheme()

  useEffect(() => {
    if (settings?.theme) {
      const colorScheme: MantineColorScheme = settings.theme === 'light' ? 'light' :
                                             settings.theme === 'auto' ? 'auto' : 'dark'
      setColorScheme(colorScheme)
      console.log('Theme changed to:', colorScheme) // Debug log
    }
  }, [settings?.theme, setColorScheme])

  // Log primary color changes
  useEffect(() => {
    if (settings?.primaryColor) {
      console.log('Primary color changed to:', settings.primaryColor) // Debug log
    }
  }, [settings?.primaryColor])

  return null
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { settings } = useAppStore()

  // Get the initial theme from settings, default to 'dark'
  const initialColorScheme: MantineColorScheme = settings?.theme === 'light' ? 'light' :
                                                 settings?.theme === 'auto' ? 'auto' : 'dark'

  // Generate dynamic brand colors based on user's primary color
  const primaryColor = settings?.primaryColor || '#1e5c7a'
  const brandColors = generateColorPalette(primaryColor)

  console.log('Primary color from settings:', primaryColor) // Debug log
  console.log('Generated brand colors:', brandColors) // Debug log

  // Update the theme with the dynamic primary color
  const dynamicTheme = {
    ...theme,
    primaryColor: 'brand', // Keep using our brand colors
    colors: {
      ...theme.colors,
      brand: brandColors, // Use dynamically generated colors
    },
    other: {
      ...theme.other,
      brandPrimary: primaryColor,
      brandSecondary: brandColors[8], // Use darker shade as secondary
      brandLight: brandColors[0], // Use lightest shade
      brandText: brandColors[8] // Use darker shade for text
    }
  }

  return (
    <MantineProvider
      key={primaryColor} // Force re-render when primary color changes
      theme={dynamicTheme}
      defaultColorScheme={initialColorScheme}
    >
      <ThemeController />
      {children}
    </MantineProvider>
  )
}
