import React, { useEffect, useState } from 'react'
import { MantineProvider, MantineColorScheme, useMantineColorScheme } from '@mantine/core'
import { useAppStore } from '../store/useAppStore'
import { theme } from '../theme/theme'

interface ThemeProviderProps {
  children: React.ReactNode
}

function ThemeController() {
  const { settings } = useAppStore()
  const { setColorScheme } = useMantineColorScheme()

  useEffect(() => {
    if (settings?.theme) {
      const colorScheme: MantineColorScheme = settings.theme === 'light' ? 'light' :
                                             settings.theme === 'auto' ? 'auto' : 'dark'
      setColorScheme(colorScheme)
      console.log('Theme changed to:', colorScheme) // Debug log
    }
  }, [settings?.theme, setColorScheme])

  return null
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { settings } = useAppStore()

  // Get the initial theme from settings, default to 'dark'
  const initialColorScheme: MantineColorScheme = settings?.theme === 'light' ? 'light' :
                                                 settings?.theme === 'auto' ? 'auto' : 'dark'

  // Update the theme with the primary color from settings if available
  const dynamicTheme = {
    ...theme,
    primaryColor: 'brand', // Keep using our brand colors
    colors: {
      ...theme.colors,
      // You can add dynamic color updates here if needed
    },
    other: {
      ...theme.other,
      brandPrimary: settings?.primaryColor || '#1e5c7a',
      brandSecondary: '#164758',
      brandLight: '#e8f4f8',
      brandText: '#164758'
    }
  }

  return (
    <MantineProvider theme={dynamicTheme} defaultColorScheme={initialColorScheme}>
      <ThemeController />
      {children}
    </MantineProvider>
  )
}
