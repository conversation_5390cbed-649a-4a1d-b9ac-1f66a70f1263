import { Container, Loader, Stack, Text } from '@mantine/core'

interface LoadingStateProps {
  message?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

export function LoadingState({ message = 'Loading...', size = 'md' }: LoadingStateProps) {
  return (
    <Container size="sm" py="xl">
      <Stack align="center" gap="md">
        <Loader size={size} />
        <Text size="sm" c="dimmed">
          {message}
        </Text>
      </Stack>
    </Container>
  )
}
