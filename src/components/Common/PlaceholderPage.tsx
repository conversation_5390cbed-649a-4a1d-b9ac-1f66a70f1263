import { Container, Title, Text, Card, Group, Button, Stack, <PERSON><PERSON>, Badge, Grid } from '@mantine/core'
import { IconTool, IconPlus, IconSettings, IconDownload, IconRefresh } from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface PlaceholderPageProps {
  title: string
  description: string
  icon?: React.ReactNode
  features?: string[]
  status?: 'coming_soon' | 'in_development' | 'planned'
  estimatedCompletion?: string
}

export function PlaceholderPage({
  title,
  description,
  icon = <IconTool size={24} />,
  features = [],
  status = 'in_development',
  estimatedCompletion
}: PlaceholderPageProps) {
  const { t } = useTranslation()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'coming_soon': return 'blue'
      case 'in_development': return 'orange'
      case 'planned': return 'gray'
      default: return 'gray'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'coming_soon': return t('comingSoon')
      case 'in_development': return t('inDevelopment')
      case 'planned': return t('planned')
      default: return t('inDevelopment')
    }
  }

  return (
    <Container size="lg" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Group gap="sm">
              {icon}
              <div>
                <Title order={2}>{title}</Title>
                <Text c="dimmed">{description}</Text>
              </div>
            </Group>
          </div>
          <Badge color={getStatusColor(status)} size="lg">
            {getStatusText(status)}
          </Badge>
        </Group>

        {/* Status Alert */}
        <Alert
          icon={<IconTool size={16} />}
          title={t('pageUnderDevelopment')}
          color={getStatusColor(status)}
        >
          <Text size="sm">
            {t('thisPageIsCurrentlyBeingDeveloped')} {estimatedCompletion && t('estimatedCompletion', { date: estimatedCompletion })}
          </Text>
        </Alert>

        {/* Planned Features */}
        {features.length > 0 && (
          <Card withBorder>
            <Title order={4} mb="md">{t('plannedFeatures')}</Title>
            <Grid>
              {features.map((feature, index) => (
                <Grid.Col span={6} key={index}>
                  <Group gap="xs">
                    <IconSettings size={16} color="gray" />
                    <Text size="sm">{feature}</Text>
                  </Group>
                </Grid.Col>
              ))}
            </Grid>
          </Card>
        )}

        {/* Mock Action Buttons */}
        <Card withBorder>
          <Title order={4} mb="md">{t('availableActions')}</Title>
          <Group>
            <Button leftSection={<IconPlus size={16} />} disabled>
              {t('add')} {title}
            </Button>
            <Button leftSection={<IconDownload size={16} />} variant="light" disabled>
              {t('export')}
            </Button>
            <Button leftSection={<IconSettings size={16} />} variant="light" disabled>
              {t('settings')}
            </Button>
            <Button leftSection={<IconRefresh size={16} />} variant="light" disabled>
              {t('refresh')}
            </Button>
          </Group>
          <Text size="xs" c="dimmed" mt="sm">
            {t('buttonsWillBeEnabledWhenPageIsComplete')}
          </Text>
        </Card>

        {/* Development Info */}
        <Card withBorder bg="gray.0">
          <Title order={5} mb="sm">{t('developmentInfo')}</Title>
          <Stack gap="xs">
            <Group justify="space-between">
              <Text size="sm" c="dimmed">{t('status')}:</Text>
              <Badge color={getStatusColor(status)} size="sm">
                {getStatusText(status)}
              </Badge>
            </Group>
            {estimatedCompletion && (
              <Group justify="space-between">
                <Text size="sm" c="dimmed">{t('estimatedCompletion')}:</Text>
                <Text size="sm">{estimatedCompletion}</Text>
              </Group>
            )}
            <Group justify="space-between">
              <Text size="sm" c="dimmed">{t('priority')}:</Text>
              <Text size="sm">{t('medium')}</Text>
            </Group>
          </Stack>
        </Card>

        {/* Contact Support */}
        <Alert color="blue" title={t('needThisFeature')}>
          <Text size="sm">
            {t('ifYouNeedThisFeatureUrgently')} {t('pleaseContactSupport')}
          </Text>
          <Button size="xs" mt="sm" variant="light">
            {t('contactSupport')}
          </Button>
        </Alert>
      </Stack>
    </Container>
  )
}
