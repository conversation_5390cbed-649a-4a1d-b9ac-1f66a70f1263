import { ReactNode } from 'react'
import { Group, Title, Text, Stack, Box } from '@mantine/core'

interface PageHeaderProps {
  title: string
  description?: string
  actions?: ReactNode
  breadcrumbs?: ReactNode
  size?: 'sm' | 'md' | 'lg'
}

export function PageHeader({ 
  title, 
  description, 
  actions, 
  breadcrumbs,
  size = 'md' 
}: PageHeaderProps) {
  const titleOrder = size === 'lg' ? 1 : size === 'sm' ? 3 : 2
  const spacing = size === 'lg' ? 'xl' : size === 'sm' ? 'md' : 'lg'

  return (
    <Box mb={spacing}>
      {breadcrumbs && (
        <Box mb="xs">
          {breadcrumbs}
        </Box>
      )}
      
      <Group justify="space-between" align="flex-start" wrap="nowrap">
        <Stack gap="xs" style={{ flex: 1, minWidth: 0 }}>
          <Title 
            order={titleOrder}
            size={size === 'lg' ? 'h1' : size === 'sm' ? 'h3' : 'h2'}
            style={{ 
              lineHeight: 1.2,
              letterSpacing: '-0.025em',
              fontWeight: 600
            }}
          >
            {title}
          </Title>
          {description && (
            <Text 
              c="dimmed" 
              size={size === 'lg' ? 'md' : 'sm'}
              style={{ 
                maxWidth: '600px',
                lineHeight: 1.4
              }}
            >
              {description}
            </Text>
          )}
        </Stack>
        
        {actions && (
          <Box style={{ flexShrink: 0, marginLeft: 'var(--mantine-spacing-md)' }}>
            {actions}
          </Box>
        )}
      </Group>
    </Box>
  )
}
