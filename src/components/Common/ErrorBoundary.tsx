import { Component, ErrorInfo, ReactNode } from 'react'
import { Container, Text, Button, Alert, Stack } from '@mantine/core'
import { IconAlertTriangle, IconRefresh } from '@tabler/icons-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Container size="sm" py="xl">
          <Stack align="center" gap="lg">
            <Alert 
              icon={<IconAlertTriangle size={24} />} 
              color="red" 
              title="Something went wrong"
              variant="light"
            >
              <Text size="sm">
                An error occurred while loading this page. This might be due to a temporary issue.
              </Text>
              {this.state.error && (
                <Text size="xs" c="dimmed" mt="xs">
                  Error: {this.state.error.message}
                </Text>
              )}
            </Alert>

            <Button 
              leftSection={<IconRefresh size={16} />}
              onClick={this.handleRetry}
              variant="light"
            >
              Try Again
            </Button>

            <Button 
              variant="subtle" 
              onClick={() => window.location.reload()}
            >
              Reload Page
            </Button>
          </Stack>
        </Container>
      )
    }

    return this.props.children
  }
}
