import { Group, Text, Badge, Box, useMantineColorScheme } from '@mantine/core'

interface LogoProps {
  size?: 'sm' | 'md' | 'lg'
  showBadge?: boolean
}

export function Logo({ size = 'md', showBadge = true }: LogoProps) {
  const logoSize = size === 'lg' ? 'xl' : size === 'sm' ? 'lg' : 'xl'
  const badgeSize = size === 'lg' ? 'sm' : 'xs'
  const { colorScheme } = useMantineColorScheme()
  const isDark = colorScheme === 'dark'

  return (
    <Box
      style={{
        border: `0.5px solid ${isDark ? 'var(--mantine-color-dark-4)' : 'var(--mantine-color-gray-4)'}`,
        borderRadius: '6px',
        padding: '6px 10px',
        backgroundColor: 'transparent',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        paddingRight: '16px',
        paddingLeft: '8px'
      }}
    >
      <Group gap="xs" align="center" justify="center">
        <Text
          size={logoSize}
          fw={700}
          className="carvio-logo"
          style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
            letterSpacing: '-0.02em',
            margin: 0,
            lineHeight: 1
          }}
        >
          Carvio
        </Text>

        {showBadge && (
          <Badge
            size={badgeSize}
            variant="gradient"
            gradient={{ from: 'blue', to: 'cyan', deg: 45 }}
            style={{
              textTransform: 'uppercase',
              fontWeight: 600,
              letterSpacing: '0.5px',
              margin: 0
            }}
          >
            Pro
          </Badge>
        )}
      </Group>
    </Box>
  )
}
