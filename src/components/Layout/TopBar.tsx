import { Group, Text, Badge, useMantineColorScheme } from '@mantine/core'
import { useTranslation } from 'react-i18next'

export function TopBar() {
  const { t } = useTranslation()
  const { colorScheme } = useMantineColorScheme()
  const isDark = colorScheme === 'dark'

  return (
    <div
      className="top-bar"
      style={{
        height: '32px',
        background: isDark ? 'var(--mantine-color-dark-7)' : 'var(--mantine-color-gray-1)',
        borderBottom: isDark
          ? '1px solid var(--mantine-color-dark-5)'
          : '1px solid var(--mantine-color-gray-3)',
        display: 'flex',
        alignItems: 'center',
        paddingLeft: '80px', // Space for macOS window controls
        paddingRight: '16px',
        WebkitAppRegion: 'drag' as any,
        fontSize: '12px',
        position: 'relative',
        zIndex: 1000
      }}
    >
      <Group justify="center" style={{ width: '100%', height: '100%' }}>
        {/* Center - Welcome badge */}
        <Badge
          size="xs"
          variant="light"
          color="blue"
          style={{
            textTransform: 'uppercase',
            fontWeight: 600,
            letterSpacing: '0.5px',
            WebkitAppRegion: 'no-drag' as any
          }}
        >
          WELCOME TO CARVIO
        </Badge>
      </Group>
    </div>
  )
}
