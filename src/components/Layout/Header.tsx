import {
  Group,
  Burger,
  ActionIcon,
  Menu,
  Avatar,
  Indicator,
  Tooltip,
  useMantineColorScheme
} from '@mantine/core'
import { 
  IconSun, 
  IconMoon, 
  IconBell, 
  IconUser, 
  IconSettings, 
  IconLogout,
  IconSearch,
  IconLanguage
} from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Logo } from './Logo'

interface HeaderProps {
  opened: boolean
  toggle: () => void
  colorScheme: 'light' | 'dark'
  toggleColorScheme: () => void
}

export function Header({ opened, toggle, colorScheme, toggleColorScheme }: HeaderProps) {
  const { t, i18n } = useTranslation()
  const [notifications] = useState(3) // Mock notification count
  const { setColorScheme } = useMantineColorScheme()

  const handleColorSchemeToggle = () => {
    const newScheme = colorScheme === 'light' ? 'dark' : 'light'
    setColorScheme(newScheme)
    toggleColorScheme()
  }

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
  }

  return (
    <Group
      h="100%"
      px="md"
      justify="space-between"
      align="center"
      style={{
        paddingLeft: '16px', // Reset to normal padding since TopBar handles macOS controls
        width: '100%'
      }}
    >
      {/* Left side - Logo and burger */}
      <Group align="center" style={{ flexShrink: 0 }}>
        <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
        <Logo size="md" showBadge={true} />
      </Group>

      {/* Right side - Actions */}
      <Group gap="sm" style={{ flexShrink: 0 }}>
        {/* Search */}
        <Tooltip label={t('search')}>
          <ActionIcon variant="subtle" size="lg">
            <IconSearch size={18} />
          </ActionIcon>
        </Tooltip>

        {/* Language Selector */}
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <Tooltip label={t('changeLanguage')}>
              <ActionIcon variant="subtle" size="lg">
                <IconLanguage size={18} />
              </ActionIcon>
            </Tooltip>
          </Menu.Target>

          <Menu.Dropdown>
            <Menu.Label>{t('selectLanguage')}</Menu.Label>
            <Menu.Item onClick={() => changeLanguage('en')}>
              🇺🇸 English
            </Menu.Item>
            <Menu.Item onClick={() => changeLanguage('ar')}>
              🇦🇪 العربية
            </Menu.Item>
            <Menu.Item onClick={() => changeLanguage('es')}>
              🇪🇸 Español
            </Menu.Item>
            <Menu.Item onClick={() => changeLanguage('fr')}>
              🇫🇷 Français
            </Menu.Item>
            <Menu.Item onClick={() => changeLanguage('de')}>
              🇩🇪 Deutsch
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>

        {/* Theme Toggle */}
        <Tooltip label={colorScheme === 'light' ? t('darkMode') : t('lightMode')}>
          <ActionIcon 
            variant="subtle" 
            size="lg"
            onClick={handleColorSchemeToggle}
          >
            {colorScheme === 'light' ? <IconMoon size={18} /> : <IconSun size={18} />}
          </ActionIcon>
        </Tooltip>

        {/* Notifications */}
        <Tooltip label={t('notifications')}>
          <Indicator inline label={notifications} size={16} disabled={notifications === 0}>
            <ActionIcon variant="subtle" size="lg">
              <IconBell size={18} />
            </ActionIcon>
          </Indicator>
        </Tooltip>

        {/* User Menu */}
        <Menu shadow="md" width={200}>
          <Menu.Target>
            <ActionIcon variant="subtle" size="lg">
              <Avatar size="sm" color="brand">
                <IconUser size={16} />
              </Avatar>
            </ActionIcon>
          </Menu.Target>

          <Menu.Dropdown>
            <Menu.Label>Admin User</Menu.Label>
            <Menu.Item leftSection={<IconUser size={14} />}>
              {t('profile')}
            </Menu.Item>
            <Menu.Item leftSection={<IconSettings size={14} />}>
              {t('settings')}
            </Menu.Item>
            <Menu.Divider />
            <Menu.Item
              leftSection={<IconLogout size={14} />}
              color="red"
            >
              {t('logout')}
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Group>
    </Group>
  )
}
