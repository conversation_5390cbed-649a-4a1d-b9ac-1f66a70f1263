import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Vehicle, Customer, Reservation, RentalContract, User, VehicleCategory, Location } from '../database/database'

// Additional data types for comprehensive app functionality
export interface Quote {
  id: string
  quote_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  vehicle_id: string
  vehicle_name: string
  plate_number: string
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired' | 'converted'
  pickup_date: string
  return_date: string
  pickup_location: string
  return_location: string
  total_days: number
  daily_rate: number
  subtotal: number
  discount_amount: number
  tax_amount: number
  total_amount: number
  valid_until: string
  quote_template: string
  special_offers: string
  terms_conditions: string
  notes: string
  sent_date?: string
  viewed_date?: string
  response_date?: string
  converted_to_reservation?: string
  created_by: string
  created_at: string
  updated_at: string
}

export interface BlacklistEntry {
  id: string
  blacklist_number: string
  customer_id: string
  customer_number: string
  customer_name: string
  customer_email: string
  customer_phone: string
  nationality: string
  country: string
  blacklist_reason: 'payment_default' | 'vehicle_damage' | 'fraudulent_activity' | 'policy_violation' | 'late_returns' | 'other'
  blacklist_type: 'temporary' | 'permanent' | 'warning'
  status: 'active' | 'expired' | 'lifted' | 'under_review'
  severity_level: 'low' | 'medium' | 'high' | 'critical'
  blacklist_date: string
  expiry_date?: string
  lifted_date?: string
  lifted_by?: string
  lift_reason?: string
  incident_details: string
  financial_impact: number
  evidence_documents: string[]
  previous_violations: number
  warning_count: number
  review_date?: string
  added_by: string
  last_updated_by: string
  created_at: string
  updated_at: string
}

export interface Inspection {
  id: string
  inspection_number: string
  vehicle_id: string
  vehicle_name: string
  vehicle_make: string
  vehicle_model: string
  vehicle_year: number
  plate_number: string
  vin_number: string
  inspection_type: 'pre_rental' | 'post_rental' | 'maintenance' | 'damage_assessment' | 'periodic' | 'insurance'
  inspection_date: string
  inspection_time: string
  scheduled_date: string
  location: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled' | 'requires_attention'
  priority: 'low' | 'medium' | 'high' | 'critical'
  inspector_id: string
  inspector_name: string
  customer_id?: string
  customer_name?: string
  reservation_id?: string
  mileage: number
  fuel_level: number
  exterior_condition: {
    overall_rating: number
    damages: Array<{
      type: string
      location: string
      severity: string
      description: string
      estimated_cost: number
      photos: string[]
    }>
  }
  interior_condition: {
    overall_rating: number
    cleanliness_score: number
    damages: Array<{
      type: string
      location: string
      severity: string
      description: string
      estimated_cost: number
      photos: string[]
    }>
  }
  mechanical_condition: {
    engine_status: boolean
    transmission_status: boolean
    brake_status: boolean
    tire_condition: string
    fluid_levels: {
      oil: string
      coolant: string
      brake_fluid: string
      windshield_washer: string
    }
    warning_lights: string[]
    performance_issues: string[]
  }
  safety_compliance: {
    seat_belts: boolean
    airbags: boolean
    fire_extinguisher: boolean
    first_aid_kit: boolean
    warning_triangle: boolean
    spare_tire: boolean
    jack_tools: boolean
    registration_documents: boolean
    insurance_documents: boolean
    compliance_score: number
  }
  checklist_items: Array<{
    category: string
    item: string
    status: 'pass' | 'fail' | 'na' | 'attention_required'
    notes?: string
    photos?: string[]
  }>
  total_estimated_cost: number
  recommendations: string[]
  next_inspection_due: string
  inspection_notes: string
  customer_signature?: string
  inspector_signature?: string
  completion_time?: string
  created_at: string
  updated_at: string
}

// Maintenance Record Interface
export interface MaintenanceRecord {
  id: string
  vehicle_id: string
  maintenance_type: 'service' | 'repair' | 'inspection'
  description: string
  scheduled_date: string
  completed_date?: string
  mileage?: number
  cost?: number
  service_provider?: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  notes?: string
  created_by?: string
  created_at: string
  updated_at: string
}

// Settings Interface
export interface AppSettings {
  // General Settings
  theme: 'light' | 'dark' | 'auto'
  language: string
  dateFormat: string
  timeFormat: '12h' | '24h'
  currency: string

  // UI Customization
  sidebarCollapsed: boolean
  showWelcomeMessage: boolean
  enableAnimations: boolean
  compactMode: boolean
  primaryColor: string

  // Performance Settings
  autoSave: boolean
  autoSaveInterval: number
  cacheEnabled: boolean
  maxRecordsPerPage: number

  // Security Options
  sessionTimeout: number
  requirePasswordChange: boolean
  enableAuditLog: boolean

  // Notifications
  desktopNotifications: boolean
  soundEnabled: boolean
  emailNotifications: boolean

  // System Behavior
  confirmDeletions: boolean
  autoBackup: boolean
  backupFrequency: 'hourly' | 'daily' | 'weekly' | 'monthly'
  debugMode: boolean

  // Currency Settings
  currencySettings?: {
    baseCurrency: string
    displayFormat: string
    decimalPlaces: number
    thousandSeparator: string
    decimalSeparator: string
    currencyPosition: 'before' | 'after'
    exchangeRates: { [key: string]: number }
  }

  // Company Profile
  companyProfile?: {
    company_name: string
    business_registration: string
    tax_number: string
    address: string
    phone: string
    email: string
    website: string
    description: string
    established_year: string
    license_number: string
    timezone: string
    enableOnlineBooking: boolean
    requireDepositForBooking: boolean
    enableMultiLocation: boolean
  }
}

interface AppState {
  // Authentication
  currentUser: User | null
  isAuthenticated: boolean
  
  // Application Settings
  settings: AppSettings
  theme: 'light' | 'dark'
  language: string
  sidebarCollapsed: boolean
  
  // Data
  vehicles: Vehicle[]
  customers: Customer[]
  reservations: Reservation[]
  contracts: RentalContract[]
  categories: VehicleCategory[]
  locations: Location[]
  quotes: Quote[]
  blacklistEntries: BlacklistEntry[]
  inspections: Inspection[]
  maintenanceRecords: MaintenanceRecord[]
  
  // UI State
  loading: boolean
  selectedVehicle: Vehicle | null
  selectedCustomer: Customer | null
  selectedReservation: Reservation | null
  
  // Dashboard Stats
  stats: {
    totalVehicles: number
    availableVehicles: number
    activeRentals: number
    monthlyRevenue: number
    pendingReservations: number
    overdueReturns: number
    maintenanceDue: number
  }
  
  // Actions
  setCurrentUser: (user: User | null) => void
  setAuthenticated: (authenticated: boolean) => void
  setTheme: (theme: 'light' | 'dark') => void
  setLanguage: (language: string) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setLoading: (loading: boolean) => void

  // Settings Actions
  updateSettings: (settings: Partial<AppSettings>) => void

  // Data Management Actions
  setQuotes: (quotes: Quote[]) => void
  addQuote: (quote: Quote) => void
  updateQuote: (id: string, updates: Partial<Quote>) => void
  deleteQuote: (id: string) => void

  setBlacklistEntries: (entries: BlacklistEntry[]) => void
  addBlacklistEntry: (entry: BlacklistEntry) => void
  updateBlacklistEntry: (id: string, updates: Partial<BlacklistEntry>) => void
  deleteBlacklistEntry: (id: string) => void

  setInspections: (inspections: Inspection[]) => void
  addInspection: (inspection: Inspection) => void
  updateInspection: (id: string, updates: Partial<Inspection>) => void
  deleteInspection: (id: string) => void
  
  // Data Actions
  setVehicles: (vehicles: Vehicle[]) => void
  addVehicle: (vehicle: Vehicle) => void
  updateVehicle: (id: string, updates: Partial<Vehicle>) => void
  removeVehicle: (id: string) => void
  setSelectedVehicle: (vehicle: Vehicle | null) => void
  
  setCustomers: (customers: Customer[]) => void
  addCustomer: (customer: Customer) => void
  updateCustomer: (id: string, updates: Partial<Customer>) => void
  removeCustomer: (id: string) => void
  setSelectedCustomer: (customer: Customer | null) => void
  
  setReservations: (reservations: Reservation[]) => void
  addReservation: (reservation: Reservation) => void
  updateReservation: (id: string, updates: Partial<Reservation>) => void
  removeReservation: (id: string) => void
  setSelectedReservation: (reservation: Reservation | null) => void
  
  setContracts: (contracts: RentalContract[]) => void
  addContract: (contract: RentalContract) => void
  updateContract: (id: string, updates: Partial<RentalContract>) => void
  
  setCategories: (categories: VehicleCategory[]) => void
  setLocations: (locations: Location[]) => void
  setMaintenanceRecords: (records: MaintenanceRecord[]) => void

  updateStats: (stats: Partial<AppState['stats']>) => void
  
  // Utility Actions
  reset: () => void
}

const initialStats = {
  totalVehicles: 0,
  availableVehicles: 0,
  activeRentals: 0,
  monthlyRevenue: 0,
  pendingReservations: 0,
  overdueReturns: 0,
  maintenanceDue: 0
}

const initialSettings: AppSettings = {
  // General Settings
  theme: 'dark',
  language: 'en',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h',
  currency: 'AED',

  // UI Customization
  sidebarCollapsed: false,
  showWelcomeMessage: true,
  enableAnimations: true,
  compactMode: false,
  primaryColor: '#1e5c7a',

  // Performance Settings
  autoSave: true,
  autoSaveInterval: 30,
  cacheEnabled: true,
  maxRecordsPerPage: 50,

  // Security Options
  sessionTimeout: 60,
  requirePasswordChange: false,
  enableAuditLog: true,

  // Notifications
  desktopNotifications: true,
  soundEnabled: true,
  emailNotifications: true,

  // System Behavior
  confirmDeletions: true,
  autoBackup: true,
  backupFrequency: 'daily',
  debugMode: false
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      // Initial State
      currentUser: null,
      isAuthenticated: false,
      settings: initialSettings,
      theme: 'dark',
      language: 'en',
      sidebarCollapsed: false,
      
      vehicles: [],
      customers: [],
      reservations: [],
      contracts: [],
      categories: [],
      locations: [],
      quotes: [],
      blacklistEntries: [],
      inspections: [],
      maintenanceRecords: [],
      
      loading: false,
      selectedVehicle: null,
      selectedCustomer: null,
      selectedReservation: null,
      
      stats: initialStats,
      
      // Actions
      setCurrentUser: (user) => set({ currentUser: user }),
      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),
      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setLoading: (loading) => set({ loading }),

      // Settings Actions
      updateSettings: (newSettings) => set((state) => ({
        settings: { ...state.settings, ...newSettings }
      })),
      
      // Vehicle Actions
      setVehicles: (vehicles) => set({ vehicles }),
      addVehicle: (vehicle) => set((state) => ({ 
        vehicles: [...state.vehicles, vehicle] 
      })),
      updateVehicle: (id, updates) => set((state) => ({
        vehicles: state.vehicles.map(v => v.id === id ? { ...v, ...updates } : v)
      })),
      removeVehicle: (id) => set((state) => ({
        vehicles: state.vehicles.filter(v => v.id !== id)
      })),
      setSelectedVehicle: (vehicle) => set({ selectedVehicle: vehicle }),
      
      // Customer Actions
      setCustomers: (customers) => set({ customers }),
      addCustomer: (customer) => set((state) => ({ 
        customers: [...state.customers, customer] 
      })),
      updateCustomer: (id, updates) => set((state) => ({
        customers: state.customers.map(c => c.id === id ? { ...c, ...updates } : c)
      })),
      removeCustomer: (id) => set((state) => ({
        customers: state.customers.filter(c => c.id !== id)
      })),
      setSelectedCustomer: (customer) => set({ selectedCustomer: customer }),
      
      // Reservation Actions
      setReservations: (reservations) => set({ reservations }),
      addReservation: (reservation) => set((state) => ({ 
        reservations: [...state.reservations, reservation] 
      })),
      updateReservation: (id, updates) => set((state) => ({
        reservations: state.reservations.map(r => r.id === id ? { ...r, ...updates } : r)
      })),
      removeReservation: (id) => set((state) => ({
        reservations: state.reservations.filter(r => r.id !== id)
      })),
      setSelectedReservation: (reservation) => set({ selectedReservation: reservation }),
      
      // Contract Actions
      setContracts: (contracts) => set({ contracts }),
      addContract: (contract) => set((state) => ({ 
        contracts: [...state.contracts, contract] 
      })),
      updateContract: (id, updates) => set((state) => ({
        contracts: state.contracts.map(c => c.id === id ? { ...c, ...updates } : c)
      })),
      
      // Other Data Actions
      setCategories: (categories) => set({ categories }),
      setLocations: (locations) => set({ locations }),
      setMaintenanceRecords: (maintenanceRecords) => set({ maintenanceRecords }),

      // Quote Management
      setQuotes: (quotes) => set({ quotes }),
      addQuote: (quote) => set((state) => ({ quotes: [...state.quotes, quote] })),
      updateQuote: (id, updates) => set((state) => ({
        quotes: state.quotes.map(quote => quote.id === id ? { ...quote, ...updates } : quote)
      })),
      deleteQuote: (id) => set((state) => ({
        quotes: state.quotes.filter(quote => quote.id !== id)
      })),

      // Blacklist Management
      setBlacklistEntries: (entries) => set({ blacklistEntries: entries }),
      addBlacklistEntry: (entry) => set((state) => ({ blacklistEntries: [...state.blacklistEntries, entry] })),
      updateBlacklistEntry: (id, updates) => set((state) => ({
        blacklistEntries: state.blacklistEntries.map(entry => entry.id === id ? { ...entry, ...updates } : entry)
      })),
      deleteBlacklistEntry: (id) => set((state) => ({
        blacklistEntries: state.blacklistEntries.filter(entry => entry.id !== id)
      })),

      // Inspection Management
      setInspections: (inspections) => set({ inspections }),
      addInspection: (inspection) => set((state) => ({ inspections: [...state.inspections, inspection] })),
      updateInspection: (id, updates) => set((state) => ({
        inspections: state.inspections.map(inspection => inspection.id === id ? { ...inspection, ...updates } : inspection)
      })),
      deleteInspection: (id) => set((state) => ({
        inspections: state.inspections.filter(inspection => inspection.id !== id)
      })),

      updateStats: (newStats) => set((state) => ({
        stats: { ...state.stats, ...newStats }
      })),
      
      // Utility Actions
      reset: () => set({
        currentUser: null,
        isAuthenticated: false,
        vehicles: [],
        customers: [],
        reservations: [],
        contracts: [],
        categories: [],
        locations: [],
        maintenanceRecords: [],
        quotes: [],
        blacklistEntries: [],
        inspections: [],
        selectedVehicle: null,
        selectedCustomer: null,
        selectedReservation: null,
        stats: initialStats,
        loading: false
      })
    }),
    {
      name: 'carvio-app-store',
      partialize: (state) => ({
        settings: state.settings,
        theme: state.theme,
        language: state.language,
        sidebarCollapsed: state.sidebarCollapsed,
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// Computed selectors
export const useVehicleStats = () => {
  const vehicles = useAppStore(state => state.vehicles)
  
  return {
    total: vehicles.length,
    available: vehicles.filter(v => v.status === 'available').length,
    rented: vehicles.filter(v => v.status === 'rented').length,
    maintenance: vehicles.filter(v => v.status === 'maintenance').length,
    reserved: vehicles.filter(v => v.status === 'reserved').length,
    outOfService: vehicles.filter(v => v.status === 'out_of_service').length
  }
}

export const useReservationStats = () => {
  const reservations = useAppStore(state => state.reservations)
  
  return {
    total: reservations.length,
    pending: reservations.filter(r => r.status === 'pending').length,
    confirmed: reservations.filter(r => r.status === 'confirmed').length,
    active: reservations.filter(r => r.status === 'active').length,
    completed: reservations.filter(r => r.status === 'completed').length,
    cancelled: reservations.filter(r => r.status === 'cancelled').length
  }
}

export const useContractStats = () => {
  const contracts = useAppStore(state => state.contracts)
  
  return {
    total: contracts.length,
    active: contracts.filter(c => c.status === 'active').length,
    completed: contracts.filter(c => c.status === 'completed').length,
    cancelled: contracts.filter(c => c.status === 'cancelled').length
  }
}
