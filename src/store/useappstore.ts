import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Vehicle, Customer, Reservation, RentalContract, User, VehicleCategory, Location } from '../database/database'

// Maintenance Record Interface
export interface MaintenanceRecord {
  id: string
  vehicle_id: string
  maintenance_type: 'service' | 'repair' | 'inspection'
  description: string
  scheduled_date: string
  completed_date?: string
  mileage?: number
  cost?: number
  service_provider?: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  notes?: string
  created_by?: string
  created_at: string
  updated_at: string
}

interface AppState {
  // Authentication
  currentUser: User | null
  isAuthenticated: boolean
  
  // Application Settings
  theme: 'light' | 'dark'
  language: string
  sidebarCollapsed: boolean
  
  // Data
  vehicles: Vehicle[]
  customers: Customer[]
  reservations: Reservation[]
  contracts: RentalContract[]
  categories: VehicleCategory[]
  locations: Location[]
  maintenanceRecords: MaintenanceRecord[]
  
  // UI State
  loading: boolean
  selectedVehicle: Vehicle | null
  selectedCustomer: Customer | null
  selectedReservation: Reservation | null
  
  // Dashboard Stats
  stats: {
    totalVehicles: number
    availableVehicles: number
    activeRentals: number
    monthlyRevenue: number
    pendingReservations: number
    overdueReturns: number
    maintenanceDue: number
  }
  
  // Actions
  setCurrentUser: (user: User | null) => void
  setAuthenticated: (authenticated: boolean) => void
  setTheme: (theme: 'light' | 'dark') => void
  setLanguage: (language: string) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setLoading: (loading: boolean) => void
  
  // Data Actions
  setVehicles: (vehicles: Vehicle[]) => void
  addVehicle: (vehicle: Vehicle) => void
  updateVehicle: (id: string, updates: Partial<Vehicle>) => void
  removeVehicle: (id: string) => void
  setSelectedVehicle: (vehicle: Vehicle | null) => void
  
  setCustomers: (customers: Customer[]) => void
  addCustomer: (customer: Customer) => void
  updateCustomer: (id: string, updates: Partial<Customer>) => void
  removeCustomer: (id: string) => void
  setSelectedCustomer: (customer: Customer | null) => void
  
  setReservations: (reservations: Reservation[]) => void
  addReservation: (reservation: Reservation) => void
  updateReservation: (id: string, updates: Partial<Reservation>) => void
  removeReservation: (id: string) => void
  setSelectedReservation: (reservation: Reservation | null) => void
  
  setContracts: (contracts: RentalContract[]) => void
  addContract: (contract: RentalContract) => void
  updateContract: (id: string, updates: Partial<RentalContract>) => void
  
  setCategories: (categories: VehicleCategory[]) => void
  setLocations: (locations: Location[]) => void
  setMaintenanceRecords: (records: MaintenanceRecord[]) => void

  updateStats: (stats: Partial<AppState['stats']>) => void
  
  // Utility Actions
  reset: () => void
}

const initialStats = {
  totalVehicles: 0,
  availableVehicles: 0,
  activeRentals: 0,
  monthlyRevenue: 0,
  pendingReservations: 0,
  overdueReturns: 0,
  maintenanceDue: 0
}

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      // Initial State
      currentUser: null,
      isAuthenticated: false,
      theme: 'dark',
      language: 'en',
      sidebarCollapsed: false,
      
      vehicles: [],
      customers: [],
      reservations: [],
      contracts: [],
      categories: [],
      locations: [],
      maintenanceRecords: [],
      
      loading: false,
      selectedVehicle: null,
      selectedCustomer: null,
      selectedReservation: null,
      
      stats: initialStats,
      
      // Actions
      setCurrentUser: (user) => set({ currentUser: user }),
      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),
      setTheme: (theme) => set({ theme }),
      setLanguage: (language) => set({ language }),
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setLoading: (loading) => set({ loading }),
      
      // Vehicle Actions
      setVehicles: (vehicles) => set({ vehicles }),
      addVehicle: (vehicle) => set((state) => ({ 
        vehicles: [...state.vehicles, vehicle] 
      })),
      updateVehicle: (id, updates) => set((state) => ({
        vehicles: state.vehicles.map(v => v.id === id ? { ...v, ...updates } : v)
      })),
      removeVehicle: (id) => set((state) => ({
        vehicles: state.vehicles.filter(v => v.id !== id)
      })),
      setSelectedVehicle: (vehicle) => set({ selectedVehicle: vehicle }),
      
      // Customer Actions
      setCustomers: (customers) => set({ customers }),
      addCustomer: (customer) => set((state) => ({ 
        customers: [...state.customers, customer] 
      })),
      updateCustomer: (id, updates) => set((state) => ({
        customers: state.customers.map(c => c.id === id ? { ...c, ...updates } : c)
      })),
      removeCustomer: (id) => set((state) => ({
        customers: state.customers.filter(c => c.id !== id)
      })),
      setSelectedCustomer: (customer) => set({ selectedCustomer: customer }),
      
      // Reservation Actions
      setReservations: (reservations) => set({ reservations }),
      addReservation: (reservation) => set((state) => ({ 
        reservations: [...state.reservations, reservation] 
      })),
      updateReservation: (id, updates) => set((state) => ({
        reservations: state.reservations.map(r => r.id === id ? { ...r, ...updates } : r)
      })),
      removeReservation: (id) => set((state) => ({
        reservations: state.reservations.filter(r => r.id !== id)
      })),
      setSelectedReservation: (reservation) => set({ selectedReservation: reservation }),
      
      // Contract Actions
      setContracts: (contracts) => set({ contracts }),
      addContract: (contract) => set((state) => ({ 
        contracts: [...state.contracts, contract] 
      })),
      updateContract: (id, updates) => set((state) => ({
        contracts: state.contracts.map(c => c.id === id ? { ...c, ...updates } : c)
      })),
      
      // Other Data Actions
      setCategories: (categories) => set({ categories }),
      setLocations: (locations) => set({ locations }),
      setMaintenanceRecords: (maintenanceRecords) => set({ maintenanceRecords }),

      updateStats: (newStats) => set((state) => ({
        stats: { ...state.stats, ...newStats }
      })),
      
      // Utility Actions
      reset: () => set({
        currentUser: null,
        isAuthenticated: false,
        vehicles: [],
        customers: [],
        reservations: [],
        contracts: [],
        categories: [],
        locations: [],
        maintenanceRecords: [],
        selectedVehicle: null,
        selectedCustomer: null,
        selectedReservation: null,
        stats: initialStats,
        loading: false
      })
    }),
    {
      name: 'carvio-app-store',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebarCollapsed: state.sidebarCollapsed,
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// Computed selectors
export const useVehicleStats = () => {
  const vehicles = useAppStore(state => state.vehicles)
  
  return {
    total: vehicles.length,
    available: vehicles.filter(v => v.status === 'available').length,
    rented: vehicles.filter(v => v.status === 'rented').length,
    maintenance: vehicles.filter(v => v.status === 'maintenance').length,
    reserved: vehicles.filter(v => v.status === 'reserved').length,
    outOfService: vehicles.filter(v => v.status === 'out_of_service').length
  }
}

export const useReservationStats = () => {
  const reservations = useAppStore(state => state.reservations)
  
  return {
    total: reservations.length,
    pending: reservations.filter(r => r.status === 'pending').length,
    confirmed: reservations.filter(r => r.status === 'confirmed').length,
    active: reservations.filter(r => r.status === 'active').length,
    completed: reservations.filter(r => r.status === 'completed').length,
    cancelled: reservations.filter(r => r.status === 'cancelled').length
  }
}

export const useContractStats = () => {
  const contracts = useAppStore(state => state.contracts)
  
  return {
    total: contracts.length,
    active: contracts.filter(c => c.status === 'active').length,
    completed: contracts.filter(c => c.status === 'completed').length,
    cancelled: contracts.filter(c => c.status === 'cancelled').length
  }
}
