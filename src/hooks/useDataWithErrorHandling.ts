import { useState, useEffect } from 'react'

interface UseDataState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

interface UseDataOptions {
  retryCount?: number
  retryDelay?: number
}

export function useDataWithErrorHandling<T>(
  fetchFunction: () => Promise<T> | T,
  dependencies: any[] = [],
  options: UseDataOptions = {}
) {
  const { retryCount = 3, retryDelay = 1000 } = options
  
  const [state, setState] = useState<UseDataState<T>>({
    data: null,
    loading: true,
    error: null
  })

  const [currentRetry, setCurrentRetry] = useState(0)

  useEffect(() => {
    let isMounted = true
    
    const fetchData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }))
        
        const result = await Promise.resolve(fetchFunction())
        
        if (isMounted) {
          setState({
            data: result,
            loading: false,
            error: null
          })
          setCurrentRetry(0)
        }
      } catch (error) {
        if (isMounted) {
          const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
          
          if (currentRetry < retryCount) {
            // Retry after delay
            setTimeout(() => {
              if (isMounted) {
                setCurrentRetry(prev => prev + 1)
              }
            }, retryDelay)
          } else {
            setState({
              data: null,
              loading: false,
              error: errorMessage
            })
          }
        }
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, [...dependencies, currentRetry])

  const retry = () => {
    setCurrentRetry(0)
  }

  return {
    ...state,
    retry,
    isRetrying: currentRetry > 0
  }
}
