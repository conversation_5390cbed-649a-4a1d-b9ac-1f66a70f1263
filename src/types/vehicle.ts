export interface Vehicle {
  id: string
  make: string
  model: string
  year: number
  plateNumber: string
  vin: string
  category: string
  status: 'available' | 'rented' | 'maintenance' | 'out-of-service' | 'reserved'
  location: string
  dailyRate: number
  weeklyRate?: number
  monthlyRate?: number
  mileage: number
  fuelType: 'gasoline' | 'diesel' | 'hybrid' | 'electric'
  transmission: 'manual' | 'automatic' | 'cvt'
  color: string
  seats: number
  doors: number
  airConditioning: boolean
  gps: boolean
  bluetooth: boolean
  features: string[]
  images: string[]
  insuranceExpiry: string
  registrationExpiry: string
  lastMaintenanceDate?: string
  nextMaintenanceDate?: string
  fuelLevel: number
  batteryLevel?: number
  engineHours?: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface VehicleFormData {
  make: string
  model: string
  year: number
  plateNumber: string
  vin: string
  category: string
  status: Vehicle['status']
  location: string
  dailyRate: number
  weeklyRate?: number
  monthlyRate?: number
  mileage?: number
  fuelType: Vehicle['fuelType']
  transmission: Vehicle['transmission']
  color: string
  seats: number
  doors: number
  airConditioning: boolean
  gps: boolean
  bluetooth: boolean
  features: string[]
  images: string[]
  insuranceExpiry: string
  registrationExpiry: string
  lastMaintenanceDate?: string
  nextMaintenanceDate?: string
  notes?: string
}

export interface VehicleFilters {
  search?: string
  status?: Vehicle['status'][]
  category?: string[]
  location?: string
  make?: string[]
  yearFrom?: number
  yearTo?: number
  priceFrom?: number
  priceTo?: number
  sortBy?: keyof Vehicle
  sortOrder?: 'asc' | 'desc'
}

export interface VehicleStats {
  total: number
  available: number
  rented: number
  maintenance: number
  outOfService: number
  utilizationRate: number
  totalRevenue: number
  averageDailyRate: number
}

export interface VehicleCategory {
  id: string
  name: string
  description?: string
  dailyRateBase: number
  weeklyRateBase?: number
  monthlyRateBase?: number
  securityDeposit: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface VehicleLocation {
  id: string
  name: string
  address: string
  city: string
  country: string
  phone?: string
  email?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface VehicleMaintenance {
  id: string
  vehicleId: string
  type: 'routine' | 'repair' | 'inspection' | 'emergency'
  description: string
  cost: number
  scheduledDate: string
  completedDate?: string
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled'
  serviceProvider: string
  mileageAtService: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface VehicleDocument {
  id: string
  vehicleId: string
  type: 'insurance' | 'registration' | 'inspection' | 'maintenance' | 'other'
  name: string
  fileName: string
  filePath: string
  expiryDate?: string
  uploadedAt: string
  uploadedBy: string
}

export interface VehicleImage {
  id: string
  vehicleId: string
  fileName: string
  filePath: string
  isPrimary: boolean
  caption?: string
  uploadedAt: string
}

export interface VehicleAvailability {
  vehicleId: string
  startDate: string
  endDate: string
  isAvailable: boolean
  reason?: string
  reservationId?: string
}

export interface VehicleUtilization {
  vehicleId: string
  vehicle: Vehicle
  totalDays: number
  rentedDays: number
  maintenanceDays: number
  utilizationRate: number
  revenue: number
  averageDailyRate: number
}

export interface VehicleSearchResult {
  vehicles: Vehicle[]
  total: number
  totalPages: number
  currentPage: number
  filters: VehicleFilters
}

export interface VehicleBulkAction {
  action: 'update-status' | 'update-location' | 'update-rates' | 'delete'
  vehicleIds: string[]
  data?: any
}

export interface VehicleExportOptions {
  format: 'csv' | 'excel' | 'pdf'
  fields: (keyof Vehicle)[]
  filters?: VehicleFilters
  includeImages?: boolean
}

export interface VehicleImportResult {
  success: number
  failed: number
  errors: string[]
  vehicles: Vehicle[]
}

export interface VehicleAlert {
  id: string
  vehicleId: string
  type: 'maintenance-due' | 'insurance-expiry' | 'registration-expiry' | 'low-fuel' | 'overdue-return'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  dueDate?: string
  isRead: boolean
  createdAt: string
}

export interface VehicleReservation {
  id: string
  vehicleId: string
  customerId: string
  startDate: string
  endDate: string
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled'
  totalAmount: number
  createdAt: string
  updatedAt: string
}

export interface VehicleRental {
  id: string
  vehicleId: string
  customerId: string
  reservationId?: string
  contractNumber: string
  pickupDate: string
  returnDate: string
  actualReturnDate?: string
  status: 'active' | 'completed' | 'overdue' | 'cancelled'
  totalAmount: number
  paidAmount: number
  securityDeposit: number
  mileageOut: number
  mileageIn?: number
  fuelLevelOut: number
  fuelLevelIn?: number
  damageNotes?: string
  createdAt: string
  updatedAt: string
}
