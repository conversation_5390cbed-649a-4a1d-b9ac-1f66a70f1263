import React from 'react'
import ReactDOM from 'react-dom/client'
import { Notifications } from '@mantine/notifications'
import { ModalsProvider } from '@mantine/modals'
import { BrowserRouter } from 'react-router-dom'
import App from './App.tsx'
import { ThemeProvider } from './components/ThemeProvider.tsx'
import './i18n/i18n.ts'

// Import Mantine styles
import '@mantine/core/styles.css'
import '@mantine/dates/styles.css'
import '@mantine/notifications/styles.css'
import '@mantine/spotlight/styles.css'
import './styles/global.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <ThemeProvider>
        <ModalsProvider>
          <Notifications />
          <App />
        </ModalsProvider>
      </ThemeProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
