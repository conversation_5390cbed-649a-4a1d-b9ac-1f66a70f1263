{"dashboard": "Dashboard", "fleetManagement": "Fleet Management", "vehicles": "Vehicles", "batchModifications": "Batch Modifications", "performBulkOperationsOnMultipleVehicles": "Perform bulk operations on multiple vehicles", "createNewBatchOperation": "Create New Batch Operation", "recentOperations": "Recent Operations", "operationHistory": "Operation History", "confirmBatchOperation": "Confirm Batch Operation", "thisActionCannotBeUndone": "This action cannot be undone", "exportHistory": "Export History", "selectOperation": "Select Operation", "chooseOperationType": "Choose operation type", "selectTheTypeOfBatchOperation": "Select the type of batch operation you want to perform", "selectVehicles": "Select Vehicles", "chooseTargetVehicles": "Choose Target Vehicles", "configureOperation": "Configure Operation", "setOperationParameters": "Set Operation Parameters", "review": "Review", "reviewAndConfirm": "Review and Confirm", "priceUpdate": "Price Update", "updateDailyRatesForMultipleVehicles": "Update daily rates for multiple vehicles", "statusChange": "Status Change", "changeStatusForMultipleVehicles": "Change status for multiple vehicles", "locationTransfer": "Location Transfer", "transferVehiclesBetweenLocations": "Transfer vehicles between locations", "categoryUpdate": "Category Update", "updateCategoryForMultipleVehicles": "Update category for multiple vehicles", "maintenanceScheduling": "Maintenance Scheduling", "scheduleMaintenanceForMultipleVehicles": "Schedule maintenance for multiple vehicles", "allVehicles": "All Vehicles", "maintenanceRecords": "Maintenance Records", "reservationManagement": "Reservation Management", "manageAllReservationsAndBookings": "Manage all reservations and bookings", "totalReservations": "Total Reservations", "activeReservations": "Active Reservations", "pendingConfirmation": "Pending Confirmation", "completedToday": "Completed Today", "addReservation": "Add Reservation", "editReservation": "Edit Reservation", "viewReservation": "View Reservation", "reservationDetails": "Reservation Details", "reservationNumber": "Reservation Number", "pickupDate": "Pickup Date", "returnDate": "Return Date", "pickupLocation": "Pickup Location", "returnLocation": "Return Location", "totalDays": "Total Days", "dailyRate": "Daily Rate", "subtotal": "Subtotal", "taxAmount": "Tax Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "remainingBalance": "Remaining Balance", "paymentStatus": "Payment Status", "specialRequests": "Special Requests", "customerNotes": "Customer Notes", "internalNotes": "Internal Notes", "securityDeposit": "Security Deposit", "confirmReservation": "Confirm Reservation", "cancelReservation": "Cancel Reservation", "noShow": "No Show", "allReservations": "All Reservations", "searchReservations": "Search reservations...", "allStatuses": "All Statuses", "confirmed": "Confirmed", "cancelled": "Cancelled", "allPayments": "All Payments", "partial": "Partial", "paid": "Paid", "refunded": "Refunded", "clearFilters": "Clear Filters", "reservations": "Reservations", "days": "Days", "calendarView": "Calendar View", "selectCustomer": "Select Customer", "selectVehicle": "Select Vehicle", "selectPickupDate": "Select pickup date", "selectReturnDate": "Select return date", "selectPickupLocation": "Select pickup location", "selectReturnLocation": "Select return location", "enterSpecialRequests": "Enter special requests", "enterNotes": "Enter notes", "createReservation": "Create Reservation", "quotesManagement": "Quotes Management", "generateAndManageRentalQuotes": "Generate and manage rental quotes", "totalQuotes": "Total Quotes", "pendingQuotes": "Pending Quotes", "acceptedQuotes": "Accepted Quotes", "conversionRate": "Conversion Rate", "analytics": "Analytics", "searchQuotes": "Search quotes...", "viewed": "Viewed", "expired": "Expired", "converted": "Converted", "dateRange": "Date Range", "allDates": "All Dates", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "amount": "Amount", "validUntil": "<PERSON>id <PERSON>", "valid": "<PERSON><PERSON>", "discount": "Discount", "quotes": "Quotes", "createQuote": "Create Quote", "discountAmount": "Discount Amount", "specialOffers": "Special Offers", "enterSpecialOffers": "Enter special offers", "quoteDetails": "Quote Det<PERSON>", "convertedToReservation": "Converted to Reservation", "sendQuote": "Send Quote", "sendQuoteToCustomer": "Send quote to customer", "emailMessage": "Email Message", "enterEmailMessage": "Enter email message", "paymentsManagement": "Payments Management", "manageAllPaymentsAndTransactions": "Manage all payments and transactions", "totalPayments": "Total Payments", "totalRevenue": "Total Revenue", "pendingPayments": "Pending Payments", "successRate": "Success Rate", "searchPayments": "Search payments...", "processing": "Processing", "failed": "Failed", "method": "Method", "allMethods": "All Methods", "cash": "Cash", "card": "Card", "bankTransfer": "Bank Transfer", "online": "Online", "cheque": "Cheque", "type": "Type", "allTypes": "All Types", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "full": "Full", "refund": "Refund", "penalty": "Penalty", "payments": "Payments", "due": "Due", "date": "Date", "recordPayment": "Record Payment", "paymentMethodDistribution": "Payment Method Distribution", "paymentMethods": "Payment Methods", "revenueOverview": "Revenue Overview", "pendingAmount": "Pending Amount", "refundAmount": "Refund Amount", "netRevenue": "Net Revenue", "selectReservation": "Select Reservation", "paymentType": "Payment Type", "selectPaymentType": "Select Payment Type", "paymentMethod": "Payment Method", "selectPaymentMethod": "Select Payment Method", "transactionId": "Transaction ID", "enterTransactionId": "Enter transaction ID", "gatewayReference": "Gateway Reference", "enterGatewayReference": "Enter gateway reference", "enterPaymentDescription": "Enter payment description", "enterPaymentNotes": "Enter payment notes", "paymentDetails": "Payment Details", "paymentDate": "Payment Date", "processedBy": "Processed By", "paymentOverdue": "Payment Overdue", "paymentDue": "Payment Due", "dueDate": "Due Date", "processRefund": "Process Refund", "refundPayment": "Refund Payment", "originalPayment": "Original Payment", "refundReason": "Refund Reason", "selectRefundReason": "Select refund reason", "earlyReturn": "Early Return", "cancellation": "Cancellation", "damageDepositReturn": "Damage Deposit Return", "overpayment": "Overpayment", "other": "Other", "refundNotes": "Refund Notes", "enterRefundNotes": "Enter refund notes", "calendarManagement": "Calendar Management", "manageReservationsAndAvailability": "Manage reservations and availability", "monthlyReservations": "Monthly Reservations", "activeRentals": "Active Rentals", "monthlyRevenue": "Monthly Revenue", "quickBook": "Quick Book", "monthView": "Month View", "weekView": "Week View", "dayView": "Day View", "filterByStatus": "Filter by Status", "filterByLocation": "Filter by Location", "allLocations": "All Locations", "sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "more": "More", "quickBookingFromCalendar": "Quick Booking from Calendar", "createNewReservationQuickly": "Create a new reservation quickly", "noReservationsForThisDay": "No reservations for this day", "reservationsForThisDay": "Reservations for this day", "pickup": "Pickup", "return": "Return", "customersManagement": "Customers Management", "manageCustomerDatabaseAndRelationships": "Manage customer database and relationships", "totalCustomers": "Total Customers", "averageCustomerValue": "Average Customer Value", "searchCustomers": "Search customers...", "customerType": "Customer Type", "individual": "Individual", "corporate": "Corporate", "government": "Government", "allCountries": "All Countries", "contact": "Contact", "rentals": "Rentals", "totalSpent": "Total Spent", "loyaltyPoints": "Loyalty Points", "lastRental": "Last Rental", "avg": "Avg", "outstanding": "Outstanding", "next": "Next", "noRentals": "No Rentals", "customers": "Customers", "customerStatusDistribution": "Customer Status Distribution", "customerStatus": "Customer Status", "vipCustomerRevenue": "VIP Customer Revenue", "outstandingBalance": "Outstanding Balance", "firstName": "First Name", "enterFirstName": "Enter first name", "lastName": "Last Name", "enterLastName": "Enter last name", "email": "Email", "enterEmail": "Enter email", "phone": "Phone", "enterPhone": "Enter phone number", "dateOfBirth": "Date of Birth", "selectDateOfBirth": "Select date of birth", "nationality": "Nationality", "selectNationality": "Select nationality", "address": "Address", "enterAddress": "Enter address", "city": "City", "enterCity": "Enter city", "country": "Country", "selectCountry": "Select country", "postalCode": "Postal Code", "enterPostalCode": "Enter postal code", "licenseNumber": "License Number", "enterLicenseNumber": "Enter license number", "licenseExpiry": "License Expiry", "selectLicenseExpiry": "Select license expiry", "selectCustomerType": "Select customer type", "creditLimit": "Credit Limit", "customerDetails": "Customer Details", "customerNumber": "Customer Number", "paymentRequired": "Payment Required", "blacklistCustomer": "Blacklist Customer", "blacklistWarning": "Blacklist Warning", "customerWillBeBlacklisted": "Customer will be blacklisted", "blacklistReason": "Blacklist Reason", "selectBlacklistReason": "Select blacklist reason", "paymentDefault": "Payment Default", "vehicleDamage": "Vehicle Damage", "fraudulentActivity": "Fraudulent Activity", "policyViolation": "Policy Violation", "blacklistNotes": "Blacklist Notes", "enterBlacklistNotes": "Enter blacklist notes", "suspended": "Suspended", "blocked": "Blocked", "blacklistManagement": "Blacklist Management", "manageBlacklistedCustomersAndRestrictions": "Manage blacklisted customers and restrictions", "totalBlacklisted": "Total Blacklisted", "activeBlacklist": "Active Blacklist", "criticalCases": "Critical Cases", "financialImpact": "Financial Impact", "allBlacklist": "All Blacklist", "searchBlacklist": "Search blacklist...", "lifted": "Lifted", "underReview": "Under Review", "warning": "Warning", "allReasons": "All Reasons", "lateReturns": "Late Returns", "paymentIssues": "Payment Issues", "severity": "Severity", "allSeverities": "All Severities", "low": "Low", "medium": "Medium", "high": "High", "critical": "Critical", "impact": "Impact", "blacklistDate": "Blacklist Date", "expiryDate": "Expiry Date", "previousViolations": "Previous Violations", "by": "By", "expiringSoon": "Expiring Soon", "permanent": "Permanent", "blacklistEntries": "Blacklist Entries", "blacklistReasonDistribution": "Blacklist Reason Distribution", "blacklistReasons": "Blacklist Reasons", "financialImpactOverview": "Financial Impact Overview", "totalFinancialImpact": "Total Financial Impact", "averageImpactPerCase": "Average Impact Per Case", "criticalCasesImpact": "Critical Cases Impact", "expiringThisMonth": "Expiring This Month", "addToBlacklist": "Add to Blacklist", "blacklistActionIsPermanent": "Blacklist action is permanent and should be used carefully", "blacklistType": "Blacklist Type", "selectType": "Select type", "severityLevel": "Severity Level", "selectSeverity": "Select severity", "selectExpiryDate": "Select expiry date", "incidentDetails": "Incident Details", "enterIncidentDetails": "Enter incident details", "blacklistDetails": "Blacklist Details", "warningCount": "Warning Count", "blacklistLifted": "Blacklist Lifted", "liftedOn": "Lifted on", "reason": "Reason", "liftBlacklist": "Lift Blacklist", "liftBlacklistConfirmation": "Lift Blacklist Confirmation", "customerWillBeRemovedFromBlacklist": "Customer will be removed from blacklist", "liftReason": "Lift Reason", "enterLiftReason": "Enter lift reason", "activeRentalsManagement": "Active Rentals Management", "monitorAndManageAllActiveRentals": "Monitor and manage all active rentals", "overdueRentals": "Overdue Rentals", "activeRevenue": "Active Revenue", "searchActiveRentals": "Search active rentals...", "dueTomorrow": "Due Tomorrow", "extended": "Extended", "allAlerts": "All Alerts", "fuelLow": "Fuel Low", "mileageWarning": "Mileage Warning", "daysRemaining": "Days Remaining", "fuel": "Fuel", "mileage": "Mileage", "updated": "Updated", "daysOverdue": "Days Overdue", "daysLeft": "Days Left", "activeAlertsAndNotifications": "Active Alerts and Notifications", "resolve": "Resolve", "vehicleLocationMap": "Vehicle Location Map", "realTimeVehicleTrackingWillBeImplementedHere": "Real-time vehicle tracking will be implemented here", "rentalDetails": "Rental Details", "currentLocation": "Current Location", "unknown": "Unknown", "fuelLevel": "Fuel Level", "limit": "Limit", "activeAlerts": "Active Alerts", "specialInstructions": "Special Instructions", "contactCustomer": "Contact Customer", "callCustomer": "Call Customer", "emailCustomer": "Email Customer", "quickMessage": "Quick Message", "typeMessageToCustomer": "Type message to customer", "sendMessage": "Send Message", "extendRental": "Extend Rental", "extendRentalPeriod": "Extend Rental Period", "currentReturnDate": "Current Return Date", "additionalDays": "Additional Days", "newReturnDate": "New Return Date", "selectNewReturnDate": "Select new return date", "additionalAmount": "Additional Amount", "additionalChargesForExtension": "Additional charges for extension", "extensionReason": "Extension Reason", "enterReasonForExtension": "Enter reason for extension", "maintenance": "Maintenance", "carRental": "Car Rental", "trafficFines": "Traffic Fines", "salikCharges": "<PERSON>ik Charges", "violationHistory": "Violation History", "fineCategories": "Fine Categories", "violations": "Violations", "reports": "Reports", "dashboardAnalytics": "Dashboard Analytics", "revenueReports": "Revenue Reports", "vehicleUtilization": "Vehicle Utilization", "customerReports": "Customer Reports", "operationalReports": "Operational Reports", "customReports": "Custom Reports", "export": "Export", "settings": "Settings", "companyProfile": "Company Profile", "generalSettings": "General Settings", "locations": "Locations", "currencyTax": "Currency & Tax", "notifications": "Notifications", "backupRestore": "Backup & Restore", "systemLogs": "System Logs", "users": "Users", "userAccounts": "User Accounts", "rolesPermissions": "Roles & Permissions", "userGroups": "User Groups", "activityLogs": "Activity Logs", "loginHistory": "Login History", "tools": "Tools", "barcodeGenerator": "Barcode Generator", "qrCodeGenerator": "QR Code Generator", "idCardReader": "ID Card Reader", "bulkOperations": "Bulk Operations", "importExport": "Import/Export", "systemDiagnostics": "System Diagnostics", "integrations": "Integrations", "apiManagement": "API Management", "paymentGateways": "Payment Gateways", "smsEmailServices": "SMS & Email Services", "accountingSoftware": "Accounting Software", "webhooks": "Webhooks", "mobile": "Mobile", "customerPortal": "Customer Portal", "mobileCheckIn": "Mobile Check-In", "onlineBooking": "Online Booking", "mobileSettings": "Mobile Settings", "license": "License", "licenseInformation": "License Information", "trialStatus": "Trial Status", "licenseActivation": "License Activation", "securitySettings": "Security Settings", "auditTrail": "Audit Trail", "help": "Help", "userManual": "User Manual", "videoTutorials": "Video Tutorials", "faq": "FAQ", "contactSupport": "Contact Support", "aboutCarvio": "About Carvio", "systemUpdates": "System Updates", "checkIn": "Check In", "checkOut": "Check Out", "damageInspection": "Damage Inspection", "fuelManagement": "Fuel Management", "mileageTracking": "Mileage Tracking", "contracts": "Contracts", "contractTemplates": "Contract Templates", "activeContracts": "Active Contracts", "contractHistory": "Contract History", "digitalSignatures": "Digital Signatures", "printTemplates": "Print Templates", "finance": "Finance", "invoicing": "Invoicing", "pricing": "Pricing", "revenue": "Revenue", "customer": "Customer", "blacklist": "Blacklist", "calendar": "Calendar", "add": "Add", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "update": "Update", "create": "Create", "search": "Search", "filter": "Filter", "refresh": "Refresh", "active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "vip": "VIP", "name": "Name", "description": "Description", "notes": "Notes"}