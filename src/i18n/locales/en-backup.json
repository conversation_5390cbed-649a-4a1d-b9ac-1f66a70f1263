{"dashboard": "Dashboard", "fleetManagement": "Fleet Management", "carRental": "Car Rental", "vehicles": "Vehicles", "batchModifications": "Batch Modifications", "performBulkOperationsOnMultipleVehicles": "Perform bulk operations on multiple vehicles", "createNewBatchOperation": "Create New Batch Operation", "recentOperations": "Recent Operations", "operationHistory": "Operation History", "confirmBatchOperation": "Confirm Batch Operation", "thisActionCannotBeUndone": "This action cannot be undone", "exportHistory": "Export History", "selectOperation": "Select Operation", "chooseOperationType": "Choose operation type", "selectTheTypeOfBatchOperation": "Select the type of batch operation you want to perform", "selectVehicles": "Select Vehicles", "chooseTargetVehicles": "Choose Target Vehicles", "configureOperation": "Configure Operation", "setOperationParameters": "Set Operation Parameters", "review": "Review", "reviewAndConfirm": "Review and Confirm", "priceUpdate": "Price Update", "updateDailyRatesForMultipleVehicles": "Update daily rates for multiple vehicles", "statusChange": "Status Change", "changeStatusForMultipleVehicles": "Change status for multiple vehicles", "locationTransfer": "Location Transfer", "transferVehiclesBetweenLocations": "Transfer vehicles between locations", "categoryUpdate": "Category Update", "updateCategoryForMultipleVehicles": "Update category for multiple vehicles", "maintenanceScheduling": "Maintenance Scheduling", "scheduleMaintenanceForMultipleVehicles": "Schedule maintenance for multiple vehicles", "allVehicles": "All Vehicles", "maintenanceRecords": "Maintenance Records", "reservationManagement": "Reservation Management", "manageAllReservationsAndBookings": "Manage all reservations and bookings", "totalReservations": "Total Reservations", "activeReservations": "Active Reservations", "pendingConfirmation": "Pending Confirmation", "completedToday": "Completed Today", "addReservation": "Add Reservation", "editReservation": "Edit Reservation", "viewReservation": "View Reservation", "reservationDetails": "Reservation Details", "reservationNumber": "Reservation Number", "pickupDate": "Pickup Date", "returnDate": "Return Date", "pickupLocation": "Pickup Location", "returnLocation": "Return Location", "totalDays": "Total Days", "dailyRate": "Daily Rate", "subtotal": "Subtotal", "taxAmount": "Tax Amount", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "remainingBalance": "Remaining Balance", "paymentStatus": "Payment Status", "specialRequests": "Special Requests", "customerNotes": "Customer Notes", "internalNotes": "Internal Notes", "securityDeposit": "Security Deposit", "confirmReservation": "Confirm Reservation", "cancelReservation": "Cancel Reservation", "noShow": "No Show", "allReservations": "All Reservations", "searchReservations": "Search reservations...", "allStatuses": "All Statuses", "confirmed": "Confirmed", "cancelled": "Cancelled", "allPayments": "All Payments", "partial": "Partial", "paid": "Paid", "refunded": "Refunded", "clearFilters": "Clear Filters", "reservations": "Reservations", "days": "Days", "calendarView": "Calendar View", "selectCustomer": "Select Customer", "selectVehicle": "Select Vehicle", "selectPickupDate": "Select pickup date", "selectReturnDate": "Select return date", "selectPickupLocation": "Select pickup location", "selectReturnLocation": "Select return location", "enterSpecialRequests": "Enter special requests", "enterNotes": "Enter notes", "createReservation": "Create Reservation", "quotesManagement": "Quotes Management", "generateAndManageRentalQuotes": "Generate and manage rental quotes", "totalQuotes": "Total Quotes", "pendingQuotes": "Pending Quotes", "acceptedQuotes": "Accepted Quotes", "conversionRate": "Conversion Rate", "analytics": "Analytics", "searchQuotes": "Search quotes...", "viewed": "Viewed", "expired": "Expired", "converted": "Converted", "dateRange": "Date Range", "allDates": "All Dates", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "amount": "Amount", "validUntil": "<PERSON>id <PERSON>", "valid": "<PERSON><PERSON>", "discount": "Discount", "quotes": "Quotes", "createQuote": "Create Quote", "discountAmount": "Discount Amount", "specialOffers": "Special Offers", "enterSpecialOffers": "Enter special offers", "quoteDetails": "Quote Det<PERSON>", "convertedToReservation": "Converted to Reservation", "sendQuote": "Send Quote", "sendQuoteToCustomer": "Send quote to customer", "emailMessage": "Email Message", "enterEmailMessage": "Enter email message", "paymentsManagement": "Payments Management", "manageAllPaymentsAndTransactions": "Manage all payments and transactions", "totalPayments": "Total Payments", "totalRevenue": "Total Revenue", "pendingPayments": "Pending Payments", "successRate": "Success Rate", "searchPayments": "Search payments...", "processing": "Processing", "failed": "Failed", "method": "Method", "allMethods": "All Methods", "cash": "Cash", "card": "Card", "bankTransfer": "Bank Transfer", "online": "Online", "cheque": "Cheque", "type": "Type", "allTypes": "All Types", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "full": "Full", "refund": "Refund", "penalty": "Penalty", "payments": "Payments", "due": "Due", "date": "Date", "recordPayment": "Record Payment", "paymentMethodDistribution": "Payment Method Distribution", "paymentMethods": "Payment Methods", "revenueOverview": "Revenue Overview", "pendingAmount": "Pending Amount", "refundAmount": "Refund Amount", "netRevenue": "Net Revenue", "selectReservation": "Select Reservation", "paymentType": "Payment Type", "selectPaymentType": "Select Payment Type", "paymentMethod": "Payment Method", "selectPaymentMethod": "Select Payment Method", "transactionId": "Transaction ID", "enterTransactionId": "Enter transaction ID", "gatewayReference": "Gateway Reference", "enterGatewayReference": "Enter gateway reference", "enterPaymentDescription": "Enter payment description", "enterPaymentNotes": "Enter payment notes", "paymentDetails": "Payment Details", "paymentDate": "Payment Date", "processedBy": "Processed By", "paymentOverdue": "Payment Overdue", "paymentDue": "Payment Due", "dueDate": "Due Date", "processRefund": "Process Refund", "refundPayment": "Refund Payment", "originalPayment": "Original Payment", "refundReason": "Refund Reason", "selectRefundReason": "Select refund reason", "earlyReturn": "Early Return", "cancellation": "Cancellation", "damageDepositReturn": "Damage Deposit Return", "overpayment": "Overpayment", "other": "Other", "refundNotes": "Refund Notes", "enterRefundNotes": "Enter refund notes", "calendarManagement": "Calendar Management", "manageReservationsAndAvailability": "Manage reservations and availability", "monthlyReservations": "Monthly Reservations", "activeRentals": "Active Rentals", "monthlyRevenue": "Monthly Revenue", "quickBook": "Quick Book", "monthView": "Month View", "weekView": "Week View", "dayView": "Day View", "filterByStatus": "Filter by Status", "filterByLocation": "Filter by Location", "allLocations": "All Locations", "sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "more": "More", "quickBookingFromCalendar": "Quick Booking from Calendar", "createNewReservationQuickly": "Create a new reservation quickly", "noReservationsForThisDay": "No reservations for this day", "reservationsForThisDay": "Reservations for this day", "pickup": "Pickup", "return": "Return", "customersManagement": "Customers Management", "manageCustomerDatabaseAndRelationships": "Manage customer database and relationships", "totalCustomers": "Total Customers", "averageCustomerValue": "Average Customer Value", "searchCustomers": "Search customers...", "customerType": "Customer Type", "individual": "Individual", "corporate": "Corporate", "government": "Government", "allCountries": "All Countries", "contact": "Contact", "rentals": "Rentals", "totalSpent": "Total Spent", "loyaltyPoints": "Loyalty Points", "lastRental": "Last Rental", "avg": "Avg", "outstanding": "Outstanding", "next": "Next", "noRentals": "No Rentals", "customers": "Customers", "customerStatusDistribution": "Customer Status Distribution", "customerStatus": "Customer Status", "vipCustomerRevenue": "VIP Customer Revenue", "outstandingBalance": "Outstanding Balance", "firstName": "First Name", "enterFirstName": "Enter first name", "lastName": "Last Name", "enterLastName": "Enter last name", "email": "Email", "enterEmail": "Enter email", "phone": "Phone", "enterPhone": "Enter phone number", "dateOfBirth": "Date of Birth", "selectDateOfBirth": "Select date of birth", "nationality": "Nationality", "selectNationality": "Select nationality", "address": "Address", "enterAddress": "Enter address", "city": "City", "enterCity": "Enter city", "country": "Country", "selectCountry": "Select country", "postalCode": "Postal Code", "enterPostalCode": "Enter postal code", "licenseNumber": "License Number", "enterLicenseNumber": "Enter license number", "licenseExpiry": "License Expiry", "selectLicenseExpiry": "Select license expiry", "selectCustomerType": "Select customer type", "creditLimit": "Credit Limit", "customerDetails": "Customer Details", "customerNumber": "Customer Number", "paymentRequired": "Payment Required", "blacklistCustomer": "Blacklist Customer", "blacklistWarning": "Blacklist Warning", "customerWillBeBlacklisted": "Customer will be blacklisted", "blacklistReason": "Blacklist Reason", "selectBlacklistReason": "Select blacklist reason", "paymentDefault": "Payment Default", "vehicleDamage": "Vehicle Damage", "fraudulentActivity": "Fraudulent Activity", "policyViolation": "Policy Violation", "blacklistNotes": "Blacklist Notes", "enterBlacklistNotes": "Enter blacklist notes", "suspended": "Suspended", "blocked": "Blocked", "blacklistManagement": "Blacklist Management", "manageBlacklistedCustomersAndRestrictions": "Manage blacklisted customers and restrictions", "totalBlacklisted": "Total Blacklisted", "activeBlacklist": "Active Blacklist", "criticalCases": "Critical Cases", "financialImpact": "Financial Impact", "allBlacklist": "All Blacklist", "searchBlacklist": "Search blacklist...", "lifted": "Lifted", "underReview": "Under Review", "warning": "Warning", "allReasons": "All Reasons", "lateReturns": "Late Returns", "paymentIssues": "Payment Issues", "severity": "Severity", "allSeverities": "All Severities", "low": "Low", "medium": "Medium", "high": "High", "critical": "Critical", "impact": "Impact", "blacklistDate": "Blacklist Date", "expiryDate": "Expiry Date", "previousViolations": "Previous Violations", "by": "By", "expiringSoon": "Expiring Soon", "permanent": "Permanent", "blacklistEntries": "Blacklist Entries", "blacklistReasonDistribution": "Blacklist Reason Distribution", "blacklistReasons": "Blacklist Reasons", "financialImpactOverview": "Financial Impact Overview", "totalFinancialImpact": "Total Financial Impact", "averageImpactPerCase": "Average Impact Per Case", "criticalCasesImpact": "Critical Cases Impact", "expiringThisMonth": "Expiring This Month", "addToBlacklist": "Add to Blacklist", "blacklistActionIsPermanent": "Blacklist action is permanent and should be used carefully", "blacklistType": "Blacklist Type", "selectType": "Select type", "severityLevel": "Severity Level", "selectSeverity": "Select severity", "selectExpiryDate": "Select expiry date", "incidentDetails": "Incident Details", "enterIncidentDetails": "Enter incident details", "blacklistDetails": "Blacklist Details", "warningCount": "Warning Count", "blacklistLifted": "Blacklist Lifted", "liftedOn": "Lifted on", "reason": "Reason", "liftBlacklist": "Lift Blacklist", "liftBlacklistConfirmation": "Lift Blacklist Confirmation", "customerWillBeRemovedFromBlacklist": "Customer will be removed from blacklist", "liftReason": "Lift Reason", "enterLiftReason": "Enter lift reason", "activeRentalsManagement": "Active Rentals Management", "monitorAndManageAllActiveRentals": "Monitor and manage all active rentals", "overdueRentals": "Overdue Rentals", "activeRevenue": "Active Revenue", "searchActiveRentals": "Search active rentals...", "dueTomorrow": "Due Tomorrow", "extended": "Extended", "allAlerts": "All Alerts", "fuelLow": "Fuel Low", "mileageWarning": "Mileage Warning", "daysRemaining": "Days Remaining", "fuel": "Fuel", "mileage": "Mileage", "updated": "Updated", "daysOverdue": "Days Overdue", "daysLeft": "Days Left", "activeAlertsAndNotifications": "Active Alerts and Notifications", "resolve": "Resolve", "vehicleLocationMap": "Vehicle Location Map", "realTimeVehicleTrackingWillBeImplementedHere": "Real-time vehicle tracking will be implemented here", "rentalDetails": "Rental Details", "currentLocation": "Current Location", "unknown": "Unknown", "fuelLevel": "Fuel Level", "limit": "Limit", "activeAlerts": "Active Alerts", "specialInstructions": "Special Instructions", "contactCustomer": "Contact Customer", "callCustomer": "Call Customer", "emailCustomer": "Email Customer", "quickMessage": "Quick Message", "typeMessageToCustomer": "Type message to customer", "sendMessage": "Send Message", "extendRental": "Extend Rental", "extendRentalPeriod": "Extend Rental Period", "currentReturnDate": "Current Return Date", "additionalDays": "Additional Days", "newReturnDate": "New Return Date", "selectNewReturnDate": "Select new return date", "additionalAmount": "Additional Amount", "additionalChargesForExtension": "Additional charges for extension", "extensionReason": "Extension Reason", "enterReasonForExtension": "Enter reason for extension", "maintenance": "Maintenance", "checkInManagement": "Check-In Management", "manageVehiclePickupAndCheckInProcess": "Manage vehicle pickup and check-in process", "todayPickups": "Today Pickups", "customersArrived": "Customers Arrived", "allCheckIns": "All Check-Ins", "todaySchedule": "Today Schedule", "searchCheckIns": "Search check-ins...", "scheduled": "Scheduled", "arrived": "Arrived", "pickupTime": "Pickup Time", "progress": "Progress", "staff": "Staff", "checkIns": "Check-Ins", "todayCheckInSchedule": "Today Check-In Schedule", "documents": "Documents", "verifyDocuments": "Verify Documents", "inspection": "Inspection", "vehicleInspection": "Vehicle Inspection", "keys": "Keys", "handOverKeys": "Hand Over Keys", "complete": "Complete", "checkInComplete": "Check-In Complete", "continueCheckIn": "Continue Check-In", "checkInDetails": "Check-In Details", "pickupDateTime": "Pickup Date & Time", "checkInProgress": "Check-In Progress", "currentStep": "Current Step", "documentsStatus": "Documents Status", "verified": "Verified", "checkInProcess": "Check-In Process", "verifyCustomerDocuments": "Verify Customer Documents", "documentVerification": "Document Verification", "drivingLicense": "Driving License", "passport": "Passport", "creditCard": "Credit Card", "insurance": "Insurance", "uploadDocumentCopies": "Upload Document Copies", "selectFiles": "Select files", "inspectVehicleCondition": "Inspect Vehicle Condition", "exteriorCondition": "Exterior Condition", "noteAnyDamageOrIssues": "Note any damage or issues", "inspectionPhotos": "Inspection Photos", "uploadPhotos": "Upload photos", "handOverVehicleKeys": "Hand Over Vehicle Keys", "keyHandover": "Key Handover", "keyHandoverConfirmation": "Key Handover Confirmation", "ensureCustomerReceivesAllKeys": "Ensure customer receives all keys", "mainKey": "Main Key", "spareKey": "Spare Key", "remoteControl": "Remote Control", "keyCard": "Key Card", "vehicleOrientation": "Vehicle Orientation", "explainVehicleFeaturesToCustomer": "Explain vehicle features to customer", "finalizeCheckIn": "Finalize Check-In", "checkInReadyToComplete": "Check-In Ready to Complete", "reviewAllDetailsBeforeCompletion": "Review all details before completion", "finalNotes": "Final Notes", "anyAdditionalNotesOrObservations": "Any additional notes or observations", "customerSignatureObtained": "Customer Signature Obtained", "allDocumentsCollected": "All Documents Collected", "vehicleInspectionComplete": "Vehicle Inspection Complete", "previous": "Previous", "completeCheckIn": "Complete Check-In", "checkOutManagement": "Check-Out Management", "manageVehicleReturnAndCheckOutProcess": "Manage vehicle return and check-out process", "allCheckOuts": "All Check-Outs", "searchCheckOuts": "Search check-outs...", "returned": "Returned", "additionalCharges": "Additional Charges", "checkOuts": "Check-Outs", "actual": "Actual", "late": "Late", "expectedReturn": "Expected Return", "startCheckOut": "Start Check-Out", "overdueReturn": "Overdue Return", "hoursLate": "Hours Late", "charges": "Charges", "processReturn": "Process Return", "checkOutDetails": "Check-Out Details", "actualReturn": "Actual Return", "checkOutProgress": "Check-Out Progress", "fuelCharges": "Fuel Charges", "lateCharges": "Late Charges", "damageCharges": "Damage Charges", "totalAdditional": "Total Additional", "checkOutNotes": "Check-Out Notes", "checkOutProcess": "Check-Out Process", "vehicleReturnInspection": "Vehicle Return Inspection", "currentFuelLevel": "Current Fuel Level", "pickupLevel": "Pickup Level", "currentMileage": "Current Mileage", "pickupMileage": "Pickup Mileage", "cleanlinessScore": "Cleanliness Score", "interiorCondition": "Interior Condition", "selectCondition": "Select condition", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor", "damageNotes": "Damage Notes", "describeDamageOrIssues": "Describe damage or issues", "damagePhotos": "Damage Photos", "uploadDamagePhotos": "Upload damage photos", "billing": "Billing", "calculateAdditionalCharges": "Calculate additional charges", "additionalChargesCalculation": "Additional Charges Calculation", "cleaningCharges": "Cleaning Charges", "mileageCharges": "Mileage Charges", "lateReturnCharges": "Late Return Charges", "otherCharges": "Other Charges", "totalAdditionalCharges": "Total Additional Charges", "payment": "Payment", "processPaymentAndDeposit": "Process payment and deposit", "paymentProcessing": "Payment Processing", "depositAndCharges": "Deposit and Charges", "deductFromDeposit": "Deduct from De<PERSON>sit", "depositRelease": "Deposit Release", "feedback": "<PERSON><PERSON><PERSON>", "collectCustomerFeedback": "Collect customer feedback", "customerSatisfactionSurvey": "Customer Satisfaction Survey", "overallRating": "Overall Rating", "customerFeedbackComments": "Customer feedback comments", "wouldRecommendToFriends": "Would recommend to friends", "anyFinalNotesOrObservations": "Any final notes or observations", "completeCheckOut": "Complete Check-Out", "dueToday": "Due Today", "overdue": "Overdue", "inProgress": "In Progress", "status": "Status", "location": "Location", "actions": "Actions", "vehicle": "Vehicle", "customer": "Customer", "refresh": "Refresh", "export": "Export", "showing": "Showing", "of": "of", "cancel": "Cancel", "inspectionsManagement": "Inspections Management", "manageVehicleInspectionsAndAssessments": "Manage vehicle inspections and assessments", "scheduledToday": "Scheduled Today", "allInspections": "All Inspections", "scheduleInspection": "Schedule Inspection", "searchInspections": "Search inspections...", "inspectionType": "Inspection Type", "preRental": "Pre-Rental", "postRental": "Post-Rental", "damageAssessment": "Damage Assessment", "periodic": "Periodic", "allPriorities": "All Priorities", "scheduledDate": "Scheduled Date", "condition": "Condition", "inspector": "Inspector", "inspections": "Inspections", "attentionRequired": "Attention Required", "estimatedCost": "Estimated Cost", "issues": "Issues", "viewDetails": "View Details", "inspectionDetails": "Inspection Details", "overallCondition": "Overall Condition", "damagesFound": "Damages Found", "cleanliness": "Cleanliness", "safetyCompliance": "Safety Compliance", "compliant": "Compliant", "repairCostsEstimated": "Repair Costs Estimated", "inspectionNotes": "Inspection Notes", "pre_rental": "Pre-Rental", "post_rental": "Post-Rental", "damage_assessment": "Damage Assessment", "requires_attention": "Requires Attention", "invoicingManagement": "Invoicing Management", "manageInvoicesAndBilling": "Manage invoices and billing", "totalInvoices": "Total Invoices", "paidInvoices": "Paid Invoices", "overdueInvoices": "Overdue Invoices", "searchInvoices": "Search invoices...", "invoiceType": "Invoice Type", "rental": "Rental", "damage": "Damage", "lateFee": "Late Fee", "urgent": "<PERSON><PERSON>", "invoice": "Invoice", "invoices": "Invoices", "createInvoice": "Create Invoice", "draft": "Draft", "sent": "<PERSON><PERSON>", "late_fee": "Late Fee", "cleaning": "Cleaning", "additional": "Additional", "pricingManagement": "Pricing Management", "managePricingRulesAndStrategies": "Manage pricing rules and strategies", "activeRules": "Active Rules", "draftRules": "Draft Rules", "avgBasePrice": "Avg Base Price", "categories": "Categories", "priceCalculator": "Price Calculator", "createRule": "Create Rule", "allRules": "All Rules", "searchPricingRules": "Search pricing rules...", "pricingType": "Pricing Type", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "hourly": "Hourly", "inactive": "Inactive", "ruleName": "Rule Name", "category": "Category", "basePrice": "Base Price", "effectiveFrom": "Effective From", "pricingRules": "Pricing Rules", "overdueInvoice": "Overdue Invoice", "sendReminder": "Send Reminder", "noOverdueInvoices": "No Overdue Invoices", "allInvoicesArePaidOnTime": "All invoices are paid on time", "noDraftInvoices": "No Draft Invoices", "allInvoicesHaveBeenSent": "All invoices have been sent", "sendInvoice": "Send Invoice", "selectInvoiceType": "Select Invoice Type", "selectCurrency": "Select Currency", "selectDate": "Select Date", "selectPriority": "Select Priority", "issueDate": "Issue Date", "taxRate": "Tax Rate", "enterInvoiceNotes": "Enter invoice notes...", "sendImmediately": "Send Immediately", "saveAsDraft": "Save as Draft", "invoiceDetails": "Invoice Details", "paymentProgress": "Payment Progress", "balanceDue": "Balance Due", "downloadPDF": "Download PDF", "print": "Print", "peakSeason": "Peak Season", "calculate": "Calculate", "noActiveRules": "No Active Rules", "createPricingRulesToStart": "Create pricing rules to start", "draftRule": "Draft Rule", "ruleNotYetActive": "Rule not yet active", "activate": "Activate", "noDraftRules": "No Draft Rules", "allRulesAreActive": "All rules are active", "createPricingRule": "Create Pricing Rule", "enterRuleName": "Enter rule name...", "selectCategory": "Select Category", "enterLocation": "Enter location...", "selectPricingType": "Select Pricing Type", "seasonal": "Seasonal", "dynamic": "Dynamic", "seasonalMultipliers": "Seasonal Multipliers", "highSeason": "High Season", "regularSeason": "Regular Season", "lowSeason": "Low Season", "additionalFees": "Additional Fees", "cleaningFee": "Cleaning Fee", "deliveryFee": "Delivery Fee", "airportSurcharge": "Airport Surcharge", "activateImmediately": "Activate Immediately", "pricingRuleDetails": "Pricing Rule Details", "vehicleCategory": "Vehicle Category", "seasonalPricing": "Seasonal Pricing", "editRule": "Edit Rule", "duplicateRule": "Duplicate Rule", "calculatingPriceFor": "Calculating price for", "startDate": "Start Date", "selectStartDate": "Select start date", "endDate": "End Date", "selectEndDate": "Select end date", "season": "Season", "selectSeason": "Select season", "customerAge": "Customer Age", "advanceBookingDays": "Advance Booking Days", "insuranceType": "Insurance Type", "selectInsurance": "Select insurance", "basicInsurance": "Basic Insurance", "comprehensiveInsurance": "Comprehensive Insurance", "premiumInsurance": "Premium Insurance", "priceBreakdown": "Price Breakdown", "seasonalAdjustment": "Seasonal Adjustment", "totalPrice": "Total Price", "close": "Close", "selectPricingRule": "Select Pricing Rule", "choosePricingRule": "Choose pricing rule...", "numberOfDays": "Number of Days", "day": "Day", "editPricingRule": "Edit Pricing Rule", "editingRule": "Editing Rule", "activeRule": "Active Rule", "saveChanges": "Save Changes", "vehicleMake": "Vehicle Make", "selectMake": "Select Make", "anyMake": "Any Make (Category Rule)", "vehicleModel": "Vehicle Model", "enterModel": "Enter model (optional)", "optionalSpecificModel": "Leave empty for all models of selected make", "pricingRuleInfo": "Pricing Rule Configuration", "pricingRuleDescription": "Leave Make/Model empty for category-wide rules, or specify Make/Model for specific car pricing", "comprehensivePaymentManagement": "Comprehensive payment management and transaction processing", "syncPayments": "Sync Payments", "reports": "Reports", "paymentMethodsBreakdown": "Payment Methods Breakdown", "quickActions": "Quick Actions", "syncGateway": "Sync Gateway", "exportReport": "Export Report", "paymentSettings": "Payment Settings", "paymentFilters": "Payment Filters", "advancedFilters": "Advanced Filters", "paymentsTable": "Payments Table", "net": "Net", "fees": "Fees", "receiptSent": "Receipt Sent", "analyticsComingSoon": "Advanced analytics dashboard coming soon", "reportsComingSoon": "Comprehensive reports coming soon", "paymentInformation": "Payment Information", "netAmount": "Net Amount", "referenceNumber": "Reference Number", "refundInformation": "Refund Information", "refundDate": "Refund Date", "disputeInformation": "Dispute Information", "disputeReason": "Dispute Reason", "printReceipt": "Print Receipt", "emailReceipt": "Email Receipt", "recordNewPayment": "Record New Payment", "recordPaymentInfo": "Payment Recording", "recordPaymentDescription": "Record a new payment transaction for a customer", "enterContractNumber": "Enter contract number", "processingFees": "Processing Fees", "selectPaymentDate": "Select payment date", "selectDueDate": "Select due date", "enterReferenceNumber": "Enter reference number", "selectLocation": "Select location", "paymentGateway": "Payment Gateway", "selectGateway": "Select gateway", "sendReceiptToCustomer": "Send receipt to customer", "markAsProcessed": "Mark as processed", "paymentWillBeRecorded": "Payment will be recorded in the system", "refundWarning": "Refund Processing", "refundWarningDescription": "This action will process a refund for the selected payment", "refundMethod": "Refund Method", "selectRefundMethod": "Select refund method", "originalPaymentMethod": "Original Payment Method", "enterRefundReason": "Enter refund reason", "notifyCustomer": "Notify customer", "sendRefundReceipt": "Send refund receipt", "refundWillBeProcessed": "Refund will be processed immediately", "rentalFee": "Rental Fee", "damageFee": "Damage Fee", "fuelCharge": "Fuel Charge", "tollFee": "Toll <PERSON>e", "insuranceFee": "Insurance Fee", "cancellationFee": "Cancellation Fee", "debitCard": "Debit Card", "digitalWallet": "Digital Wallet", "paymentsOverdue": "payments are overdue", "paymentManagement": "Payment management and processing", "trafficFines": "Traffic Fines", "salikCharges": "<PERSON>ik Charges", "violationHistory": "Violation History", "fineCategories": "Fine Categories", "violations": "Violations", "dashboardAnalytics": "Dashboard Analytics", "revenueReports": "Revenue Reports", "vehicleUtilization": "Vehicle Utilization", "customerReports": "Customer Reports", "operationalReports": "Operational Reports", "customReports": "Custom Reports", "settings": "Settings", "companyProfile": "Company Profile", "generalSettings": "General Settings", "locations": "Locations", "currencyTax": "Currency & Tax", "notifications": "Notifications", "backupRestore": "Backup & Restore", "systemLogs": "System Logs", "users": "Users", "userAccounts": "User Accounts", "rolesPermissions": "Roles & Permissions", "userGroups": "User Groups", "activityLogs": "Activity Logs", "loginHistory": "Login History", "tools": "Tools", "barcodeGenerator": "Barcode Generator", "qrCodeGenerator": "QR Code Generator", "idCardReader": "ID Card Reader", "bulkOperations": "Bulk Operations", "importExport": "Import/Export", "systemDiagnostics": "System Diagnostics", "integrations": "Integrations", "apiManagement": "API Management", "paymentGateways": "Payment Gateways", "smsEmailServices": "SMS & Email Services", "accountingSoftware": "Accounting Software", "webhooks": "Webhooks", "mobile": "Mobile", "customerPortal": "Customer Portal", "mobileCheckIn": "Mobile Check-In", "onlineBooking": "Online Booking", "mobileSettings": "Mobile Settings", "license": "License", "licenseInformation": "License Information", "trialStatus": "Trial Status", "licenseActivation": "License Activation", "securitySettings": "Security Settings", "auditTrail": "Audit Trail", "help": "Help", "userManual": "User Manual", "videoTutorials": "Video Tutorials", "faq": "FAQ", "contactSupport": "Contact Support", "aboutCarvio": "About Carvio", "systemUpdates": "System Updates", "manageTrafficViolationsAndFines": "Manage traffic violations and fines", "manageSalikTollCharges": "Manage Salik toll charges", "viewCompleteViolationHistory": "View complete violation history", "manageFineTypesAndCategories": "Manage fine types and categories", "analyzeCustomerBehaviorAndPerformance": "Analyze customer behavior and performance", "monitorOperationalPerformanceMetrics": "Monitor operational performance metrics", "manageCompanyInformationAndSettings": "Manage company information and settings", "pageUnderDevelopment": "Page Under Development", "thisPageIsCurrentlyBeingDeveloped": "This page is currently being developed", "plannedFeatures": "Planned Features", "availableActions": "Available Actions", "buttonsWillBeEnabledWhenPageIsComplete": "Buttons will be enabled when page is complete", "developmentInfo": "Development Info", "estimatedCompletion": "Estimated Completion", "priority": "Priority", "needThisFeature": "Need This Feature?", "ifYouNeedThisFeatureUrgently": "If you need this feature urgently", "pleaseContactSupport": "please contact support", "comingSoon": "Coming Soon", "inDevelopment": "In Development", "planned": "Planned", "addFine": "Add Fine", "addCharge": "Add Charge", "addCategory": "Add Category", "violationType": "Violation Type", "violationDate": "Violation Date", "fineAmount": "Fine Amount", "paymentDeadline": "Payment Deadline", "enterViolationLocation": "Enter violation location", "additionalNotes": "Additional notes", "addTrafficFine": "Add Traffic Fine", "selectViolationType": "Select violation type", "speeding": "Speeding", "redLightViolation": "Red Light Violation", "parkingViolation": "Parking Violation", "recklessDriving": "Reckless Driving", "chargeDate": "Charge Date", "chargedToCustomer": "Charged to Customer", "paidByCompany": "Paid by Company", "addSalikCharge": "Add Salik Charge", "chargeToCustomer": "Charge to Customer", "shouldThisChargeBePassedToCustomer": "Should this charge be passed to customer?", "totalViolations": "Total Violations", "totalCategories": "Total Categories", "activeCategories": "Active Categories", "averageAmount": "Average Amount", "totalPoints": "Total Points", "categoryName": "Category Name", "enterCategoryName": "Enter category name", "categoryDescription": "Category description", "enterCategoryDescription": "Enter category description", "baseAmount": "Base Amount", "points": "Points", "enableThisCategory": "Enable this category", "addFineCategory": "Add Fine Category", "editFineCategory": "Edit Fine Category", "updateCategory": "Update Category"}