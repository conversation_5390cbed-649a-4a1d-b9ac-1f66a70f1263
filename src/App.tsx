import { Routes, Route, Navigate } from 'react-router-dom'
import { AppShell, useMantineColorScheme } from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'

import { Sidebar } from './components/Layout/Sidebar'
import { Header } from './components/Layout/Header'
import { TopBar } from './components/Layout/TopBar'
import { Dashboard } from './pages/Dashboard/Dashboard'
import { ErrorBoundary } from './components/Common/ErrorBoundary'

// Import the implemented pages gradually - testing one by one
import { BatchModifications } from './pages/Fleet/BatchModifications'
import { Vehicles } from './pages/Fleet/Vehicles'
import { Maintenance } from './pages/Fleet/Maintenance'
import { ReservationsComprehensive } from './pages/CarRental/ReservationsComprehensive'
import { QuotesComprehensive } from './pages/CarRental/QuotesComprehensive'
import { PaymentsComprehensive as CarRentalPayments } from './pages/CarRental/PaymentsComprehensive'
import { CalendarComprehensive } from './pages/CarRental/CalendarComprehensive'
import { CustomersComprehensive } from './pages/Customers/CustomersComprehensive'
import { BlacklistComprehensive } from './pages/Customers/BlacklistComprehensive'
import { ActiveRentalsComprehensive } from './pages/Operations/ActiveRentalsComprehensive'
import { CheckInComprehensive } from './pages/Operations/CheckInComprehensive'
import { CheckOutComprehensive } from './pages/Operations/CheckOutComprehensive'
import { FuelManagementSimple } from './pages/Operations/FuelManagementSimple'
import { MileageTrackingSimple } from './pages/Operations/MileageTrackingSimple'
import { LateReturnsSimple } from './pages/Operations/LateReturnsSimple'
import { DamageInspectionWorking } from './pages/Operations/DamageInspectionWorking'
import { ContractTemplatesSimple } from './pages/Contracts/ContractTemplatesSimple'
import { ActiveContractsSimple } from './pages/Contracts/ActiveContractsSimple'
import { ContractHistoryWorking } from './pages/Contracts/ContractHistoryWorking'
import { DigitalSignaturesSimple } from './pages/Contracts/DigitalSignaturesSimple'
import { PrintTemplatesSimple } from './pages/Contracts/PrintTemplatesSimple'
import { PaymentsComprehensive } from './pages/Finance/PaymentsComprehensive'
import { InvoicingComprehensive } from './pages/Finance/InvoicingComprehensive'
import { PricingComprehensive } from './pages/Finance/PricingComprehensive'
import { RevenueSimple } from './pages/Finance/RevenueSimple'
import { DashboardAnalyticsSimple } from './pages/Reports/DashboardAnalyticsSimple'
import { RevenueReportsSimple } from './pages/Reports/RevenueReportsSimple'
import { VehicleUtilizationSimple } from './pages/Reports/VehicleUtilizationSimple'
import { TrafficFines } from './pages/Violations/TrafficFines'
import { SalikCharges } from './pages/Violations/SalikCharges'
import { ViolationHistory } from './pages/Violations/ViolationHistory'
import { FineCategories } from './pages/Violations/FineCategories'
import { CustomerReports } from './pages/Reports/CustomerReports'
import { OperationalReports } from './pages/Reports/OperationalReports'
import { CompanyProfile } from './pages/Settings/CompanyProfile'
import {
  CustomReports, ExportReports, GeneralSettings, Locations, CurrencyTax,
  Notifications, BackupRestore, SystemLogs, UserAccounts, RolesPermissions,
  UserGroups, ActivityLogs, LoginHistory, BarcodeGenerator, QRCodeGenerator,
  IDCardReader, BulkOperations, ImportExport, SystemDiagnostics, APIManagement,
  PaymentGateways, SMSEmailServices, AccountingSoftware, Webhooks, CustomerPortal,
  MobileCheckIn, OnlineBooking, MobileSettings, LicenseInfo, TrialStatus,
  LicenseActivation, SecuritySettings, AuditTrail, UserManual, VideoTutorials,
  FAQ, ContactSupport, AboutCarvio, SystemUpdates
} from './pages/MissingPages'

function App() {
  const [opened, { toggle }] = useDisclosure()
  const { colorScheme, setColorScheme } = useMantineColorScheme()

  const toggleColorScheme = () => {
    const newScheme = colorScheme === 'light' ? 'dark' : 'light'
    setColorScheme(newScheme)
  }

  // Convert MantineColorScheme to our expected type
  const currentScheme: 'light' | 'dark' = colorScheme === 'auto' ? 'light' : colorScheme

  return (
    <>
      {/* Top thin bar for macOS window controls */}
      <div style={{ position: 'fixed', top: 0, left: 0, right: 0, zIndex: 1000 }}>
        <TopBar />
      </div>

      <AppShell
        header={{ height: 80 }} // Increased height to accommodate top bar + spacing
        navbar={{
          width: 280,
          breakpoint: 'sm',
          collapsed: { mobile: !opened },
        }}
        padding="md"
        style={{
          paddingTop: '32px', // Only account for top bar height
          marginTop: '-20px' // Bring content up to reduce gap
        }}
      >
        <AppShell.Header>
          <Header
            opened={opened}
            toggle={toggle}
            colorScheme={currentScheme}
            toggleColorScheme={toggleColorScheme}
          />
        </AppShell.Header>

      <AppShell.Navbar>
        <Sidebar />
      </AppShell.Navbar>

      <AppShell.Main>
        <ErrorBoundary>
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/fleet/vehicles" element={<Vehicles />} />
          <Route path="/fleet/batch-modifications" element={<BatchModifications />} />
          <Route path="/fleet/maintenance" element={<Maintenance />} />
          <Route path="/car-rental/reservations" element={<ReservationsComprehensive />} />
          <Route path="/car-rental/quotes" element={<QuotesComprehensive />} />
          <Route path="/car-rental/payments" element={<CarRentalPayments />} />
          <Route path="/car-rental/calendar" element={<CalendarComprehensive />} />
          <Route path="/customers" element={<CustomersComprehensive />} />
          <Route path="/customers/blacklist" element={<BlacklistComprehensive />} />
          <Route path="/operations/active-rentals" element={<ActiveRentalsComprehensive />} />
          <Route path="/operations/check-in" element={<CheckInComprehensive />} />
          <Route path="/operations/check-out" element={<CheckOutComprehensive />} />
          <Route path="/operations/fuel-management" element={<FuelManagementSimple />} />
          <Route path="/operations/mileage-tracking" element={<MileageTrackingSimple />} />
          <Route path="/operations/late-returns" element={<LateReturnsSimple />} />
          <Route path="/operations/damage-inspection" element={<DamageInspectionWorking />} />
          <Route path="/contracts/templates" element={<ContractTemplatesSimple />} />
          <Route path="/contracts/active" element={<ActiveContractsSimple />} />
          <Route path="/contracts/history" element={<ContractHistoryWorking />} />
          <Route path="/contracts/signatures" element={<DigitalSignaturesSimple />} />
          <Route path="/contracts/print-templates" element={<PrintTemplatesSimple />} />
          <Route path="/finance/payments" element={<PaymentsComprehensive />} />
          <Route path="/finance/invoicing" element={<InvoicingComprehensive />} />
          <Route path="/finance/pricing" element={<PricingComprehensive />} />
          <Route path="/finance/revenue" element={<RevenueSimple />} />
          <Route path="/reports/dashboard" element={<DashboardAnalyticsSimple />} />
          <Route path="/reports/revenue" element={<RevenueReportsSimple />} />
          <Route path="/reports/utilization" element={<VehicleUtilizationSimple />} />
          <Route path="/violations/traffic-fines" element={<TrafficFines />} />
          <Route path="/violations/salik" element={<SalikCharges />} />
          <Route path="/violations/history" element={<ViolationHistory />} />
          <Route path="/violations/categories" element={<FineCategories />} />
          <Route path="/reports/customers" element={<CustomerReports />} />
          <Route path="/reports/operational" element={<OperationalReports />} />
          <Route path="/reports/custom" element={<CustomReports />} />
          <Route path="/reports/export" element={<ExportReports />} />
          <Route path="/settings/company" element={<CompanyProfile />} />
          <Route path="/settings/general" element={<GeneralSettings />} />
          <Route path="/settings/locations" element={<Locations />} />
          <Route path="/settings/currency-tax" element={<CurrencyTax />} />
          <Route path="/settings/notifications" element={<Notifications />} />
          <Route path="/settings/backup" element={<BackupRestore />} />
          <Route path="/settings/logs" element={<SystemLogs />} />
          <Route path="/users/accounts" element={<UserAccounts />} />
          <Route path="/users/roles" element={<RolesPermissions />} />
          <Route path="/users/groups" element={<UserGroups />} />
          <Route path="/users/activity" element={<ActivityLogs />} />
          <Route path="/users/login-history" element={<LoginHistory />} />
          <Route path="/tools/barcode" element={<BarcodeGenerator />} />
          <Route path="/tools/qr-code" element={<QRCodeGenerator />} />
          <Route path="/tools/id-reader" element={<IDCardReader />} />
          <Route path="/tools/bulk-operations" element={<BulkOperations />} />
          <Route path="/tools/import-export" element={<ImportExport />} />
          <Route path="/tools/diagnostics" element={<SystemDiagnostics />} />
          <Route path="/integrations/api" element={<APIManagement />} />
          <Route path="/integrations/payments" element={<PaymentGateways />} />
          <Route path="/integrations/sms-email" element={<SMSEmailServices />} />
          <Route path="/integrations/accounting" element={<AccountingSoftware />} />
          <Route path="/integrations/webhooks" element={<Webhooks />} />
          <Route path="/mobile/portal" element={<CustomerPortal />} />
          <Route path="/mobile/check-in" element={<MobileCheckIn />} />
          <Route path="/mobile/booking" element={<OnlineBooking />} />
          <Route path="/mobile/settings" element={<MobileSettings />} />
          <Route path="/license/info" element={<LicenseInfo />} />
          <Route path="/license/trial" element={<TrialStatus />} />
          <Route path="/license/activation" element={<LicenseActivation />} />
          <Route path="/license/security" element={<SecuritySettings />} />
          <Route path="/license/audit" element={<AuditTrail />} />
          <Route path="/help/manual" element={<UserManual />} />
          <Route path="/help/tutorials" element={<VideoTutorials />} />
          <Route path="/help/faq" element={<FAQ />} />
          <Route path="/help/support" element={<ContactSupport />} />
          <Route path="/help/about" element={<AboutCarvio />} />
          <Route path="/help/updates" element={<SystemUpdates />} />
          </Routes>
        </ErrorBoundary>
      </AppShell.Main>
    </AppShell>
    </>
  )
}

export default App
