import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconArchive,
  IconCar,
  IconCheck,
  IconDownload,
  IconEye,
  IconFileText,
  IconSearch,
  IconUser
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface ContractHistory {
  id: string
  contractNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  startDate: string
  endDate: string
  contractType: 'standard' | 'premium' | 'corporate' | 'short-term'
  status: 'completed' | 'terminated' | 'expired' | 'cancelled'
  totalAmount: number
  completionDate: string
  location: string
}

export function ContractHistoryWorking() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const historyContracts: ContractHistory[] = [
    {
      id: '1',
      contractNumber: 'CON-2023-156',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      startDate: '2023-12-15',
      endDate: '2023-12-22',
      contractType: 'standard',
      status: 'completed',
      totalAmount: 600,
      completionDate: '2023-12-22',
      location: 'Dubai Airport'
    },
    {
      id: '2',
      contractNumber: 'CON-2023-145',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      startDate: '2023-12-10',
      endDate: '2023-12-20',
      contractType: 'premium',
      status: 'terminated',
      totalAmount: 800,
      completionDate: '2023-12-18',
      location: 'Abu Dhabi'
    },
    {
      id: '3',
      contractNumber: 'CON-2023-134',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      startDate: '2023-11-16',
      endDate: '2023-12-16',
      contractType: 'corporate',
      status: 'completed',
      totalAmount: 2400,
      completionDate: '2023-12-16',
      location: 'Sharjah'
    }
  ]

  const stats = [
    { label: t('totalHistoryContracts'), value: '1,247', color: 'blue', icon: IconArchive },
    { label: t('completedContracts'), value: '1,156', color: 'green', icon: IconCheck },
    { label: t('terminatedContracts'), value: '67', color: 'red', icon: IconCar},
    { label: t('totalRevenue'), value: '$456,780', color: 'green', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'terminated': return 'red'
      case 'expired': return 'orange'
      case 'cancelled': return 'gray'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'standard': return 'blue'
      case 'premium': return 'yellow'
      case 'corporate': return 'purple'
      case 'short-term': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredContracts = historyContracts.filter(contract => {
    const matchesSearch = contract.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         contract.contractNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || contract.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('contractHistory')}</Title>
          <Text c="dimmed" size="sm">{t('viewPastContractsAndArchive')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportHistory')}
          </Button>
          <Button leftSection={<IconArchive size={16} />} onClick={() => setModalOpen(true)}>
            {t('archiveContract')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="history">{t('contractHistory')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('performanceMetrics')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconCheck size={16} />} color="green">
                    <Text fw={500} size="sm">{t('completionRate')}</Text>
                    <Text size="xs">92.7% {t('contractsCompletedSuccessfully')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconUser size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('customerSatisfaction')}</Text>
                    <Text size="xs">4.6/5 {t('averageCustomerRating')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Completions - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentCompletions')}</Title>
                
                <Stack gap="sm">
                  {historyContracts.slice(0, 3).map((contract) => (
                    <Paper key={contract.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getTypeColor(contract.contractType)} radius="xl" size="sm">
                            {getInitials(contract.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{contract.customerName}</Text>
                            <Text size="xs" c="dimmed">{contract.vehicleName} - {contract.completionDate}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(contract.status)} size="sm">
                            ${contract.totalAmount}
                          </Badge>
                          <Text size="xs" c="dimmed">{contract.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="history" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchHistory')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'completed', label: t('completed') },
                    { value: 'terminated', label: t('terminated') },
                    { value: 'expired', label: t('expired') },
                    { value: 'cancelled', label: t('cancelled') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* History Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('period')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredContracts.map((contract) => (
                  <Table.Tr key={contract.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getTypeColor(contract.contractType)} radius="xl" size="sm">
                          {getInitials(contract.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{contract.customerName}</Text>
                          <Text size="xs" c="dimmed">{contract.contractNumber}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{contract.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{contract.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{contract.startDate} - {contract.endDate}</Text>
                        <Text size="xs" c="dimmed">{t('completed')} {contract.completionDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(contract.status)}>
                        {t(contract.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(contract.contractType)} variant="light">
                        {t(contract.contractType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">${contract.totalAmount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconDownload size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconFileText size={12} />}>
                          {t('view')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('archiveContract')}
        size="md"
      >
        <Stack>
          <Select
            label={t('contract')}
            placeholder={t('selectContract')}
            data={[
              { value: '1', label: 'CON-2024-001 - Ahmed Al-Rashid' },
              { value: '2', label: 'CON-2024-002 - Sarah Johnson' },
              { value: '3', label: 'CON-2024-003 - Mohammed Hassan' }
            ]}
            required
          />

          <Select
            label={t('archiveReason')}
            placeholder={t('selectArchiveReason')}
            data={[
              { value: 'completed', label: t('completed') },
              { value: 'terminated', label: t('terminated') },
              { value: 'expired', label: t('expired') },
              { value: 'cancelled', label: t('cancelled') }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconArchive size={16} />} onClick={() => setModalOpen(false)}>
              {t('archive')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
