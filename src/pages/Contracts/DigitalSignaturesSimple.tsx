import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconClock,
  IconDownload,
  IconEye,
  IconMail,
  IconPlus,
  IconSearch,
  IconSignature
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface DigitalSignature {
  id: string
  contractNumber: string
  customerName: string
  customerEmail: string
  documentType: 'rental-agreement' | 'insurance-waiver' | 'damage-report' | 'return-form'
  signatureStatus: 'pending' | 'signed' | 'expired' | 'rejected'
  sentDate: string
  signedDate: string
  expiryDate: string
  ipAddress: string
  deviceInfo: string
  location: string
}

export function DigitalSignaturesSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const signatures: DigitalSignature[] = [
    {
      id: '1',
      contractNumber: 'CON-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerEmail: '<EMAIL>',
      documentType: 'rental-agreement',
      signatureStatus: 'signed',
      sentDate: '2024-01-15',
      signedDate: '2024-01-15',
      expiryDate: '2024-01-22',
      ipAddress: '*************',
      deviceInfo: 'iPhone 15 Pro',
      location: 'Dubai, UAE'
    },
    {
      id: '2',
      contractNumber: 'CON-2024-002',
      customerName: 'Sarah Johnson',
      customerEmail: '<EMAIL>',
      documentType: 'insurance-waiver',
      signatureStatus: 'pending',
      sentDate: '2024-01-20',
      signedDate: '',
      expiryDate: '2024-01-27',
      ipAddress: '',
      deviceInfo: '',
      location: 'Abu Dhabi, UAE'
    },
    {
      id: '3',
      contractNumber: 'CON-2024-003',
      customerName: 'Mohammed Hassan',
      customerEmail: '<EMAIL>',
      documentType: 'damage-report',
      signatureStatus: 'expired',
      sentDate: '2024-01-10',
      signedDate: '',
      expiryDate: '2024-01-17',
      ipAddress: '',
      deviceInfo: '',
      location: 'Sharjah, UAE'
    }
  ]

  const stats = [
    { label: t('totalSignatures'), value: '156', color: 'blue', icon: IconSignature },
    { label: t('pendingSignatures'), value: '12', color: 'orange', icon: IconClock },
    { label: t('signedToday'), value: '8', color: 'green', icon: IconCar},
    { label: t('expiredSignatures'), value: '3', color: 'red', icon: IconAlertTriangle }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'signed': return 'green'
      case 'pending': return 'orange'
      case 'expired': return 'red'
      case 'rejected': return 'gray'
      default: return 'gray'
    }
  }

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case 'rental-agreement': return 'blue'
      case 'insurance-waiver': return 'yellow'
      case 'damage-report': return 'red'
      case 'return-form': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredSignatures = signatures.filter(signature => {
    const matchesSearch = signature.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         signature.contractNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || signature.signatureStatus === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('digitalSignatures')}</Title>
          <Text c="dimmed" size="sm">{t('manageElectronicSignaturesAndDocuments')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportSignatures')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('sendForSignature')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="signatures">{t('allSignatures')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('signatureAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('expiredSignatures')}</Text>
                    <Text size="xs">3 {t('signaturesHaveExpired')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('pendingSignatures')}</Text>
                    <Text size="xs">12 {t('documentsAwaitingSignature')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconMail size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('remindersNeeded')}</Text>
                    <Text size="xs">5 {t('customersNeedReminders')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Signatures - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentSignatures')}</Title>
                
                <Stack gap="sm">
                  {signatures.slice(0, 3).map((signature) => (
                    <Paper key={signature.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getDocumentTypeColor(signature.documentType)} radius="xl" size="sm">
                            {getInitials(signature.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{signature.customerName}</Text>
                            <Text size="xs" c="dimmed">{t(signature.documentType)} - {signature.sentDate}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(signature.signatureStatus)} size="sm">
                            {t(signature.signatureStatus)}
                          </Badge>
                          <Text size="xs" c="dimmed">{signature.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="signatures" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchSignatures')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'signed', label: t('signed') },
                    { value: 'pending', label: t('pending') },
                    { value: 'expired', label: t('expired') },
                    { value: 'rejected', label: t('rejected') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Signatures Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('document')}</Table.Th>
                  <Table.Th>{t('sentDate')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('signedDate')}</Table.Th>
                  <Table.Th>{t('expiryDate')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredSignatures.map((signature) => (
                  <Table.Tr key={signature.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getDocumentTypeColor(signature.documentType)} radius="xl" size="sm">
                          {getInitials(signature.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{signature.customerName}</Text>
                          <Text size="xs" c="dimmed">{signature.customerEmail}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{signature.contractNumber}</Text>
                        <Badge color={getDocumentTypeColor(signature.documentType)} variant="light" size="xs">
                          {t(signature.documentType)}
                        </Badge>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{signature.sentDate}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(signature.signatureStatus)}>
                        {t(signature.signatureStatus)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{signature.signedDate || '-'}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{signature.expiryDate}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconMail size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconDownload size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconSignature size={12} />}>
                          {t('resend')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('sendForSignature')}
        size="md"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid - <EMAIL>' },
              { value: '2', label: 'Sarah Johnson - <EMAIL>' },
              { value: '3', label: 'Mohammed Hassan - <EMAIL>' }
            ]}
            required
          />

          <Select
            label={t('documentType')}
            placeholder={t('selectDocumentType')}
            data={[
              { value: 'rental-agreement', label: t('rentalAgreement') },
              { value: 'insurance-waiver', label: t('insuranceWaiver') },
              { value: 'damage-report', label: t('damageReport') },
              { value: 'return-form', label: t('returnForm') }
            ]}
            required
          />

          <TextInput
            label={t('expiryDays')}
            placeholder={t('enterExpiryDays')}
            defaultValue="7"
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconSignature size={16} />} onClick={() => setModalOpen(false)}>
              {t('sendDocument')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
