import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconPlus,
  IconPrinter,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface PrintTemplate {
  id: string
  templateName: string
  templateType: 'contract' | 'invoice' | 'receipt' | 'report' | 'form'
  paperSize: 'A4' | 'Letter' | 'Legal' | 'A5'
  orientation: 'portrait' | 'landscape'
  language: string
  version: string
  createdDate: string
  lastModified: string
  status: 'active' | 'draft' | 'archived'
  usageCount: number
  createdBy: string
}

export function PrintTemplatesSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const printTemplates: PrintTemplate[] = [
    {
      id: '1',
      templateName: 'Standard Rental Contract',
      templateType: 'contract',
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'English',
      version: '2.1',
      createdDate: '2024-01-15',
      lastModified: '2024-01-18',
      status: 'active',
      usageCount: 245,
      createdBy: 'Admin'
    },
    {
      id: '2',
      templateName: 'Rental Invoice Template',
      templateType: 'invoice',
      paperSize: 'A4',
      orientation: 'portrait',
      language: 'English',
      version: '1.8',
      createdDate: '2024-01-10',
      lastModified: '2024-01-16',
      status: 'active',
      usageCount: 189,
      createdBy: 'Finance Team'
    },
    {
      id: '3',
      templateName: 'Payment Receipt',
      templateType: 'receipt',
      paperSize: 'A5',
      orientation: 'portrait',
      language: 'Arabic',
      version: '1.2',
      createdDate: '2024-01-05',
      lastModified: '2024-01-12',
      status: 'draft',
      usageCount: 67,
      createdBy: 'Manager'
    }
  ]

  const stats = [
    { label: t('totalPrintTemplates'), value: '24', color: 'blue', icon: IconPrinter },
    { label: t('activePrintTemplates'), value: '18', color: 'green', icon: IconCheck },
    { label: t('draftPrintTemplates'), value: '4', color: 'orange', icon: IconClock },
    { label: t('totalPrints'), value: '2,847', color: 'gray', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'draft': return 'orange'
      case 'archived': return 'gray'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'contract': return 'blue'
      case 'invoice': return 'green'
      case 'receipt': return 'yellow'
      case 'report': return 'purple'
      case 'form': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredTemplates = printTemplates.filter(template => {
    const matchesSearch = template.templateName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.templateType.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || template.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('printTemplates')}</Title>
          <Text c="dimmed" size="sm">{t('managePrintableDocumentTemplates')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportPrintTemplates')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('newPrintTemplate')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="templates">{t('allPrintTemplates')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('printTemplateAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('outdatedPrintTemplates')}</Text>
                    <Text size="xs">2 {t('printTemplatesNeedUpdating')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('draftPrintTemplates')}</Text>
                    <Text size="xs">4 {t('printTemplatesInDraftStatus')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconPrinter size={16} />} color="green">
                    <Text fw={500} size="sm">{t('popularPrintTemplates')}</Text>
                    <Text size="xs">6 {t('mostUsedPrintTemplates')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Popular Print Templates - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('popularPrintTemplates')}</Title>
                
                <Stack gap="sm">
                  {printTemplates.slice(0, 3).map((template) => (
                    <Paper key={template.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getTypeColor(template.templateType)} radius="xl" size="sm">
                            {getInitials(template.templateName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{template.templateName}</Text>
                            <Text size="xs" c="dimmed">{template.paperSize} {template.orientation} - v{template.version}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(template.status)} size="sm">
                            {template.usageCount} {t('prints')}
                          </Badge>
                          <Text size="xs" c="dimmed">{template.language}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="templates" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchPrintTemplates')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'active', label: t('active') },
                    { value: 'draft', label: t('draft') },
                    { value: 'archived', label: t('archived') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Print Templates Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('templateName')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('paperSize')}</Table.Th>
                  <Table.Th>{t('language')}</Table.Th>
                  <Table.Th>{t('version')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('prints')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredTemplates.map((template) => (
                  <Table.Tr key={template.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getTypeColor(template.templateType)} radius="xl" size="sm">
                          {getInitials(template.templateName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{template.templateName}</Text>
                          <Text size="xs" c="dimmed">{t('createdBy')} {template.createdBy}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(template.templateType)} variant="light">
                        {t(template.templateType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{template.paperSize}</Text>
                        <Text size="xs" c="dimmed">{template.orientation}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{template.language}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">v{template.version}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(template.status)}>
                        {t(template.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{template.usageCount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPrinter size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconDownload size={12} />}>
                          {t('download')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('newPrintTemplate')}
        size="md"
      >
        <Stack>
          <TextInput
            label={t('templateName')}
            placeholder={t('enterPrintTemplateName')}
            required
          />

          <Select
            label={t('templateType')}
            placeholder={t('selectPrintTemplateType')}
            data={[
              { value: 'contract', label: t('contract') },
              { value: 'invoice', label: t('invoice') },
              { value: 'receipt', label: t('receipt') },
              { value: 'report', label: t('report') },
              { value: 'form', label: t('form') }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('paperSize')}
                placeholder={t('selectPaperSize')}
                data={[
                  { value: 'A4', label: 'A4' },
                  { value: 'Letter', label: 'Letter' },
                  { value: 'Legal', label: 'Legal' },
                  { value: 'A5', label: 'A5' }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('orientation')}
                placeholder={t('selectOrientation')}
                data={[
                  { value: 'portrait', label: t('portrait') },
                  { value: 'landscape', label: t('landscape') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('createPrintTemplate')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
