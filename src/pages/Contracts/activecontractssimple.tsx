import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFileText,
  IconPhone,
  IconPlus,
  IconSearch,
  IconSignature
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface ActiveContract {
  id: string
  contractNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  startDate: string
  endDate: string
  daysRemaining: number
  contractType: 'standard' | 'premium' | 'corporate' | 'short-term'
  status: 'active' | 'expiring-soon' | 'renewal-due' | 'pending-signature'
  totalAmount: number
  signedDate: string
  location: string
}

export function ActiveContractsSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const contracts: ActiveContract[] = [
    {
      id: '1',
      contractNumber: 'CON-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      startDate: '2024-01-15',
      endDate: '2024-01-22',
      daysRemaining: 2,
      contractType: 'standard',
      status: 'expiring-soon',
      totalAmount: 600,
      signedDate: '2024-01-14',
      location: 'Dubai Airport'
    },
    {
      id: '2',
      contractNumber: 'CON-2024-002',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      startDate: '2024-01-10',
      endDate: '2024-01-25',
      daysRemaining: 5,
      contractType: 'premium',
      status: 'active',
      totalAmount: 800,
      signedDate: '2024-01-09',
      location: 'Abu Dhabi'
    },
    {
      id: '3',
      contractNumber: 'CON-2024-003',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      startDate: '2024-01-16',
      endDate: '2024-02-16',
      daysRemaining: 27,
      contractType: 'corporate',
      status: 'active',
      totalAmount: 2400,
      signedDate: '2024-01-15',
      location: 'Sharjah'
    }
  ]

  const stats = [
    { label: t('totalActiveContracts'), value: '45', color: 'blue', icon: IconFileText },
    { label: t('expiringSoon'), value: '8', color: 'orange', icon: IconClock },
    { label: t('renewalsDue'), value: '3', color: 'red', icon: IconAlertTriangle },
    { label: t('totalValue'), value: '$125,400', color: 'green', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'expiring-soon': return 'orange'
      case 'renewal-due': return 'red'
      case 'pending-signature': return 'blue'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'standard': return 'blue'
      case 'premium': return 'yellow'
      case 'corporate': return 'purple'
      case 'short-term': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredContracts = contracts.filter(contract => {
    const matchesSearch = contract.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         contract.contractNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || contract.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('activeContracts')}
        description={t('manageCurrentlyActiveContracts')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportContracts')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('newContract')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="contracts">{t('allActiveContracts')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('contractAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('renewalsDue')}</Text>
                    <Text size="xs">3 {t('contractsRequireRenewal')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('expiringSoon')}</Text>
                    <Text size="xs">8 {t('contractsExpiringIn7Days')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconSignature size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('pendingSignatures')}</Text>
                    <Text size="xs">2 {t('contractsAwaitingSignature')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Contracts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentContracts')}</Title>
                
                <Stack gap="sm">
                  {contracts.slice(0, 3).map((contract) => (
                    <Paper key={contract.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getTypeColor(contract.contractType)} radius="xl" size="sm">
                            {getInitials(contract.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{contract.customerName}</Text>
                            <Text size="xs" c="dimmed">{contract.vehicleName} - {contract.daysRemaining} days left</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(contract.status)} size="sm">
                            ${contract.totalAmount}
                          </Badge>
                          <Text size="xs" c="dimmed">{contract.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="contracts" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchContracts')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'active', label: t('active') },
                    { value: 'expiring-soon', label: t('expiringSoon') },
                    { value: 'renewal-due', label: t('renewalDue') },
                    { value: 'pending-signature', label: t('pendingSignature') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Contracts Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('contractPeriod')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('value')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredContracts.map((contract) => (
                  <Table.Tr key={contract.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getTypeColor(contract.contractType)} radius="xl" size="sm">
                          {getInitials(contract.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{contract.customerName}</Text>
                          <Text size="xs" c="dimmed">{contract.contractNumber}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{contract.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{contract.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{contract.startDate} - {contract.endDate}</Text>
                        <Text size="xs" c="dimmed">{contract.daysRemaining} days remaining</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(contract.status)}>
                        {t(contract.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(contract.contractType)} variant="light">
                        {t(contract.contractType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">${contract.totalAmount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPhone size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconFileText size={12} />}>
                          {t('view')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('newActiveContract')}
        size="md"
        withCloseButton
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid' },
              { value: '2', label: 'Sarah Johnson' },
              { value: '3', label: 'Mohammed Hassan' }
            ]}
            required
          />

          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023 - ABC-123' },
              { value: '2', label: 'BMW X5 2022 - XYZ-456' },
              { value: '3', label: 'Mercedes C-Class 2023 - DEF-789' }
            ]}
            required
          />

          <Select
            label={t('contractType')}
            placeholder={t('selectContractType')}
            data={[
              { value: 'standard', label: t('standard') },
              { value: 'premium', label: t('premium') },
              { value: 'corporate', label: t('corporate') },
              { value: 'short-term', label: t('shortTerm') }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('createContract')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
