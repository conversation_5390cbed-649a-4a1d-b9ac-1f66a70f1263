import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconCopy,
  IconDownload,
  IconEdit,
  IconEye,
  IconPlus,
  IconSearch,
  IconTrash
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface ContractTemplate {
  id: string
  templateName: string
  templateType: 'standard' | 'premium' | 'corporate' | 'short-term'
  language: string
  version: string
  createdDate: string
  lastModified: string
  status: 'active' | 'draft' | 'archived'
  usageCount: number
  createdBy: string
}

export function ContractTemplatesSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const templates: ContractTemplate[] = [
    {
      id: '1',
      templateName: 'Standard Rental Agreement',
      templateType: 'standard',
      language: 'English',
      version: '2.1',
      createdDate: '2024-01-15',
      lastModified: '2024-01-18',
      status: 'active',
      usageCount: 156,
      createdBy: 'Admin'
    },
    {
      id: '2',
      templateName: 'Premium Vehicle Contract',
      templateType: 'premium',
      language: 'English',
      version: '1.5',
      createdDate: '2024-01-10',
      lastModified: '2024-01-16',
      status: 'active',
      usageCount: 89,
      createdBy: 'Manager'
    },
    {
      id: '3',
      templateName: 'Corporate Fleet Agreement',
      templateType: 'corporate',
      language: 'Arabic',
      version: '3.0',
      createdDate: '2024-01-05',
      lastModified: '2024-01-12',
      status: 'draft',
      usageCount: 23,
      createdBy: 'Legal Team'
    }
  ]

  const stats = [
    { label: t('totalTemplates'), value: '12', color: 'blue', icon: IconCar},
    { label: t('activeTemplates'), value: '8', color: 'green', icon: IconCheck },
    { label: t('draftTemplates'), value: '3', color: 'orange', icon: IconClock },
    { label: t('totalUsage'), value: '1,247', color: 'gray', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'draft': return 'orange'
      case 'archived': return 'gray'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'standard': return 'blue'
      case 'premium': return 'yellow'
      case 'corporate': return 'purple'
      case 'short-term': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.templateName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.templateType.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || template.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('contractTemplates')}</Title>
          <Text c="dimmed" size="sm">{t('manageContractTemplatesAndDocuments')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportTemplates')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('newTemplate')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="templates">{t('allTemplates')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('templateAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('outdatedTemplates')}</Text>
                    <Text size="xs">2 {t('templatesNeedUpdating')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('draftTemplates')}</Text>
                    <Text size="xs">3 {t('templatesInDraftStatus')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Popular Templates - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('popularTemplates')}</Title>
                
                <Stack gap="sm">
                  {templates.slice(0, 3).map((template) => (
                    <Paper key={template.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getTypeColor(template.templateType)} radius="xl" size="sm">
                            {getInitials(template.templateName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{template.templateName}</Text>
                            <Text size="xs" c="dimmed">{template.templateType} - v{template.version}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(template.status)} size="sm">
                            {template.usageCount} {t('uses')}
                          </Badge>
                          <Text size="xs" c="dimmed">{template.language}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="templates" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchTemplates')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'active', label: t('active') },
                    { value: 'draft', label: t('draft') },
                    { value: 'archived', label: t('archived') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Templates Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('templateName')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('language')}</Table.Th>
                  <Table.Th>{t('version')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('usage')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredTemplates.map((template) => (
                  <Table.Tr key={template.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getTypeColor(template.templateType)} radius="xl" size="sm">
                          {getInitials(template.templateName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{template.templateName}</Text>
                          <Text size="xs" c="dimmed">{t('createdBy')} {template.createdBy}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(template.templateType)} variant="light">
                        {t(template.templateType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{template.language}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">v{template.version}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(template.status)}>
                        {t(template.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{template.usageCount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconCopy size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('newContractTemplate')}
        size="md"
      >
        <Stack>
          <TextInput
            label={t('templateName')}
            placeholder={t('enterTemplateName')}
            required
          />

          <Select
            label={t('templateType')}
            placeholder={t('selectTemplateType')}
            data={[
              { value: 'standard', label: t('standard') },
              { value: 'premium', label: t('premium') },
              { value: 'corporate', label: t('corporate') },
              { value: 'short-term', label: t('shortTerm') }
            ]}
            required
          />

          <Select
            label={t('language')}
            placeholder={t('selectLanguage')}
            data={[
              { value: 'english', label: 'English' },
              { value: 'arabic', label: 'Arabic' },
              { value: 'spanish', label: 'Spanish' },
              { value: 'french', label: 'French' }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('createTemplate')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
