import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconPlus,
  IconSearch,IconEye,
  IconCheck,
  IconCar,
  IconUser,IconDownload,IconTrendingUp,
  IconChartBar
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface RevenueReport {
  id: string
  reportName: string
  reportType: 'monthly' | 'quarterly' | 'yearly' | 'custom'
  period: string
  totalRevenue: number
  currency: string
  revenueGrowth: number
  topSource: string
  topLocation: string
  customerCount: number
  averagePerCustomer: number
  generatedDate: string
  status: 'generated' | 'scheduled' | 'processing' | 'failed'
}

export function RevenueReportsSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const revenueReports: RevenueReport[] = [
    {
      id: '1',
      reportName: 'January 2024 Revenue Report',
      reportType: 'monthly',
      period: 'January 2024',
      totalRevenue: 125000,
      currency: 'AED',
      revenueGrowth: 13.6,
      topSource: 'Rental Fees',
      topLocation: 'Dubai',
      customerCount: 245,
      averagePerCustomer: 510,
      generatedDate: '2024-02-01',
      status: 'generated'
    },
    {
      id: '2',
      reportName: 'Q1 2024 Quarterly Report',
      reportType: 'quarterly',
      period: 'Q1 2024',
      totalRevenue: 367000,
      currency: 'AED',
      revenueGrowth: 18.2,
      topSource: 'Premium Rentals',
      topLocation: 'Abu Dhabi',
      customerCount: 678,
      averagePerCustomer: 541,
      generatedDate: '2024-04-01',
      status: 'scheduled'
    },
    {
      id: '3',
      reportName: 'Annual Revenue 2023',
      reportType: 'yearly',
      period: '2023',
      totalRevenue: 1456000,
      currency: 'AED',
      revenueGrowth: 22.4,
      topSource: 'Corporate Contracts',
      topLocation: 'Dubai',
      customerCount: 2847,
      averagePerCustomer: 511,
      generatedDate: '2024-01-15',
      status: 'generated'
    }
  ]

  const stats = [
    { label: t('totalReports'), value: '24', color: 'blue', icon: IconChartBar },
    { label: t('generatedReports'), value: '18', color: 'green', icon: IconCheck },
    { label: t('scheduledReports'), value: '4', color: 'orange', icon: IconCar},
    { label: t('totalRevenueTracked'), value: 'AED 2.1M', color: 'green', icon: IconTrendingUp }
  ]

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'monthly': return 'blue'
      case 'quarterly': return 'green'
      case 'yearly': return 'purple'
      case 'custom': return 'orange'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generated': return 'green'
      case 'scheduled': return 'blue'
      case 'processing': return 'orange'
      case 'failed': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredReports = revenueReports.filter(report => {
    const matchesSearch = report.reportName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         report.period.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesType = !typeFilter || report.reportType === typeFilter
    
    return matchesSearch && matchesType
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('revenueReports')}
        description={t('detailedFinancialReportingAndAnalysis')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportReports')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('generateReport')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="reports">{t('allReports')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Revenue Insights - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('revenueInsights')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconTrendingUp size={16} />} color="green">
                    <Text fw={500} size="sm">{t('strongPerformance')}</Text>
                    <Text size="xs">22.4% {t('yearOverYearGrowth')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconUser size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('customerValue')}</Text>
                    <Text size="xs">AED 511 {t('averageRevenuePerCustomer')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCar size={16} />} color="purple">
                    <Text fw={500} size="sm">{t('topRevenueSource')}</Text>
                    <Text size="xs">{t('corporateContractsLeading')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconChartBar size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('monthlyTrend')}</Text>
                    <Text size="xs">13.6% {t('growthThisMonth')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Reports - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentReports')}</Title>
                
                <Stack gap="sm">
                  {revenueReports.slice(0, 3).map((report) => (
                    <Paper key={report.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getTypeColor(report.reportType)} radius="xl" size="sm">
                            {getInitials(report.reportName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{report.reportName}</Text>
                            <Text size="xs" c="dimmed">{report.period} - {report.generatedDate}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(report.status)} size="sm">
                            {report.currency} {report.totalRevenue.toLocaleString()}
                          </Badge>
                          <Text size="xs" c="dimmed">+{report.revenueGrowth}%</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="reports" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchReports')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allTypes')}
                  data={[
                    { value: '', label: t('allTypes') },
                    { value: 'monthly', label: t('monthly') },
                    { value: 'quarterly', label: t('quarterly') },
                    { value: 'yearly', label: t('yearly') },
                    { value: 'custom', label: t('custom') }
                  ]}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Reports Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('reportName')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('period')}</Table.Th>
                  <Table.Th>{t('totalRevenue')}</Table.Th>
                  <Table.Th>{t('growth')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredReports.map((report) => (
                  <Table.Tr key={report.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getTypeColor(report.reportType)} radius="xl" size="sm">
                          {getInitials(report.reportName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{report.reportName}</Text>
                          <Text size="xs" c="dimmed">{t('generated')}: {report.generatedDate}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(report.reportType)} variant="light">
                        {t(report.reportType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{report.period}</Text>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{report.currency} {report.totalRevenue.toLocaleString()}</Text>
                        <Text size="xs" c="dimmed">{report.customerCount} {t('customers')}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={report.revenueGrowth > 0 ? 'green' : 'red'} variant="light">
                        +{report.revenueGrowth}%
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(report.status)}>
                        {t(report.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconDownload size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconChartBar size={12} />}>
                          {t('view')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('generateRevenueReport')}
        size="md"
      >
        <Stack>
          <TextInput
            label={t('reportName')}
            placeholder={t('enterReportName')}
            required
          />

          <Select
            label={t('reportType')}
            placeholder={t('selectReportType')}
            data={[
              { value: 'monthly', label: t('monthly') },
              { value: 'quarterly', label: t('quarterly') },
              { value: 'yearly', label: t('yearly') },
              { value: 'custom', label: t('custom') }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('currency')}
                placeholder={t('selectCurrency')}
                data={[
                  { value: 'AED', label: 'AED' },
                  { value: 'USD', label: 'USD' },
                  { value: 'EUR', label: 'EUR' }
                ]}
                defaultValue="AED"
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('location')}
                placeholder={t('selectLocation')}
                data={[
                  { value: 'all', label: t('allLocations') },
                  { value: 'dubai', label: 'Dubai' },
                  { value: 'abu-dhabi', label: 'Abu Dhabi' },
                  { value: 'sharjah', label: 'Sharjah' }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('generateReport')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
