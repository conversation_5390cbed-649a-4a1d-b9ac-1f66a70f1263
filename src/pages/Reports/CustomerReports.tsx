import { Container, Title, Text, Card, Group, Button, Stack, Grid, Select, Table, Badge, ActionIcon } from '@mantine/core'
import { IconDownload, IconUsers, IconTrendingUp, IconCalendar, IconEye, IconFilter } from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

interface CustomerReport {
  id: string
  customer_name: string
  total_rentals: number
  total_revenue: number
  last_rental: string
  status: 'active' | 'inactive' | 'vip'
  avg_rental_duration: number
  preferred_vehicle_type: string
}

export function CustomerReports() {
  const { t } = useTranslation()
  const [reportType, setReportType] = useState<string>('summary')
  const [dateRange, setDateRange] = useState<string>('last_30_days')

  // Mock data
  const customerReports: CustomerReport[] = [
    {
      id: '1',
      customer_name: '<PERSON>',
      total_rentals: 15,
      total_revenue: 12500,
      last_rental: '2024-01-20',
      status: 'vip',
      avg_rental_duration: 5.2,
      preferred_vehicle_type: 'Luxury'
    },
    {
      id: '2',
      customer_name: '<PERSON>',
      total_rentals: 8,
      total_revenue: 6800,
      last_rental: '2024-01-18',
      status: 'active',
      avg_rental_duration: 3.5,
      preferred_vehicle_type: 'Economy'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'vip': return 'gold'
      case 'active': return 'green'
      case 'inactive': return 'gray'
      default: return 'gray'
    }
  }

  const totalCustomers = customerReports.length
  const vipCustomers = customerReports.filter(c => c.status === 'vip').length
  const totalRevenue = customerReports.reduce((sum, c) => sum + c.total_revenue, 0)
  const avgRentalsPerCustomer = customerReports.reduce((sum, c) => sum + c.total_rentals, 0) / customerReports.length

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={2}>{t('customerReports')}</Title>
            <Text c="dimmed">{t('analyzeCustomerBehaviorAndPerformance')}</Text>
          </div>
          <Button leftSection={<IconDownload size={16} />}>
            {t('exportReport')}
          </Button>
        </Group>

        {/* Report Controls */}
        <Card withBorder p="md">
          <Group>
            <Select
              label={t('reportType')}
              placeholder={t('selectReportType')}
              data={[
                { value: 'summary', label: t('customerSummary') },
                { value: 'activity', label: t('customerActivity') },
                { value: 'revenue', label: t('revenueByCustomer') },
                { value: 'retention', label: t('customerRetention') }
              ]}
              value={reportType}
              onChange={(value) => setReportType(value || 'summary')}
            />
            <Select
              label={t('dateRange')}
              placeholder={t('selectDateRange')}
              data={[
                { value: 'last_7_days', label: t('last7Days') },
                { value: 'last_30_days', label: t('last30Days') },
                { value: 'last_90_days', label: t('last90Days') },
                { value: 'last_year', label: t('lastYear') },
                { value: 'custom', label: t('customRange') }
              ]}
              value={dateRange}
              onChange={(value) => setDateRange(value || 'last_30_days')}
            />
            <Button leftSection={<IconFilter size={16} />} variant="light">
              {t('generateReport')}
            </Button>
          </Group>
        </Card>

        {/* Stats Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalCustomers')}</Text>
                  <Text fw={700} size="xl">{totalCustomers}</Text>
                </div>
                <IconUsers size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('vipCustomers')}</Text>
                  <Text fw={700} size="xl">{vipCustomers}</Text>
                </div>
                <IconUsers size={24} color="gold" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalRevenue')}</Text>
                  <Text fw={700} size="xl">AED {totalRevenue.toLocaleString()}</Text>
                </div>
                <IconTrendingUp size={24} color="green" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('avgRentalsPerCustomer')}</Text>
                  <Text fw={700} size="xl">{avgRentalsPerCustomer.toFixed(1)}</Text>
                </div>
                <IconCalendar size={24} color="orange" />
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Customer Reports Table */}
        <Card withBorder>
          <Title order={4} mb="md">{t('customerPerformance')}</Title>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>{t('customer')}</Table.Th>
                <Table.Th>{t('totalRentals')}</Table.Th>
                <Table.Th>{t('totalRevenue')}</Table.Th>
                <Table.Th>{t('lastRental')}</Table.Th>
                <Table.Th>{t('avgDuration')}</Table.Th>
                <Table.Th>{t('preferredType')}</Table.Th>
                <Table.Th>{t('status')}</Table.Th>
                <Table.Th>{t('actions')}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {customerReports.map((customer) => (
                <Table.Tr key={customer.id}>
                  <Table.Td>
                    <Text fw={500}>{customer.customer_name}</Text>
                  </Table.Td>
                  <Table.Td>{customer.total_rentals}</Table.Td>
                  <Table.Td>AED {customer.total_revenue.toLocaleString()}</Table.Td>
                  <Table.Td>{customer.last_rental}</Table.Td>
                  <Table.Td>{customer.avg_rental_duration} days</Table.Td>
                  <Table.Td>{customer.preferred_vehicle_type}</Table.Td>
                  <Table.Td>
                    <Badge color={getStatusColor(customer.status)}>
                      {t(customer.status)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <ActionIcon variant="light">
                      <IconEye size={16} />
                    </ActionIcon>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>
      </Stack>
    </Container>
  )
}
