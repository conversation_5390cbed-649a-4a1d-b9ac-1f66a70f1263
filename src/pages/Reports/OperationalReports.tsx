import { Container, Title, Text, Card, Group, Button, <PERSON>ack, Grid, Select, Table, Badge, Progress } from '@mantine/core'
import { IconDownload, IconClipboardCheck, IconCar, IconUsers, IconClock, IconTrendingUp, IconAlertTriangle } from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

interface OperationalMetric {
  id: string
  metric_name: string
  current_value: number
  target_value: number
  unit: string
  trend: 'up' | 'down' | 'stable'
  status: 'good' | 'warning' | 'critical'
}

export function OperationalReports() {
  const { t } = useTranslation()
  const [reportPeriod, setReportPeriod] = useState<string>('monthly')
  const [departmentFilter, setDepartmentFilter] = useState<string>('all')

  // Mock data
  const operationalMetrics: OperationalMetric[] = [
    {
      id: '1',
      metric_name: 'Fleet Utilization Rate',
      current_value: 85,
      target_value: 90,
      unit: '%',
      trend: 'up',
      status: 'good'
    },
    {
      id: '2',
      metric_name: 'Average Check-in Time',
      current_value: 15,
      target_value: 10,
      unit: 'minutes',
      trend: 'down',
      status: 'warning'
    },
    {
      id: '3',
      metric_name: 'Customer Satisfaction',
      current_value: 4.2,
      target_value: 4.5,
      unit: '/5',
      trend: 'stable',
      status: 'good'
    },
    {
      id: '4',
      metric_name: 'Maintenance Compliance',
      current_value: 95,
      target_value: 100,
      unit: '%',
      trend: 'up',
      status: 'good'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'green'
      case 'warning': return 'yellow'
      case 'critical': return 'red'
      default: return 'gray'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <IconTrendingUp size={16} color="green" />
      case 'down': return <IconTrendingUp size={16} color="red" style={{ transform: 'rotate(180deg)' }} />
      case 'stable': return <IconTrendingUp size={16} color="gray" style={{ transform: 'rotate(90deg)' }} />
      default: return null
    }
  }

  const getProgressValue = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100)
  }

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={2}>{t('operationalReports')}</Title>
            <Text c="dimmed">{t('monitorOperationalPerformanceMetrics')}</Text>
          </div>
          <Button leftSection={<IconDownload size={16} />}>
            {t('exportReport')}
          </Button>
        </Group>

        {/* Report Controls */}
        <Card withBorder p="md">
          <Group>
            <Select
              label={t('reportPeriod')}
              placeholder={t('selectPeriod')}
              data={[
                { value: 'daily', label: t('daily') },
                { value: 'weekly', label: t('weekly') },
                { value: 'monthly', label: t('monthly') },
                { value: 'quarterly', label: t('quarterly') },
                { value: 'yearly', label: t('yearly') }
              ]}
              value={reportPeriod}
              onChange={(value) => setReportPeriod(value || 'monthly')}
            />
            <Select
              label={t('department')}
              placeholder={t('selectDepartment')}
              data={[
                { value: 'all', label: t('allDepartments') },
                { value: 'operations', label: t('operations') },
                { value: 'fleet', label: t('fleetManagement') },
                { value: 'customer_service', label: t('customerService') },
                { value: 'maintenance', label: t('maintenance') }
              ]}
              value={departmentFilter}
              onChange={(value) => setDepartmentFilter(value || 'all')}
            />
          </Group>
        </Card>

        {/* Key Performance Indicators */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('activeRentals')}</Text>
                  <Text fw={700} size="xl">127</Text>
                </div>
                <IconCar size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('dailyCheckIns')}</Text>
                  <Text fw={700} size="xl">23</Text>
                </div>
                <IconClipboardCheck size={24} color="green" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('pendingMaintenance')}</Text>
                  <Text fw={700} size="xl">8</Text>
                </div>
                <IconAlertTriangle size={24} color="orange" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('avgResponseTime')}</Text>
                  <Text fw={700} size="xl">12 min</Text>
                </div>
                <IconClock size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Operational Metrics Table */}
        <Card withBorder>
          <Title order={4} mb="md">{t('performanceMetrics')}</Title>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>{t('metric')}</Table.Th>
                <Table.Th>{t('currentValue')}</Table.Th>
                <Table.Th>{t('target')}</Table.Th>
                <Table.Th>{t('progress')}</Table.Th>
                <Table.Th>{t('trend')}</Table.Th>
                <Table.Th>{t('status')}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {operationalMetrics.map((metric) => (
                <Table.Tr key={metric.id}>
                  <Table.Td>
                    <Text fw={500}>{metric.metric_name}</Text>
                  </Table.Td>
                  <Table.Td>
                    {metric.current_value}{metric.unit}
                  </Table.Td>
                  <Table.Td>
                    {metric.target_value}{metric.unit}
                  </Table.Td>
                  <Table.Td>
                    <Progress
                      value={getProgressValue(metric.current_value, metric.target_value)}
                      color={getStatusColor(metric.status)}
                      size="sm"
                    />
                  </Table.Td>
                  <Table.Td>
                    {getTrendIcon(metric.trend)}
                  </Table.Td>
                  <Table.Td>
                    <Badge color={getStatusColor(metric.status)}>
                      {t(metric.status)}
                    </Badge>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>

        {/* Department Performance */}
        <Grid>
          <Grid.Col span={6}>
            <Card withBorder>
              <Title order={5} mb="md">{t('departmentEfficiency')}</Title>
              <Stack gap="sm">
                <Group justify="space-between">
                  <Text size="sm">{t('operations')}</Text>
                  <Badge color="green">92%</Badge>
                </Group>
                <Progress value={92} color="green" size="sm" />
                
                <Group justify="space-between">
                  <Text size="sm">{t('fleetManagement')}</Text>
                  <Badge color="yellow">78%</Badge>
                </Group>
                <Progress value={78} color="yellow" size="sm" />
                
                <Group justify="space-between">
                  <Text size="sm">{t('customerService')}</Text>
                  <Badge color="green">95%</Badge>
                </Group>
                <Progress value={95} color="green" size="sm" />
                
                <Group justify="space-between">
                  <Text size="sm">{t('maintenance')}</Text>
                  <Badge color="green">88%</Badge>
                </Group>
                <Progress value={88} color="green" size="sm" />
              </Stack>
            </Card>
          </Grid.Col>
          
          <Grid.Col span={6}>
            <Card withBorder>
              <Title order={5} mb="md">{t('issuesSummary')}</Title>
              <Stack gap="sm">
                <Group justify="space-between">
                  <Text size="sm" c="red">{t('criticalIssues')}</Text>
                  <Badge color="red">2</Badge>
                </Group>
                <Group justify="space-between">
                  <Text size="sm" c="orange">{t('warningIssues')}</Text>
                  <Badge color="orange">5</Badge>
                </Group>
                <Group justify="space-between">
                  <Text size="sm" c="blue">{t('infoIssues')}</Text>
                  <Badge color="blue">12</Badge>
                </Group>
                <Group justify="space-between">
                  <Text size="sm" c="green">{t('resolvedToday')}</Text>
                  <Badge color="green">8</Badge>
                </Group>
              </Stack>
            </Card>
          </Grid.Col>
        </Grid>
      </Stack>
    </Container>
  )
}
