import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar,
  Progress
} from '@mantine/core'
import {
  IconPlus,
  IconSearch,IconEye,
  IconCheck,
  IconCar,IconAlertTriangle,
  IconDownload,IconTrendingUp,
  IconChartBar
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface VehicleUtilization {
  id: string
  vehicleName: string
  plateNumber: string
  category: 'economy' | 'compact' | 'standard' | 'premium' | 'luxury' | 'suv'
  utilizationRate: number
  totalDays: number
  rentalDays: number
  idleDays: number
  maintenanceDays: number
  revenue: number
  currency: string
  location: string
  status: 'excellent' | 'good' | 'average' | 'poor'
}

export function VehicleUtilizationSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const utilization: VehicleUtilization[] = [
    {
      id: '1',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      category: 'standard',
      utilizationRate: 87,
      totalDays: 30,
      rentalDays: 26,
      idleDays: 3,
      maintenanceDays: 1,
      revenue: 3120,
      currency: 'AED',
      location: 'Dubai',
      status: 'excellent'
    },
    {
      id: '2',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      category: 'premium',
      utilizationRate: 73,
      totalDays: 30,
      rentalDays: 22,
      idleDays: 6,
      maintenanceDays: 2,
      revenue: 4400,
      currency: 'AED',
      location: 'Abu Dhabi',
      status: 'good'
    },
    {
      id: '3',
      vehicleName: 'Nissan Altima 2023',
      plateNumber: 'DEF-789',
      category: 'economy',
      utilizationRate: 45,
      totalDays: 30,
      rentalDays: 14,
      idleDays: 14,
      maintenanceDays: 2,
      revenue: 1680,
      currency: 'AED',
      location: 'Sharjah',
      status: 'poor'
    }
  ]

  const stats = [
    { label: t('averageUtilization'), value: '68%', color: 'blue', icon: IconCar },
    { label: t('topPerformers'), value: '12', color: 'green', icon: IconTrendingUp },
    { label: t('underutilized'), value: '8', color: 'orange', icon: IconAlertTriangle },
    { label: t('totalFleetRevenue'), value: 'AED 156K', color: 'green', icon: IconChartBar }
  ]

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'economy': return 'blue'
      case 'compact': return 'green'
      case 'standard': return 'yellow'
      case 'premium': return 'orange'
      case 'luxury': return 'purple'
      case 'suv': return 'red'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'green'
      case 'good': return 'blue'
      case 'average': return 'orange'
      case 'poor': return 'red'
      default: return 'gray'
    }
  }

  const getUtilizationColor = (rate: number) => {
    if (rate >= 80) return 'green'
    if (rate >= 60) return 'blue'
    if (rate >= 40) return 'orange'
    return 'red'
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredUtilization = utilization.filter(vehicle => {
    const matchesSearch = vehicle.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         vehicle.plateNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = !categoryFilter || vehicle.category === categoryFilter
    
    return matchesSearch && matchesCategory
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('vehicleUtilization')}
        description={t('fleetPerformanceAnalyticsAndUtilization')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportUtilization')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('utilizationReport')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="utilization">{t('vehicleUtilization')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Utilization Insights - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('utilizationInsights')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconTrendingUp size={16} />} color="green">
                    <Text fw={500} size="sm">{t('topPerformers')}</Text>
                    <Text size="xs">12 {t('vehiclesAbove80Percent')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCar size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('fleetAverage')}</Text>
                    <Text size="xs">68% {t('overallUtilizationRate')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('underutilized')}</Text>
                    <Text size="xs">8 {t('vehiclesBelow50Percent')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconChartBar size={16} />} color="purple">
                    <Text fw={500} size="sm">{t('revenueImpact')}</Text>
                    <Text size="xs">{t('premiumVehiclesGenerateMore')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Top Performers - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('topPerformingVehicles')}</Title>
                
                <Stack gap="sm">
                  {utilization.slice(0, 3).map((vehicle) => (
                    <Paper key={vehicle.id} p="sm" withBorder>
                      <Group justify="space-between" mb="xs">
                        <Group>
                          <Avatar color={getCategoryColor(vehicle.category)} radius="xl" size="sm">
                            {getInitials(vehicle.vehicleName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{vehicle.vehicleName}</Text>
                            <Text size="xs" c="dimmed">{vehicle.plateNumber} - {vehicle.location}</Text>
                          </div>
                        </Group>
                        <Badge color={getStatusColor(vehicle.status)} size="sm">
                          {vehicle.utilizationRate}%
                        </Badge>
                      </Group>
                      
                      <Progress 
                        value={vehicle.utilizationRate} 
                        color={getUtilizationColor(vehicle.utilizationRate)}
                        size="sm"
                        mb="xs"
                      />
                      
                      <Group justify="space-between">
                        <Text size="xs" c="dimmed">
                          {vehicle.rentalDays}/{vehicle.totalDays} {t('days')}
                        </Text>
                        <Text size="xs" fw={500}>
                          {vehicle.currency} {vehicle.revenue}
                        </Text>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="utilization" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchVehicles')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allCategories')}
                  data={[
                    { value: '', label: t('allCategories') },
                    { value: 'economy', label: t('economy') },
                    { value: 'compact', label: t('compact') },
                    { value: 'standard', label: t('standard') },
                    { value: 'premium', label: t('premium') },
                    { value: 'luxury', label: t('luxury') },
                    { value: 'suv', label: t('suv') }
                  ]}
                  value={categoryFilter}
                  onChange={(value) => setCategoryFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Utilization Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('category')}</Table.Th>
                  <Table.Th>{t('utilizationRate')}</Table.Th>
                  <Table.Th>{t('rentalDays')}</Table.Th>
                  <Table.Th>{t('revenue')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredUtilization.map((vehicle) => (
                  <Table.Tr key={vehicle.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getCategoryColor(vehicle.category)} radius="xl" size="sm">
                          {getInitials(vehicle.vehicleName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{vehicle.vehicleName}</Text>
                          <Text size="xs" c="dimmed">{vehicle.plateNumber} - {vehicle.location}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getCategoryColor(vehicle.category)} variant="light">
                        {t(vehicle.category)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Group gap="xs" mb="xs">
                          <Text fw={500} size="sm">{vehicle.utilizationRate}%</Text>
                        </Group>
                        <Progress 
                          value={vehicle.utilizationRate} 
                          color={getUtilizationColor(vehicle.utilizationRate)}
                          size="xs"
                        />
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{vehicle.rentalDays}/{vehicle.totalDays}</Text>
                        <Text size="xs" c="dimmed">{vehicle.idleDays} {t('idle')}, {vehicle.maintenanceDays} {t('maintenance')}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{vehicle.currency} {vehicle.revenue}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(vehicle.status)}>
                        {t(vehicle.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconChartBar size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconCar size={12} />}>
                          {t('optimize')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('generateUtilizationReport')}
        size="md"
      >
        <Stack>
          <Select
            label={t('reportPeriod')}
            placeholder={t('selectReportPeriod')}
            data={[
              { value: 'current-month', label: t('currentMonth') },
              { value: 'last-month', label: t('lastMonth') },
              { value: 'quarter', label: t('currentQuarter') },
              { value: 'year', label: t('currentYear') }
            ]}
            required
          />

          <Select
            label={t('vehicleCategory')}
            placeholder={t('selectVehicleCategory')}
            data={[
              { value: 'all', label: t('allCategories') },
              { value: 'economy', label: t('economy') },
              { value: 'premium', label: t('premium') },
              { value: 'luxury', label: t('luxury') }
            ]}
            required
          />

          <Select
            label={t('location')}
            placeholder={t('selectLocation')}
            data={[
              { value: 'all', label: t('allLocations') },
              { value: 'dubai', label: 'Dubai' },
              { value: 'abu-dhabi', label: 'Abu Dhabi' },
              { value: 'sharjah', label: 'Sharjah' }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('generateReport')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
