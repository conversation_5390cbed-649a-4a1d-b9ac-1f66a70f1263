import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar,
  Progress
} from '@mantine/core'
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconEye,
  IconCheck,
  IconCar,
  IconUser,IconAlertTriangle,
  IconDownload,IconDashboard,
  IconTrendingUp,
  IconTrendingDown
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface AnalyticsMetric {
  id: string
  metricName: string
  category: 'revenue' | 'operations' | 'fleet' | 'customers'
  currentValue: number
  previousValue: number
  unit: string
  changePercent: number
  trend: 'up' | 'down' | 'stable'
  period: string
  target: number
  status: 'excellent' | 'good' | 'warning' | 'critical'
}

export function DashboardAnalyticsSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const metrics: AnalyticsMetric[] = [
    {
      id: '1',
      metricName: 'Total Revenue',
      category: 'revenue',
      currentValue: 125000,
      previousValue: 110000,
      unit: 'AED',
      changePercent: 13.6,
      trend: 'up',
      period: 'This Month',
      target: 130000,
      status: 'good'
    },
    {
      id: '2',
      metricName: 'Fleet Utilization',
      category: 'fleet',
      currentValue: 87,
      previousValue: 82,
      unit: '%',
      changePercent: 6.1,
      trend: 'up',
      period: 'This Month',
      target: 90,
      status: 'good'
    },
    {
      id: '3',
      metricName: 'Customer Satisfaction',
      category: 'customers',
      currentValue: 4.6,
      previousValue: 4.8,
      unit: '/5',
      changePercent: -4.2,
      trend: 'down',
      period: 'This Month',
      target: 4.5,
      status: 'warning'
    },
    {
      id: '4',
      metricName: 'Active Rentals',
      category: 'operations',
      currentValue: 156,
      previousValue: 142,
      unit: 'vehicles',
      changePercent: 9.9,
      trend: 'up',
      period: 'Today',
      target: 160,
      status: 'excellent'
    }
  ]

  const stats = [
    { label: t('totalMetrics'), value: '24', color: 'blue', icon: IconDashboard },
    { label: t('positiveMetrics'), value: '18', color: 'green', icon: IconTrendingUp },
    { label: t('warningMetrics'), value: '4', color: 'orange', icon: IconAlertTriangle },
    { label: t('criticalMetrics'), value: '2', color: 'red', icon: IconTrendingDown }
  ]

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'revenue': return 'green'
      case 'operations': return 'blue'
      case 'fleet': return 'orange'
      case 'customers': return 'purple'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'green'
      case 'good': return 'blue'
      case 'warning': return 'orange'
      case 'critical': return 'red'
      default: return 'gray'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <IconTrendingUp size={14} color="green" />
      case 'down': return <IconTrendingDown size={14} color="red" />
      default: return <IconTrendingUp size={14} color="gray" />
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredMetrics = metrics.filter(metric => {
    const matchesSearch = metric.metricName.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = !categoryFilter || metric.category === categoryFilter
    
    return matchesSearch && matchesCategory
  })

  const getProgressValue = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100)
  }

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('dashboardAnalytics')}</Title>
          <Text c="dimmed" size="sm">{t('comprehensiveBusinessAnalyticsAndMetrics')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportAnalytics')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('customMetric')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="metrics">{t('allMetrics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Key Performance Indicators */}
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('keyPerformanceIndicators')}</Title>
                
                <Stack gap="md">
                  {metrics.map((metric) => (
                    <Paper key={metric.id} p="md" withBorder>
                      <Group justify="space-between" mb="xs">
                        <Group>
                          <Badge color={getCategoryColor(metric.category)} variant="light">
                            {t(metric.category)}
                          </Badge>
                          <Text fw={500} size="sm">{metric.metricName}</Text>
                        </Group>
                        <Group gap="xs">
                          {getTrendIcon(metric.trend)}
                          <Text size="sm" c={metric.changePercent > 0 ? 'green' : 'red'}>
                            {metric.changePercent > 0 ? '+' : ''}{metric.changePercent}%
                          </Text>
                        </Group>
                      </Group>
                      
                      <Group justify="space-between" mb="xs">
                        <Text size="lg" fw={700}>
                          {metric.unit === 'AED' ? `${metric.unit} ${metric.currentValue.toLocaleString()}` : 
                           `${metric.currentValue}${metric.unit}`}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {t('target')}: {metric.unit === 'AED' ? `${metric.unit} ${metric.target.toLocaleString()}` : 
                           `${metric.target}${metric.unit}`}
                        </Text>
                      </Group>
                      
                      <Progress 
                        value={getProgressValue(metric.currentValue, metric.target)} 
                        color={getStatusColor(metric.status)}
                        size="sm"
                      />
                      
                      <Group justify="space-between" mt="xs">
                        <Text size="xs" c="dimmed">{metric.period}</Text>
                        <Badge color={getStatusColor(metric.status)} size="sm">
                          {t(metric.status)}
                        </Badge>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>

            {/* Analytics Insights */}
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('analyticsInsights')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconTrendingUp size={16} />} color="green">
                    <Text fw={500} size="sm">{t('revenueGrowth')}</Text>
                    <Text size="xs">13.6% {t('increaseThisMonth')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCar size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('fleetPerformance')}</Text>
                    <Text size="xs">87% {t('utilizationRate')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('attentionNeeded')}</Text>
                    <Text size="xs">{t('customerSatisfactionDeclining')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconUser size={16} />} color="purple">
                    <Text fw={500} size="sm">{t('operationalExcellence')}</Text>
                    <Text size="xs">156 {t('activeRentalsToday')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="metrics" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchMetrics')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allCategories')}
                  data={[
                    { value: '', label: t('allCategories') },
                    { value: 'revenue', label: t('revenue') },
                    { value: 'operations', label: t('operations') },
                    { value: 'fleet', label: t('fleet') },
                    { value: 'customers', label: t('customers') }
                  ]}
                  value={categoryFilter}
                  onChange={(value) => setCategoryFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Metrics Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('metric')}</Table.Th>
                  <Table.Th>{t('category')}</Table.Th>
                  <Table.Th>{t('currentValue')}</Table.Th>
                  <Table.Th>{t('target')}</Table.Th>
                  <Table.Th>{t('change')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredMetrics.map((metric) => (
                  <Table.Tr key={metric.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getCategoryColor(metric.category)} radius="xl" size="sm">
                          {getInitials(metric.metricName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{metric.metricName}</Text>
                          <Text size="xs" c="dimmed">{metric.period}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getCategoryColor(metric.category)} variant="light">
                        {t(metric.category)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">
                        {metric.unit === 'AED' ? `${metric.unit} ${metric.currentValue.toLocaleString()}` : 
                         `${metric.currentValue}${metric.unit}`}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">
                        {metric.unit === 'AED' ? `${metric.unit} ${metric.target.toLocaleString()}` : 
                         `${metric.target}${metric.unit}`}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        {getTrendIcon(metric.trend)}
                        <Text size="sm" c={metric.changePercent > 0 ? 'green' : 'red'}>
                          {metric.changePercent > 0 ? '+' : ''}{metric.changePercent}%
                        </Text>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(metric.status)}>
                        {t(metric.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconDashboard size={12} />}>
                          {t('details')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('createCustomMetric')}
        size="md"
      >
        <Stack>
          <TextInput
            label={t('metricName')}
            placeholder={t('enterMetricName')}
            required
          />

          <Select
            label={t('category')}
            placeholder={t('selectCategory')}
            data={[
              { value: 'revenue', label: t('revenue') },
              { value: 'operations', label: t('operations') },
              { value: 'fleet', label: t('fleet') },
              { value: 'customers', label: t('customers') }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('targetValue')}
                placeholder={t('enterTargetValue')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('unit')}
                placeholder={t('selectUnit')}
                data={[
                  { value: 'AED', label: 'AED' },
                  { value: '%', label: 'Percentage' },
                  { value: 'count', label: 'Count' },
                  { value: '/5', label: 'Rating' }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('createMetric')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
