import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconPlus,
  IconSearch,
  IconTags
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface PricingRule {
  id: string
  ruleName: string
  vehicleCategory: 'economy' | 'compact' | 'standard' | 'premium' | 'luxury' | 'suv'
  rateType: 'daily' | 'weekly' | 'monthly' | 'hourly'
  baseRate: number
  currency: string
  seasonType: 'peak' | 'regular' | 'off-peak'
  validFrom: string
  validTo: string
  status: 'active' | 'inactive' | 'scheduled' | 'expired'
  discountPercent: number
  minimumDays: number
  location: string
}

export function PricingSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const pricingRules: PricingRule[] = [
    {
      id: '1',
      ruleName: 'Economy Daily Rate',
      vehicleCategory: 'economy',
      rateType: 'daily',
      baseRate: 120,
      currency: 'AED',
      seasonType: 'regular',
      validFrom: '2024-01-01',
      validTo: '2024-12-31',
      status: 'active',
      discountPercent: 0,
      minimumDays: 1,
      location: 'Dubai'
    },
    {
      id: '2',
      ruleName: 'Premium Weekly Rate',
      vehicleCategory: 'premium',
      rateType: 'weekly',
      baseRate: 1800,
      currency: 'AED',
      seasonType: 'peak',
      validFrom: '2024-06-01',
      validTo: '2024-08-31',
      status: 'scheduled',
      discountPercent: 10,
      minimumDays: 7,
      location: 'Abu Dhabi'
    },
    {
      id: '3',
      ruleName: 'Luxury Monthly Rate',
      vehicleCategory: 'luxury',
      rateType: 'monthly',
      baseRate: 6500,
      currency: 'AED',
      seasonType: 'off-peak',
      validFrom: '2024-01-01',
      validTo: '2024-05-31',
      status: 'active',
      discountPercent: 15,
      minimumDays: 30,
      location: 'Sharjah'
    }
  ]

  const stats = [
    { label: t('totalPricingRules'), value: '45', color: 'blue', icon: IconTags },
    { label: t('activePricingRules'), value: '38', color: 'green', icon: IconCheck },
    { label: t('scheduledPricingRules'), value: '5', color: 'orange', icon: IconClock },
    { label: t('averageDailyRate'), value: 'AED 185', color: 'green', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'inactive': return 'gray'
      case 'scheduled': return 'orange'
      case 'expired': return 'red'
      default: return 'gray'
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'economy': return 'blue'
      case 'compact': return 'green'
      case 'standard': return 'yellow'
      case 'premium': return 'orange'
      case 'luxury': return 'purple'
      case 'suv': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredRules = pricingRules.filter(rule => {
    const matchesSearch = rule.ruleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         rule.vehicleCategory.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || rule.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('pricing')}
        description={t('managePricingRulesAndRates')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportPricing')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('createPricingRule')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="pricing">{t('allPricingRules')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('pricingAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('expiredPricingRules')}</Text>
                    <Text size="xs">2 {t('pricingRulesHaveExpired')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('scheduledPricingRules')}</Text>
                    <Text size="xs">5 {t('pricingRulesScheduledToStart')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconTags size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('seasonalRates')}</Text>
                    <Text size="xs">12 {t('seasonalRatesActive')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Popular Pricing Rules - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('popularPricingRules')}</Title>
                
                <Stack gap="sm">
                  {pricingRules.slice(0, 3).map((rule) => (
                    <Paper key={rule.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getCategoryColor(rule.vehicleCategory)} radius="xl" size="sm">
                            {getInitials(rule.ruleName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{rule.ruleName}</Text>
                            <Text size="xs" c="dimmed">{t(rule.vehicleCategory)} - {t(rule.rateType)}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(rule.status)} size="sm">
                            {rule.currency} {rule.baseRate}
                          </Badge>
                          <Text size="xs" c="dimmed">{t(rule.seasonType)}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="pricing" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchPricingRules')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'active', label: t('active') },
                    { value: 'inactive', label: t('inactive') },
                    { value: 'scheduled', label: t('scheduled') },
                    { value: 'expired', label: t('expired') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Pricing Rules Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('ruleName')}</Table.Th>
                  <Table.Th>{t('category')}</Table.Th>
                  <Table.Th>{t('rate')}</Table.Th>
                  <Table.Th>{t('season')}</Table.Th>
                  <Table.Th>{t('validity')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredRules.map((rule) => (
                  <Table.Tr key={rule.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getCategoryColor(rule.vehicleCategory)} radius="xl" size="sm">
                          {getInitials(rule.ruleName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{rule.ruleName}</Text>
                          <Text size="xs" c="dimmed">{rule.location}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getCategoryColor(rule.vehicleCategory)} variant="light">
                        {t(rule.vehicleCategory)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{rule.currency} {rule.baseRate}</Text>
                        <Text size="xs" c="dimmed">{t(rule.rateType)}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={rule.seasonType === 'peak' ? 'red' : rule.seasonType === 'regular' ? 'blue' : 'green'} variant="light">
                        {t(rule.seasonType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{rule.validFrom} - {rule.validTo}</Text>
                        <Text size="xs" c="dimmed">{t('minDays')}: {rule.minimumDays}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(rule.status)}>
                        {t(rule.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconTags size={12} />}>
                          {t('duplicate')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('createPricingRule')}
        size="md"
      >
        <Stack>
          <TextInput
            label={t('ruleName')}
            placeholder={t('enterRuleName')}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('vehicleCategory')}
                placeholder={t('selectVehicleCategory')}
                data={[
                  { value: 'economy', label: t('economy') },
                  { value: 'compact', label: t('compact') },
                  { value: 'standard', label: t('standard') },
                  { value: 'premium', label: t('premium') },
                  { value: 'luxury', label: t('luxury') },
                  { value: 'suv', label: t('suv') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('rateType')}
                placeholder={t('selectRateType')}
                data={[
                  { value: 'hourly', label: t('hourly') },
                  { value: 'daily', label: t('daily') },
                  { value: 'weekly', label: t('weekly') },
                  { value: 'monthly', label: t('monthly') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('baseRate')}
                placeholder={t('enterBaseRate')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('currency')}
                placeholder={t('selectCurrency')}
                data={[
                  { value: 'AED', label: 'AED' },
                  { value: 'USD', label: 'USD' },
                  { value: 'EUR', label: 'EUR' }
                ]}
                defaultValue="AED"
                required
              />
            </Grid.Col>
          </Grid>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('createPricingRule')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
