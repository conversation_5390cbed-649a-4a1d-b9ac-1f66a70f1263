import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconCreditCard,
  IconDownload,
  IconEdit,
  IconEye,
  IconPlus,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface Payment {
  id: string
  paymentId: string
  customerName: string
  contractNumber: string
  paymentMethod: 'credit-card' | 'debit-card' | 'cash' | 'bank-transfer' | 'digital-wallet'
  paymentType: 'rental-fee' | 'deposit' | 'late-fee' | 'damage-fee' | 'fuel-charge'
  amount: number
  currency: string
  paymentDate: string
  dueDate: string
  status: 'completed' | 'pending' | 'failed' | 'refunded' | 'disputed'
  transactionId: string
  location: string
}

export function PaymentsSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const payments: Payment[] = [
    {
      id: '1',
      paymentId: 'PAY-2024-001',
      customerName: 'Ahmed Al-Rashid',
      contractNumber: 'CON-2024-001',
      paymentMethod: 'credit-card',
      paymentType: 'rental-fee',
      amount: 600,
      currency: 'AED',
      paymentDate: '2024-01-15',
      dueDate: '2024-01-15',
      status: 'completed',
      transactionId: 'TXN-789123456',
      location: 'Dubai Airport'
    },
    {
      id: '2',
      paymentId: 'PAY-2024-002',
      customerName: 'Sarah Johnson',
      contractNumber: 'CON-2024-002',
      paymentMethod: 'digital-wallet',
      paymentType: 'deposit',
      amount: 1500,
      currency: 'AED',
      paymentDate: '2024-01-20',
      dueDate: '2024-01-20',
      status: 'pending',
      transactionId: 'TXN-*********',
      location: 'Abu Dhabi'
    },
    {
      id: '3',
      paymentId: 'PAY-2024-003',
      customerName: 'Mohammed Hassan',
      contractNumber: 'CON-2024-003',
      paymentMethod: 'bank-transfer',
      paymentType: 'late-fee',
      amount: 150,
      currency: 'AED',
      paymentDate: '2024-01-18',
      dueDate: '2024-01-17',
      status: 'failed',
      transactionId: 'TXN-*********',
      location: 'Sharjah'
    }
  ]

  const stats = [
    { label: t('totalPayments'), value: '1,247', color: 'blue', icon: IconCreditCard },
    { label: t('completedPayments'), value: '1,156', color: 'green', icon: IconCheck },
    { label: t('pendingPayments'), value: '67', color: 'orange', icon: IconClock },
    { label: t('totalRevenue'), value: 'AED 456,780', color: 'green', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'pending': return 'orange'
      case 'failed': return 'red'
      case 'refunded': return 'blue'
      case 'disputed': return 'gray'
      default: return 'gray'
    }
  }

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'credit-card': return 'blue'
      case 'debit-card': return 'green'
      case 'cash': return 'yellow'
      case 'bank-transfer': return 'purple'
      case 'digital-wallet': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.paymentId.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || payment.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('payments')}</Title>
          <Text c="dimmed" size="sm">{t('managePaymentsAndTransactions')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportPayments')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('recordPayment')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="payments">{t('allPayments')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('paymentAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('failedPayments')}</Text>
                    <Text size="xs">12 {t('paymentsRequireAttention')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('pendingPayments')}</Text>
                    <Text size="xs">67 {t('paymentsAwaitingProcessing')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCreditCard size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('disputedPayments')}</Text>
                    <Text size="xs">3 {t('paymentsUnderDispute')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Payments - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentPayments')}</Title>
                
                <Stack gap="sm">
                  {payments.slice(0, 3).map((payment) => (
                    <Paper key={payment.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getMethodColor(payment.paymentMethod)} radius="xl" size="sm">
                            {getInitials(payment.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{payment.customerName}</Text>
                            <Text size="xs" c="dimmed">{t(payment.paymentType)} - {payment.paymentDate}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(payment.status)} size="sm">
                            {payment.currency} {payment.amount}
                          </Badge>
                          <Text size="xs" c="dimmed">{t(payment.paymentMethod)}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="payments" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchPayments')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'completed', label: t('completed') },
                    { value: 'pending', label: t('pending') },
                    { value: 'failed', label: t('failed') },
                    { value: 'refunded', label: t('refunded') },
                    { value: 'disputed', label: t('disputed') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Payments Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('paymentDetails')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('method')}</Table.Th>
                  <Table.Th>{t('date')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredPayments.map((payment) => (
                  <Table.Tr key={payment.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getMethodColor(payment.paymentMethod)} radius="xl" size="sm">
                          {getInitials(payment.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{payment.customerName}</Text>
                          <Text size="xs" c="dimmed">{payment.contractNumber}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{payment.paymentId}</Text>
                        <Badge color={getMethodColor(payment.paymentMethod)} variant="light" size="xs">
                          {t(payment.paymentType)}
                        </Badge>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{payment.currency} {payment.amount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getMethodColor(payment.paymentMethod)} variant="light">
                        {t(payment.paymentMethod)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{payment.paymentDate}</Text>
                        <Text size="xs" c="dimmed">{t('due')}: {payment.dueDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(payment.status)}>
                        {t(payment.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconDownload size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconCreditCard size={12} />}>
                          {t('refund')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('recordPayment')}
        size="md"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid - CON-2024-001' },
              { value: '2', label: 'Sarah Johnson - CON-2024-002' },
              { value: '3', label: 'Mohammed Hassan - CON-2024-003' }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('amount')}
                placeholder={t('enterAmount')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('currency')}
                placeholder={t('selectCurrency')}
                data={[
                  { value: 'AED', label: 'AED' },
                  { value: 'USD', label: 'USD' },
                  { value: 'EUR', label: 'EUR' }
                ]}
                defaultValue="AED"
                required
              />
            </Grid.Col>
          </Grid>

          <Select
            label={t('paymentMethod')}
            placeholder={t('selectPaymentMethod')}
            data={[
              { value: 'credit-card', label: t('creditCard') },
              { value: 'debit-card', label: t('debitCard') },
              { value: 'cash', label: t('cash') },
              { value: 'bank-transfer', label: t('bankTransfer') },
              { value: 'digital-wallet', label: t('digitalWallet') }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('recordPayment')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
