import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconPlus,
  IconSearch,IconEye,
  IconCheck,
  IconCar,
  IconUser,IconDownload,IconTrendingUp
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface RevenueRecord {
  id: string
  period: string
  revenueSource: 'rental-fees' | 'deposits' | 'late-fees' | 'damage-fees' | 'fuel-charges' | 'insurance'
  amount: number
  currency: string
  vehicleCategory: string
  location: string
  customerCount: number
  averagePerCustomer: number
  growthPercent: number
  status: 'confirmed' | 'pending' | 'projected'
}

export function RevenueSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [sourceFilter, setSourceFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const revenueRecords: RevenueRecord[] = [
    {
      id: '1',
      period: 'January 2024',
      revenueSource: 'rental-fees',
      amount: 125000,
      currency: 'AED',
      vehicleCategory: 'All Categories',
      location: 'Dubai',
      customerCount: 245,
      averagePerCustomer: 510,
      growthPercent: 12.5,
      status: 'confirmed'
    },
    {
      id: '2',
      period: 'January 2024',
      revenueSource: 'deposits',
      amount: 45000,
      currency: 'AED',
      vehicleCategory: 'Premium',
      location: 'Abu Dhabi',
      customerCount: 89,
      averagePerCustomer: 506,
      growthPercent: 8.3,
      status: 'confirmed'
    },
    {
      id: '3',
      period: 'February 2024',
      revenueSource: 'late-fees',
      amount: 8500,
      currency: 'AED',
      vehicleCategory: 'All Categories',
      location: 'Sharjah',
      customerCount: 23,
      averagePerCustomer: 370,
      growthPercent: -5.2,
      status: 'projected'
    }
  ]

  const stats = [
    { label: t('totalRevenue'), value: 'AED 1,247,890', color: 'green', icon: IconTrendingUp },
    { label: t('monthlyGrowth'), value: '+12.5%', color: 'blue', icon: IconTrendingUp },
    { label: t('averagePerCustomer'), value: 'AED 485', color: 'orange', icon: IconUser },
    { label: t('revenueStreams'), value: '6', color: 'purple', icon: IconCar }
  ]

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'rental-fees': return 'blue'
      case 'deposits': return 'green'
      case 'late-fees': return 'red'
      case 'damage-fees': return 'orange'
      case 'fuel-charges': return 'yellow'
      case 'insurance': return 'purple'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'green'
      case 'pending': return 'orange'
      case 'projected': return 'blue'
      default: return 'gray'
    }
  }

  const getInitials = (source: string) => {
    return source.split('-').map(s => s[0]).join('').toUpperCase()
  }

  const filteredRecords = revenueRecords.filter(record => {
    const matchesSearch = record.period.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.location.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesSource = !sourceFilter || record.revenueSource === sourceFilter
    
    return matchesSearch && matchesSource
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('revenue')}
        description={t('trackRevenueAndFinancialPerformance')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportRevenue')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('generateReport')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="revenue">{t('revenueBreakdown')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Performance Insights - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('performanceInsights')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconTrendingUp size={16} />} color="green">
                    <Text fw={500} size="sm">{t('strongGrowth')}</Text>
                    <Text size="xs">12.5% {t('monthOverMonthGrowth')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconUser size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('customerValue')}</Text>
                    <Text size="xs">AED 485 {t('averageRevenuePerCustomer')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCar size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('topPerformer')}</Text>
                    <Text size="xs">{t('premiumVehiclesLeadRevenue')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Revenue Sources - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('topRevenueSources')}</Title>
                
                <Stack gap="sm">
                  {revenueRecords.slice(0, 3).map((record) => (
                    <Paper key={record.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getSourceColor(record.revenueSource)} radius="xl" size="sm">
                            {getInitials(record.revenueSource)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{t(record.revenueSource)}</Text>
                            <Text size="xs" c="dimmed">{record.period} - {record.location}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getSourceColor(record.revenueSource)} size="sm">
                            {record.currency} {record.amount.toLocaleString()}
                          </Badge>
                          <Text size="xs" c="dimmed">{record.growthPercent > 0 ? '+' : ''}{record.growthPercent}%</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="revenue" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchRevenue')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allSources')}
                  data={[
                    { value: '', label: t('allSources') },
                    { value: 'rental-fees', label: t('rentalFees') },
                    { value: 'deposits', label: t('deposits') },
                    { value: 'late-fees', label: t('lateFees') },
                    { value: 'damage-fees', label: t('damageFees') },
                    { value: 'fuel-charges', label: t('fuelCharges') },
                    { value: 'insurance', label: t('insurance') }
                  ]}
                  value={sourceFilter}
                  onChange={(value) => setSourceFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Revenue Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('period')}</Table.Th>
                  <Table.Th>{t('source')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('customers')}</Table.Th>
                  <Table.Th>{t('avgPerCustomer')}</Table.Th>
                  <Table.Th>{t('growth')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredRecords.map((record) => (
                  <Table.Tr key={record.id}>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{record.period}</Text>
                        <Text size="xs" c="dimmed">{record.location}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Group>
                        <Avatar color={getSourceColor(record.revenueSource)} radius="xl" size="sm">
                          {getInitials(record.revenueSource)}
                        </Avatar>
                        <div>
                          <Badge color={getSourceColor(record.revenueSource)} variant="light">
                            {t(record.revenueSource)}
                          </Badge>
                          <Text size="xs" c="dimmed">{record.vehicleCategory}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{record.currency} {record.amount.toLocaleString()}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{record.customerCount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{record.currency} {record.averagePerCustomer}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={record.growthPercent > 0 ? 'green' : 'red'} variant="light">
                        {record.growthPercent > 0 ? '+' : ''}{record.growthPercent}%
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(record.status)}>
                        {t(record.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconDownload size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconTrendingUp size={12} />}>
                          {t('analyze')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('generateRevenueReport')}
        size="md"
      >
        <Stack>
          <Select
            label={t('reportPeriod')}
            placeholder={t('selectReportPeriod')}
            data={[
              { value: 'current-month', label: t('currentMonth') },
              { value: 'last-month', label: t('lastMonth') },
              { value: 'quarter', label: t('currentQuarter') },
              { value: 'year', label: t('currentYear') },
              { value: 'custom', label: t('customPeriod') }
            ]}
            required
          />

          <Select
            label={t('revenueSource')}
            placeholder={t('selectRevenueSource')}
            data={[
              { value: 'all', label: t('allSources') },
              { value: 'rental-fees', label: t('rentalFees') },
              { value: 'deposits', label: t('deposits') },
              { value: 'late-fees', label: t('lateFees') },
              { value: 'damage-fees', label: t('damageFees') }
            ]}
            required
          />

          <Select
            label={t('location')}
            placeholder={t('selectLocation')}
            data={[
              { value: 'all', label: t('allLocations') },
              { value: 'dubai', label: 'Dubai' },
              { value: 'abu-dhabi', label: 'Abu Dhabi' },
              { value: 'sharjah', label: 'Sharjah' }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('generateReport')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
