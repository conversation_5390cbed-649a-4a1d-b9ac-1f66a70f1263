import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Progress,
  Alert,
  ThemeIcon,
  Checkbox
} from '@mantine/core'
import { DateInput } from '@mantine/dates'
import {
  IconAlertTriangle,
  IconCheck,
  IconDownload,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconReceipt,
  IconCurrencyDollar,
  IconFileText,
  IconPrinter,
  IconSend,
  IconRefresh
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface InvoiceData {
  id: string
  invoice_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  customer_address: string
  reservation_id?: string
  reservation_number?: string
  vehicle_id?: string
  vehicle_name?: string
  invoice_type: 'rental' | 'deposit' | 'damage' | 'fuel' | 'late_fee' | 'cleaning' | 'insurance' | 'additional'
  status: 'draft' | 'pending' | 'sent' | 'viewed' | 'paid' | 'overdue' | 'cancelled' | 'refunded'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  issue_date: string
  due_date: string
  paid_date?: string
  currency: 'AED' | 'USD' | 'EUR' | 'GBP'
  subtotal: number
  tax_rate: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  balance_due: number
  line_items: Array<{
    id: string
    description: string
    quantity: number
    unit_price: number
    total: number
    tax_rate: number
  }>
  payment_terms: string
  payment_method?: string
  payment_reference?: string
  notes?: string
  internal_notes?: string
  sent_count: number
  last_sent_date?: string
  last_viewed_date?: string
  reminder_count: number
  last_reminder_date?: string
  created_by: string
  created_at: string
  updated_at: string
  pdf_url?: string
  payment_link?: string
}

export function InvoicingComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [invoices, setInvoices] = useState<InvoiceData[]>([])
  const [activeTab, setActiveTab] = useState('all-invoices')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [priorityFilter, setPriorityFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [createModalOpen, setCreateModalOpen] = useState(false)

  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceData | null>(null)

  // Mock data for development - comprehensive invoicing structure
  const mockInvoices: InvoiceData[] = [
    {
      id: '1',
      invoice_number: 'INV-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971501234567',
      customer_address: 'Dubai Marina, Dubai, UAE',
      reservation_id: '1',
      reservation_number: 'RES-2024-001',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      invoice_type: 'rental',
      status: 'paid',
      priority: 'medium',
      issue_date: '2024-01-15T00:00:00Z',
      due_date: '2024-01-22T00:00:00Z',
      paid_date: '2024-01-16T10:30:00Z',
      currency: 'AED',
      subtotal: 600,
      tax_rate: 5,
      tax_amount: 30,
      discount_amount: 0,
      total_amount: 630,
      paid_amount: 630,
      balance_due: 0,
      line_items: [
        {
          id: '1',
          description: 'Toyota Camry 2023 - 5 days rental',
          quantity: 5,
          unit_price: 120,
          total: 600,
          tax_rate: 5
        }
      ],
      payment_terms: 'Net 7 days',
      payment_method: 'Credit Card',
      payment_reference: 'CC-2024-001',
      notes: 'Thank you for your business',
      sent_count: 1,
      last_sent_date: '2024-01-15T09:00:00Z',
      last_viewed_date: '2024-01-15T14:30:00Z',
      reminder_count: 0,
      created_by: 'John Smith',
      created_at: '2024-01-15T08:00:00Z',
      updated_at: '2024-01-16T10:30:00Z',
      pdf_url: '/invoices/INV-2024-001.pdf',
      payment_link: 'https://pay.carvio.com/inv/INV-2024-001'
    },
    {
      id: '2',
      invoice_number: 'INV-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+1555987654',
      customer_address: 'Downtown Dubai, Dubai, UAE',
      reservation_id: '2',
      reservation_number: 'RES-2024-002',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      invoice_type: 'damage',
      status: 'overdue',
      priority: 'high',
      issue_date: '2024-01-10T00:00:00Z',
      due_date: '2024-01-17T00:00:00Z',
      currency: 'AED',
      subtotal: 800,
      tax_rate: 5,
      tax_amount: 40,
      discount_amount: 50,
      total_amount: 790,
      paid_amount: 0,
      balance_due: 790,
      line_items: [
        {
          id: '1',
          description: 'Rear bumper scratch repair',
          quantity: 1,
          unit_price: 350,
          total: 350,
          tax_rate: 5
        },
        {
          id: '2',
          description: 'Door dent repair',
          quantity: 1,
          unit_price: 200,
          total: 200,
          tax_rate: 5
        },
        {
          id: '3',
          description: 'Interior cleaning',
          quantity: 1,
          unit_price: 250,
          total: 250,
          tax_rate: 5
        }
      ],
      payment_terms: 'Net 7 days',
      notes: 'Damage charges from vehicle return inspection',
      internal_notes: 'Customer disputed charges - follow up required',
      sent_count: 3,
      last_sent_date: '2024-01-18T10:00:00Z',
      last_viewed_date: '2024-01-12T16:45:00Z',
      reminder_count: 2,
      last_reminder_date: '2024-01-18T10:00:00Z',
      created_by: 'Maria Garcia',
      created_at: '2024-01-10T15:00:00Z',
      updated_at: '2024-01-18T10:00:00Z',
      pdf_url: '/invoices/INV-2024-002.pdf',
      payment_link: 'https://pay.carvio.com/inv/INV-2024-002'
    },
    {
      id: '3',
      invoice_number: 'INV-2024-003',
      customer_id: '3',
      customer_name: 'Mohammed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+971509876543',
      customer_address: 'Abu Dhabi Mall, Abu Dhabi, UAE',
      reservation_id: '3',
      reservation_number: 'RES-2024-003',
      vehicle_id: '3',
      vehicle_name: 'Mercedes C-Class 2023',
      invoice_type: 'rental',
      status: 'sent',
      priority: 'medium',
      issue_date: '2024-01-20T00:00:00Z',
      due_date: '2024-01-27T00:00:00Z',
      currency: 'AED',
      subtotal: 945,
      tax_rate: 5,
      tax_amount: 47.25,
      discount_amount: 100,
      total_amount: 892.25,
      paid_amount: 0,
      balance_due: 892.25,
      line_items: [
        {
          id: '1',
          description: 'Mercedes C-Class 2023 - 5 days rental',
          quantity: 5,
          unit_price: 189,
          total: 945,
          tax_rate: 5
        }
      ],
      payment_terms: 'Net 7 days',
      notes: 'Early bird discount applied',
      sent_count: 1,
      last_sent_date: '2024-01-20T09:00:00Z',
      reminder_count: 0,
      created_by: 'Ahmed Ali',
      created_at: '2024-01-20T08:00:00Z',
      updated_at: '2024-01-20T09:00:00Z',
      pdf_url: '/invoices/INV-2024-003.pdf',
      payment_link: 'https://pay.carvio.com/inv/INV-2024-003'
    },
    {
      id: '4',
      invoice_number: 'INV-2024-004',
      customer_id: '4',
      customer_name: 'Lisa Chen',
      customer_email: '<EMAIL>',
      customer_phone: '+1555123456',
      customer_address: 'Business Bay, Dubai, UAE',
      reservation_id: '4',
      reservation_number: 'RES-2024-004',
      vehicle_id: '4',
      vehicle_name: 'Audi A4 2023',
      invoice_type: 'rental',
      status: 'draft',
      priority: 'medium',
      issue_date: '2024-01-22T00:00:00Z',
      due_date: '2024-01-29T00:00:00Z',
      currency: 'AED',
      subtotal: 750,
      tax_rate: 5,
      tax_amount: 37.5,
      discount_amount: 0,
      total_amount: 787.5,
      paid_amount: 0,
      balance_due: 787.5,
      line_items: [
        {
          id: '1',
          description: 'Audi A4 2023 - 5 days rental',
          quantity: 5,
          unit_price: 150,
          total: 750,
          tax_rate: 5
        }
      ],
      payment_terms: 'Net 7 days',
      notes: 'Draft invoice - pending customer approval',
      sent_count: 0,
      reminder_count: 0,
      created_by: 'Ahmed Ali',
      created_at: '2024-01-22T08:00:00Z',
      updated_at: '2024-01-22T08:00:00Z'
    },
    {
      id: '5',
      invoice_number: 'INV-2024-005',
      customer_id: '5',
      customer_name: 'Omar Abdullah',
      customer_email: '<EMAIL>',
      customer_phone: '+971501111111',
      customer_address: 'Jumeirah, Dubai, UAE',
      reservation_id: '5',
      reservation_number: 'RES-2024-005',
      vehicle_id: '5',
      vehicle_name: 'Range Rover 2022',
      invoice_type: 'late_fee',
      status: 'overdue',
      priority: 'high',
      issue_date: '2024-01-05T00:00:00Z',
      due_date: '2024-01-12T00:00:00Z',
      currency: 'AED',
      subtotal: 300,
      tax_rate: 5,
      tax_amount: 15,
      discount_amount: 0,
      total_amount: 315,
      paid_amount: 0,
      balance_due: 315,
      line_items: [
        {
          id: '1',
          description: 'Late return fee - 6 hours',
          quantity: 6,
          unit_price: 50,
          total: 300,
          tax_rate: 5
        }
      ],
      payment_terms: 'Net 7 days',
      notes: 'Late return charges - vehicle returned 6 hours late',
      internal_notes: 'Customer contacted multiple times - no response',
      sent_count: 4,
      last_sent_date: '2024-01-20T10:00:00Z',
      last_viewed_date: '2024-01-08T16:30:00Z',
      reminder_count: 3,
      last_reminder_date: '2024-01-20T10:00:00Z',
      created_by: 'Maria Garcia',
      created_at: '2024-01-05T15:00:00Z',
      updated_at: '2024-01-20T10:00:00Z',
      pdf_url: '/invoices/INV-2024-005.pdf',
      payment_link: 'https://pay.carvio.com/inv/INV-2024-005'
    }
  ]

  useEffect(() => {
    loadInvoices()
  }, [])

  const loadInvoices = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await invoiceService.getAllInvoices()
      setInvoices(mockInvoices)
    } catch (error) {
      console.error('Error loading invoices:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = !searchQuery ||
      invoice.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.invoice_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.customer_email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (invoice.reservation_number && invoice.reservation_number.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesStatus = !statusFilter || invoice.status === statusFilter
    const matchesType = !typeFilter || invoice.invoice_type === typeFilter
    const matchesPriority = !priorityFilter || invoice.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesType && matchesPriority
  })

  // Pagination
  const totalItems = filteredInvoices.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedInvoices = filteredInvoices.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const totalInvoices = invoices.length
  const paidInvoices = invoices.filter(i => i.status === 'paid').length
  const overdueInvoices = invoices.filter(i => i.status === 'overdue').length
  const totalRevenue = invoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + i.total_amount, 0)
  const outstandingAmount = invoices.filter(i => ['sent', 'overdue'].includes(i.status)).reduce((sum, i) => sum + i.balance_due, 0)

  const stats = [
    {
      label: t('totalInvoices'),
      value: totalInvoices.toString(),
      color: 'blue',
      icon: IconReceipt
    },
    {
      label: t('paidInvoices'),
      value: paidInvoices.toString(),
      color: 'green',
      icon: IconCheck
    },
    {
      label: t('overdueInvoices'),
      value: overdueInvoices.toString(),
      color: 'red',
      icon: IconAlertTriangle
    },
    {
      label: t('totalRevenue'),
      value: `AED ${totalRevenue.toLocaleString()}`,
      color: 'green',
      icon: IconCurrencyDollar
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'gray'
      case 'pending': return 'blue'
      case 'sent': return 'cyan'
      case 'viewed': return 'purple'
      case 'paid': return 'green'
      case 'overdue': return 'red'
      case 'cancelled': return 'orange'
      case 'refunded': return 'yellow'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'rental': return 'blue'
      case 'deposit': return 'green'
      case 'damage': return 'red'
      case 'fuel': return 'yellow'
      case 'late_fee': return 'orange'
      case 'cleaning': return 'purple'
      case 'insurance': return 'cyan'
      case 'additional': return 'gray'
      default: return 'gray'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'green'
      case 'medium': return 'yellow'
      case 'high': return 'orange'
      case 'urgent': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getPaymentProgress = (invoice: InvoiceData) => {
    return Math.round((invoice.paid_amount / invoice.total_amount) * 100)
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('invoicingManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageInvoicesAndBilling')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconRefresh size={16} />}>
                {t('refresh')}
              </Button>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setCreateModalOpen(true)}
              >
                {t('createInvoice')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-invoices">{t('allInvoices')}</Tabs.Tab>
            <Tabs.Tab value="overdue">{t('overdue')}</Tabs.Tab>
            <Tabs.Tab value="draft">{t('draft')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-invoices">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder={t('searchInvoices')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'draft', label: t('draft') },
                      { value: 'sent', label: t('sent') },
                      { value: 'paid', label: t('paid') },
                      { value: 'overdue', label: t('overdue') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('invoiceType')}
                    value={typeFilter}
                    onChange={(value) => setTypeFilter(value || '')}
                    data={[
                      { value: '', label: t('allTypes') },
                      { value: 'rental', label: t('rental') },
                      { value: 'deposit', label: t('deposit') },
                      { value: 'damage', label: t('damage') },
                      { value: 'fuel', label: t('fuel') },
                      { value: 'late_fee', label: t('lateFee') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('priority')}
                    value={priorityFilter}
                    onChange={(value) => setPriorityFilter(value || '')}
                    data={[
                      { value: '', label: t('allPriorities') },
                      { value: 'low', label: t('low') },
                      { value: 'medium', label: t('medium') },
                      { value: 'high', label: t('high') },
                      { value: 'urgent', label: t('urgent') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setTypeFilter('')
                      setPriorityFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Invoices Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('invoice')}</Table.Th>
                    <Table.Th>{t('amount')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('payment')}</Table.Th>
                    <Table.Th>{t('dueDate')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedInvoices.map((invoice) => (
                    <Table.Tr key={invoice.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color={getStatusColor(invoice.status)}>
                            {getInitials(invoice.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{invoice.customer_name}</Text>
                            <Text size="xs" c="dimmed">{invoice.customer_email}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{invoice.invoice_number}</Text>
                          <Text size="xs" c="dimmed">
                            {invoice.reservation_number && `${invoice.reservation_number} • `}
                            {t(invoice.invoice_type)}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">
                            {invoice.currency} {invoice.total_amount.toLocaleString()}
                          </Text>
                          {invoice.balance_due > 0 && (
                            <Text size="xs" c="red">
                              {t('due')}: {invoice.currency} {invoice.balance_due.toLocaleString()}
                            </Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(invoice.status)} size="sm">
                          {t(invoice.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Progress
                            value={getPaymentProgress(invoice)}
                            size="sm"
                            color={getPaymentProgress(invoice) === 100 ? 'green' : 'blue'}
                          />
                          <Text size="xs" c="dimmed" mt={2}>
                            {getPaymentProgress(invoice)}% {t('paid')}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">
                            {new Date(invoice.due_date).toLocaleDateString()}
                          </Text>
                          {invoice.status === 'overdue' && (
                            <Text size="xs" c="red">
                              {Math.ceil((new Date().getTime() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24))} {t('daysOverdue')}
                            </Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedInvoice(invoice)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedInvoice(invoice)
                              // TODO: Implement send invoice functionality
                            }}
                          >
                            <IconSend size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="green"
                          >
                            <IconDownload size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="orange"
                          >
                            <IconPrinter size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('invoices')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="overdue">
            <Grid>
              {invoices
                .filter(i => i.status === 'overdue')
                .map((invoice) => (
                  <Grid.Col key={invoice.id} span={{ base: 12, md: 6 }}>
                    <Card withBorder>
                      <Alert color="red" icon={<IconAlertTriangle size={16} />} mb="md">
                        <Text fw={500}>{t('overdueInvoice')}</Text>
                        <Text size="sm">
                          {Math.ceil((new Date().getTime() - new Date(invoice.due_date).getTime()) / (1000 * 60 * 60 * 24))} {t('daysOverdue')}
                        </Text>
                      </Alert>

                      <Group justify="space-between" mb="md">
                        <Group>
                          <Avatar color="red">
                            {getInitials(invoice.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500}>{invoice.customer_name}</Text>
                            <Text size="sm" c="dimmed">{invoice.invoice_number}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Text fw={700} size="lg" c="red">
                            {invoice.currency} {invoice.balance_due.toLocaleString()}
                          </Text>
                          <Text size="xs" c="dimmed">{t(invoice.invoice_type)}</Text>
                        </div>
                      </Group>

                      <Group justify="flex-end" mt="md">
                        <Button size="xs" variant="light" color="red">
                          {t('sendReminder')}
                        </Button>
                        <Button size="xs" color="red">
                          {t('callCustomer')}
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
              {invoices.filter(i => i.status === 'overdue').length === 0 && (
                <Grid.Col span={12}>
                  <Card withBorder>
                    <Stack align="center" py="xl">
                      <ThemeIcon size="xl" color="green" variant="light">
                        <IconCheck size={24} />
                      </ThemeIcon>
                      <Text fw={500}>{t('noOverdueInvoices')}</Text>
                      <Text size="sm" c="dimmed">{t('allInvoicesArePaidOnTime')}</Text>
                    </Stack>
                  </Card>
                </Grid.Col>
              )}
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="draft">
            <Grid>
              {invoices
                .filter(i => i.status === 'draft')
                .map((invoice) => (
                  <Grid.Col key={invoice.id} span={{ base: 12, md: 6 }}>
                    <Card withBorder>
                      <Group justify="space-between" mb="md">
                        <Group>
                          <Avatar color="yellow">
                            {getInitials(invoice.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500}>{invoice.customer_name}</Text>
                            <Text size="sm" c="dimmed">{invoice.invoice_number}</Text>
                          </div>
                        </Group>
                        <Badge color="yellow">
                          {t('draft')}
                        </Badge>
                      </Group>

                      <Text size="sm" c="dimmed" mb="xs">{t('amount')}</Text>
                      <Text fw={700} size="lg" mb="md">
                        {invoice.currency} {invoice.total_amount.toLocaleString()}
                      </Text>

                      <Group justify="flex-end" mt="md">
                        <Button size="xs" variant="light">
                          {t('edit')}
                        </Button>
                        <Button size="xs" color="blue">
                          {t('sendInvoice')}
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
              {invoices.filter(i => i.status === 'draft').length === 0 && (
                <Grid.Col span={12}>
                  <Card withBorder>
                    <Stack align="center" py="xl">
                      <ThemeIcon size="xl" color="blue" variant="light">
                        <IconFileText size={24} />
                      </ThemeIcon>
                      <Text fw={500}>{t('noDraftInvoices')}</Text>
                      <Text size="sm" c="dimmed">{t('allInvoicesHaveBeenSent')}</Text>
                      <Button leftSection={<IconPlus size={16} />} onClick={() => setCreateModalOpen(true)}>
                        {t('createInvoice')}
                      </Button>
                    </Stack>
                  </Card>
                </Grid.Col>
              )}
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* Create Invoice Modal */}
        <Modal
          opened={createModalOpen}
          onClose={() => setCreateModalOpen(false)}
          title={t('createInvoice')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('customer')}
                  placeholder={t('selectCustomer')}
                  data={[
                    { value: '1', label: 'Ahmed Al-Rashid' },
                    { value: '2', label: 'Sarah Johnson' },
                    { value: '3', label: 'Mohammed Hassan' },
                    { value: '4', label: 'Lisa Chen' },
                    { value: '5', label: 'Omar Abdullah' }
                  ]}
                  required
                  searchable
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('reservation')}
                  placeholder={t('selectReservation')}
                  data={[
                    { value: '1', label: 'RES-2024-001 - Toyota Camry' },
                    { value: '2', label: 'RES-2024-002 - BMW X5' },
                    { value: '3', label: 'RES-2024-003 - Mercedes C-Class' }
                  ]}
                  searchable
                />
              </Grid.Col>
            </Grid>

            <Select
              label={t('invoiceType')}
              placeholder={t('selectInvoiceType')}
              data={[
                { value: 'rental', label: t('rental') },
                { value: 'deposit', label: t('deposit') },
                { value: 'damage', label: t('damage') },
                { value: 'fuel', label: t('fuel') },
                { value: 'late_fee', label: t('lateFee') },
                { value: 'cleaning', label: t('cleaning') },
                { value: 'additional', label: t('additional') }
              ]}
              required
            />

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('amount')}
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  fixedDecimalScale
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('currency')}
                  placeholder={t('selectCurrency')}
                  data={[
                    { value: 'AED', label: 'AED - UAE Dirham' },
                    { value: 'USD', label: 'USD - US Dollar' },
                    { value: 'EUR', label: 'EUR - Euro' },
                    { value: 'GBP', label: 'GBP - British Pound' }
                  ]}
                  defaultValue="AED"
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <DateInput
                  label={t('issueDate')}
                  placeholder={t('selectDate')}
                  defaultValue={new Date()}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <DateInput
                  label={t('dueDate')}
                  placeholder={t('selectDate')}
                  defaultValue={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('taxRate')}
                  placeholder="5"
                  min={0}
                  max={100}
                  suffix="%"
                  defaultValue={5}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('discountAmount')}
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  fixedDecimalScale
                />
              </Grid.Col>
            </Grid>

            <Select
              label={t('priority')}
              placeholder={t('selectPriority')}
              data={[
                { value: 'low', label: t('low') },
                { value: 'medium', label: t('medium') },
                { value: 'high', label: t('high') },
                { value: 'urgent', label: t('urgent') }
              ]}
              defaultValue="medium"
            />

            <Textarea
              label={t('notes')}
              placeholder={t('enterInvoiceNotes')}
              rows={3}
            />

            <Divider />

            <Group justify="space-between">
              <Group>
                <Checkbox label={t('sendImmediately')} />
                <Checkbox label={t('saveAsDraft')} defaultChecked />
              </Group>
              <Group>
                <Button variant="light" onClick={() => setCreateModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button leftSection={<IconCheck size={16} />} onClick={() => setCreateModalOpen(false)}>
                  {t('createInvoice')}
                </Button>
              </Group>
            </Group>
          </Stack>
        </Modal>

        {/* View Invoice Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedInvoice ? selectedInvoice.invoice_number : t('invoiceDetails')}
          size="xl"
        >
          {selectedInvoice && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('customer')}</Text>
                      <Text fw={500}>{selectedInvoice.customer_name}</Text>
                      <Text size="xs" c="dimmed">{selectedInvoice.customer_email}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('invoiceType')}</Text>
                      <Badge color={getTypeColor(selectedInvoice.invoice_type)}>
                        {t(selectedInvoice.invoice_type)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('issueDate')}</Text>
                      <Text fw={500}>
                        {new Date(selectedInvoice.issue_date).toLocaleDateString()}
                      </Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('dueDate')}</Text>
                      <Text fw={500}>
                        {new Date(selectedInvoice.due_date).toLocaleDateString()}
                      </Text>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <ThemeIcon size="xl" color={getStatusColor(selectedInvoice.status)}>
                        <IconReceipt size={24} />
                      </ThemeIcon>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="lg">
                          {selectedInvoice.currency} {selectedInvoice.total_amount.toLocaleString()}
                        </Text>
                        <Text size="sm" c="dimmed">{t('totalAmount')}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <div>
                <Text size="sm" c="dimmed" mb="xs">{t('paymentProgress')}</Text>
                <Progress
                  value={getPaymentProgress(selectedInvoice)}
                  size="lg"
                  color={getPaymentProgress(selectedInvoice) === 100 ? 'green' : 'blue'}
                />
                <Text size="xs" c="dimmed" mt="xs">
                  {selectedInvoice.currency} {selectedInvoice.paid_amount.toLocaleString()} {t('of')} {selectedInvoice.currency} {selectedInvoice.total_amount.toLocaleString()} {t('paid')}
                </Text>
              </div>

              <Grid>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('subtotal')}</Text>
                  <Text fw={500}>{selectedInvoice.currency} {selectedInvoice.subtotal.toLocaleString()}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('taxAmount')}</Text>
                  <Text fw={500}>{selectedInvoice.currency} {selectedInvoice.tax_amount.toLocaleString()}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('discountAmount')}</Text>
                  <Text fw={500} c="green">-{selectedInvoice.currency} {selectedInvoice.discount_amount.toLocaleString()}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('balanceDue')}</Text>
                  <Text fw={700} size="lg" c={selectedInvoice.balance_due > 0 ? 'red' : 'green'}>
                    {selectedInvoice.currency} {selectedInvoice.balance_due.toLocaleString()}
                  </Text>
                </Grid.Col>
              </Grid>

              {selectedInvoice.notes && (
                <>
                  <Divider />
                  <div>
                    <Text size="sm" c="dimmed">{t('notes')}</Text>
                    <Text>{selectedInvoice.notes}</Text>
                  </div>
                </>
              )}

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" leftSection={<IconDownload size={16} />}>
                  {t('downloadPDF')}
                </Button>
                <Button variant="light" leftSection={<IconPrinter size={16} />}>
                  {t('print')}
                </Button>
                <Button leftSection={<IconSend size={16} />}>
                  {t('sendInvoice')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
