import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconMail,
  IconPlus,
  IconReceipt,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface Invoice {
  id: string
  invoiceNumber: string
  customerName: string
  customerEmail: string
  contractNumber: string
  invoiceType: 'rental' | 'deposit' | 'damage' | 'fuel' | 'late-fee'
  amount: number
  currency: string
  issueDate: string
  dueDate: string
  paidDate: string
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  paymentMethod: string
  location: string
}

export function InvoicingSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const invoices: Invoice[] = [
    {
      id: '1',
      invoiceNumber: 'INV-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerEmail: '<EMAIL>',
      contractNumber: 'CON-2024-001',
      invoiceType: 'rental',
      amount: 600,
      currency: 'AED',
      issueDate: '2024-01-15',
      dueDate: '2024-01-22',
      paidDate: '2024-01-16',
      status: 'paid',
      paymentMethod: 'Credit Card',
      location: 'Dubai Airport'
    },
    {
      id: '2',
      invoiceNumber: 'INV-2024-002',
      customerName: 'Sarah Johnson',
      customerEmail: '<EMAIL>',
      contractNumber: 'CON-2024-002',
      invoiceType: 'deposit',
      amount: 1500,
      currency: 'AED',
      issueDate: '2024-01-20',
      dueDate: '2024-01-27',
      paidDate: '',
      status: 'sent',
      paymentMethod: '',
      location: 'Abu Dhabi'
    },
    {
      id: '3',
      invoiceNumber: 'INV-2024-003',
      customerName: 'Mohammed Hassan',
      customerEmail: '<EMAIL>',
      contractNumber: 'CON-2024-003',
      invoiceType: 'late-fee',
      amount: 150,
      currency: 'AED',
      issueDate: '2024-01-10',
      dueDate: '2024-01-17',
      paidDate: '',
      status: 'overdue',
      paymentMethod: '',
      location: 'Sharjah'
    }
  ]

  const stats = [
    { label: t('totalInvoices'), value: '1,847', color: 'blue', icon: IconReceipt },
    { label: t('paidInvoices'), value: '1,456', color: 'green', icon: IconCheck },
    { label: t('overdueInvoices'), value: '89', color: 'red', icon: IconAlertTriangle },
    { label: t('totalInvoiceValue'), value: 'AED 567,890', color: 'green', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'green'
      case 'sent': return 'blue'
      case 'draft': return 'gray'
      case 'overdue': return 'red'
      case 'cancelled': return 'orange'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'rental': return 'blue'
      case 'deposit': return 'green'
      case 'damage': return 'red'
      case 'fuel': return 'yellow'
      case 'late-fee': return 'orange'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || invoice.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('invoicing')}
        description={t('manageInvoicesAndBilling')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportInvoices')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('createInvoice')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="invoices">{t('allInvoices')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('invoiceAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('overdueInvoices')}</Text>
                    <Text size="xs">89 {t('invoicesAreOverdue')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('dueSoon')}</Text>
                    <Text size="xs">156 {t('invoicesDueSoon')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconMail size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('draftInvoices')}</Text>
                    <Text size="xs">23 {t('invoicesInDraft')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Invoices - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentInvoices')}</Title>
                
                <Stack gap="sm">
                  {invoices.slice(0, 3).map((invoice) => (
                    <Paper key={invoice.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getTypeColor(invoice.invoiceType)} radius="xl" size="sm">
                            {getInitials(invoice.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{invoice.customerName}</Text>
                            <Text size="xs" c="dimmed">{invoice.invoiceNumber} - {invoice.issueDate}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(invoice.status)} size="sm">
                            {invoice.currency} {invoice.amount}
                          </Badge>
                          <Text size="xs" c="dimmed">{t(invoice.invoiceType)}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="invoices" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchInvoices')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'paid', label: t('paid') },
                    { value: 'sent', label: t('sent') },
                    { value: 'draft', label: t('draft') },
                    { value: 'overdue', label: t('overdue') },
                    { value: 'cancelled', label: t('cancelled') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Invoices Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('invoice')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('dueDate')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredInvoices.map((invoice) => (
                  <Table.Tr key={invoice.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getTypeColor(invoice.invoiceType)} radius="xl" size="sm">
                          {getInitials(invoice.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{invoice.customerName}</Text>
                          <Text size="xs" c="dimmed">{invoice.customerEmail}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{invoice.invoiceNumber}</Text>
                        <Text size="xs" c="dimmed">{invoice.contractNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{invoice.currency} {invoice.amount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(invoice.invoiceType)} variant="light">
                        {t(invoice.invoiceType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{invoice.dueDate}</Text>
                        <Text size="xs" c="dimmed">{t('issued')}: {invoice.issueDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(invoice.status)}>
                        {t(invoice.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconDownload size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconMail size={12} />}>
                          {t('send')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('createInvoice')}
        size="md"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid - CON-2024-001' },
              { value: '2', label: 'Sarah Johnson - CON-2024-002' },
              { value: '3', label: 'Mohammed Hassan - CON-2024-003' }
            ]}
            required
          />

          <Select
            label={t('invoiceType')}
            placeholder={t('selectInvoiceType')}
            data={[
              { value: 'rental', label: t('rental') },
              { value: 'deposit', label: t('deposit') },
              { value: 'damage', label: t('damage') },
              { value: 'fuel', label: t('fuel') },
              { value: 'late-fee', label: t('lateFee') }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('amount')}
                placeholder={t('enterAmount')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('currency')}
                placeholder={t('selectCurrency')}
                data={[
                  { value: 'AED', label: 'AED' },
                  { value: 'USD', label: 'USD' },
                  { value: 'EUR', label: 'EUR' }
                ]}
                defaultValue="AED"
                required
              />
            </Grid.Col>
          </Grid>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('createInvoice')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
