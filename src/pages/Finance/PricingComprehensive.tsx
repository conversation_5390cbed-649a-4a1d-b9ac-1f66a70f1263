import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Progress,
  Alert,
  ThemeIcon,
  Paper,
  Checkbox,
  Switch,
  Slider
} from '@mantine/core'
import { DateInput } from '@mantine/dates'
import {
  IconAlertTriangle,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconCurrencyDollar,
  IconCalendar,
  IconSettings,
  IconTrendingUp,
  IconTrendingDown,
  IconRefresh,
  IconCalculator,
  IconPercentage,
  IconCar,
  IconTag,
  IconCopy
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface PricingRuleData {
  id: string
  rule_name: string
  vehicle_category: string
  vehicle_make?: string
  vehicle_model?: string
  location: string
  pricing_type: 'daily' | 'weekly' | 'monthly' | 'hourly' | 'seasonal' | 'dynamic'
  base_price: number
  currency: 'AED' | 'USD' | 'EUR' | 'GBP'
  seasonal_multipliers: {
    peak_season: number
    high_season: number
    regular_season: number
    low_season: number
  }
  day_of_week_multipliers: {
    monday: number
    tuesday: number
    wednesday: number
    thursday: number
    friday: number
    saturday: number
    sunday: number
  }
  duration_discounts: Array<{
    min_days: number
    max_days: number
    discount_percentage: number
  }>
  advance_booking_discounts: Array<{
    min_days_advance: number
    discount_percentage: number
  }>
  age_restrictions: {
    min_age: number
    max_age: number
    young_driver_surcharge: number
    senior_driver_discount: number
  }
  insurance_options: Array<{
    type: 'basic' | 'comprehensive' | 'premium'
    daily_rate: number
    coverage_amount: number
    deductible: number
  }>
  additional_fees: {
    cleaning_fee: number
    fuel_service_fee: number
    delivery_fee: number
    airport_surcharge: number
    late_return_fee_per_hour: number
    mileage_fee_per_km: number
    mileage_limit_per_day: number
  }
  status: 'active' | 'inactive' | 'draft' | 'scheduled'
  effective_from: string
  effective_until?: string
  created_by: string
  created_at: string
  updated_at: string
}

export function PricingComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [pricingRules, setPricingRules] = useState<PricingRuleData[]>([])
  const [activeTab, setActiveTab] = useState('all-rules')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [calculatorModalOpen, setCalculatorModalOpen] = useState(false)
  const [selectedRule, setSelectedRule] = useState<PricingRuleData | null>(null)

  // Calculator states
  const [calculatorDays, setCalculatorDays] = useState(3)
  const [calculatorSeason, setCalculatorSeason] = useState('regular_season')
  const [calculatorInsurance, setCalculatorInsurance] = useState('basic')

  // Mock data for development - comprehensive pricing structure
  const mockPricingRules: PricingRuleData[] = [
    {
      id: '1',
      rule_name: 'Economy Cars - Dubai',
      vehicle_category: 'Economy',
      location: 'Dubai',
      pricing_type: 'daily',
      base_price: 120,
      currency: 'AED',
      seasonal_multipliers: {
        peak_season: 1.5,
        high_season: 1.3,
        regular_season: 1.0,
        low_season: 0.8
      },
      day_of_week_multipliers: {
        monday: 1.0,
        tuesday: 1.0,
        wednesday: 1.0,
        thursday: 1.1,
        friday: 1.3,
        saturday: 1.4,
        sunday: 1.2
      },
      duration_discounts: [
        { min_days: 7, max_days: 13, discount_percentage: 10 },
        { min_days: 14, max_days: 29, discount_percentage: 15 },
        { min_days: 30, max_days: 999, discount_percentage: 25 }
      ],
      advance_booking_discounts: [
        { min_days_advance: 7, discount_percentage: 5 },
        { min_days_advance: 14, discount_percentage: 10 },
        { min_days_advance: 30, discount_percentage: 15 }
      ],
      age_restrictions: {
        min_age: 21,
        max_age: 75,
        young_driver_surcharge: 25,
        senior_driver_discount: 0
      },
      insurance_options: [
        { type: 'basic', daily_rate: 15, coverage_amount: 50000, deductible: 2000 },
        { type: 'comprehensive', daily_rate: 35, coverage_amount: 100000, deductible: 500 },
        { type: 'premium', daily_rate: 55, coverage_amount: 200000, deductible: 0 }
      ],
      additional_fees: {
        cleaning_fee: 50,
        fuel_service_fee: 75,
        delivery_fee: 100,
        airport_surcharge: 50,
        late_return_fee_per_hour: 25,
        mileage_fee_per_km: 0.5,
        mileage_limit_per_day: 200
      },
      status: 'active',
      effective_from: '2024-01-01T00:00:00Z',
      created_by: 'John Smith',
      created_at: '2024-01-01T08:00:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      rule_name: 'Luxury Cars - Dubai',
      vehicle_category: 'Luxury',
      location: 'Dubai',
      pricing_type: 'daily',
      base_price: 450,
      currency: 'AED',
      seasonal_multipliers: {
        peak_season: 1.8,
        high_season: 1.5,
        regular_season: 1.0,
        low_season: 0.9
      },
      day_of_week_multipliers: {
        monday: 1.0,
        tuesday: 1.0,
        wednesday: 1.0,
        thursday: 1.2,
        friday: 1.5,
        saturday: 1.6,
        sunday: 1.3
      },
      duration_discounts: [
        { min_days: 3, max_days: 6, discount_percentage: 5 },
        { min_days: 7, max_days: 13, discount_percentage: 12 },
        { min_days: 14, max_days: 29, discount_percentage: 18 }
      ],
      advance_booking_discounts: [
        { min_days_advance: 14, discount_percentage: 8 },
        { min_days_advance: 30, discount_percentage: 12 }
      ],
      age_restrictions: {
        min_age: 25,
        max_age: 70,
        young_driver_surcharge: 100,
        senior_driver_discount: 0
      },
      insurance_options: [
        { type: 'comprehensive', daily_rate: 85, coverage_amount: 300000, deductible: 1000 },
        { type: 'premium', daily_rate: 150, coverage_amount: 500000, deductible: 0 }
      ],
      additional_fees: {
        cleaning_fee: 150,
        fuel_service_fee: 200,
        delivery_fee: 250,
        airport_surcharge: 100,
        late_return_fee_per_hour: 75,
        mileage_fee_per_km: 1.5,
        mileage_limit_per_day: 150
      },
      status: 'active',
      effective_from: '2024-01-01T00:00:00Z',
      created_by: 'Maria Garcia',
      created_at: '2024-01-01T08:00:00Z',
      updated_at: '2024-01-10T14:20:00Z'
    },
    {
      id: '3',
      rule_name: 'SUV Cars - Abu Dhabi',
      vehicle_category: 'SUV',
      location: 'Abu Dhabi',
      pricing_type: 'daily',
      base_price: 280,
      currency: 'AED',
      seasonal_multipliers: {
        peak_season: 1.6,
        high_season: 1.4,
        regular_season: 1.0,
        low_season: 0.85
      },
      day_of_week_multipliers: {
        monday: 1.0,
        tuesday: 1.0,
        wednesday: 1.0,
        thursday: 1.1,
        friday: 1.4,
        saturday: 1.5,
        sunday: 1.3
      },
      duration_discounts: [
        { min_days: 5, max_days: 9, discount_percentage: 8 },
        { min_days: 10, max_days: 19, discount_percentage: 15 },
        { min_days: 20, max_days: 999, discount_percentage: 22 }
      ],
      advance_booking_discounts: [
        { min_days_advance: 10, discount_percentage: 7 },
        { min_days_advance: 21, discount_percentage: 12 }
      ],
      age_restrictions: {
        min_age: 23,
        max_age: 75,
        young_driver_surcharge: 50,
        senior_driver_discount: 0
      },
      insurance_options: [
        { type: 'basic', daily_rate: 25, coverage_amount: 75000, deductible: 1500 },
        { type: 'comprehensive', daily_rate: 55, coverage_amount: 150000, deductible: 750 },
        { type: 'premium', daily_rate: 85, coverage_amount: 250000, deductible: 0 }
      ],
      additional_fees: {
        cleaning_fee: 75,
        fuel_service_fee: 125,
        delivery_fee: 150,
        airport_surcharge: 75,
        late_return_fee_per_hour: 40,
        mileage_fee_per_km: 1.0,
        mileage_limit_per_day: 180
      },
      status: 'draft',
      effective_from: '2024-02-01T00:00:00Z',
      created_by: 'Ahmed Ali',
      created_at: '2024-01-22T10:00:00Z',
      updated_at: '2024-01-22T10:00:00Z'
    },
    {
      id: '4',
      rule_name: 'Weekend Special - Sports Cars',
      vehicle_category: 'Sports',
      location: 'Dubai',
      pricing_type: 'daily',
      base_price: 650,
      currency: 'AED',
      seasonal_multipliers: {
        peak_season: 2.0,
        high_season: 1.7,
        regular_season: 1.0,
        low_season: 0.95
      },
      day_of_week_multipliers: {
        monday: 0.8,
        tuesday: 0.8,
        wednesday: 0.8,
        thursday: 1.0,
        friday: 1.8,
        saturday: 2.0,
        sunday: 1.5
      },
      duration_discounts: [
        { min_days: 2, max_days: 4, discount_percentage: 5 },
        { min_days: 5, max_days: 9, discount_percentage: 10 }
      ],
      advance_booking_discounts: [
        { min_days_advance: 7, discount_percentage: 5 },
        { min_days_advance: 21, discount_percentage: 10 }
      ],
      age_restrictions: {
        min_age: 28,
        max_age: 65,
        young_driver_surcharge: 200,
        senior_driver_discount: 0
      },
      insurance_options: [
        { type: 'comprehensive', daily_rate: 120, coverage_amount: 500000, deductible: 2000 },
        { type: 'premium', daily_rate: 200, coverage_amount: 1000000, deductible: 500 }
      ],
      additional_fees: {
        cleaning_fee: 200,
        fuel_service_fee: 300,
        delivery_fee: 400,
        airport_surcharge: 150,
        late_return_fee_per_hour: 100,
        mileage_fee_per_km: 2.0,
        mileage_limit_per_day: 100
      },
      status: 'draft',
      effective_from: '2024-02-15T00:00:00Z',
      created_by: 'John Smith',
      created_at: '2024-01-25T14:00:00Z',
      updated_at: '2024-01-25T14:00:00Z'
    }
  ]

  useEffect(() => {
    loadPricingRules()
  }, [])

  const loadPricingRules = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await pricingService.getAllPricingRules()
      setPricingRules(mockPricingRules)
    } catch (error) {
      console.error('Error loading pricing rules:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredRules = pricingRules.filter(rule => {
    const matchesSearch = !searchQuery || 
      rule.rule_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rule.vehicle_category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rule.location.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || rule.status === statusFilter
    const matchesType = !typeFilter || rule.pricing_type === typeFilter
    const matchesLocation = !locationFilter || rule.location.toLowerCase().includes(locationFilter.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesType && matchesLocation
  })

  // Pagination
  const totalItems = filteredRules.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedRules = filteredRules.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const activeRules = pricingRules.filter(r => r.status === 'active').length
  const draftRules = pricingRules.filter(r => r.status === 'draft').length
  const avgBasePrice = pricingRules.reduce((sum, r) => sum + r.base_price, 0) / pricingRules.length
  const totalCategories = new Set(pricingRules.map(r => r.vehicle_category)).size

  const stats = [
    {
      label: t('activeRules'),
      value: activeRules.toString(),
      color: 'green',
      icon: IconCheck
    },
    {
      label: t('draftRules'),
      value: draftRules.toString(),
      color: 'yellow',
      icon: IconEdit
    },
    {
      label: t('avgBasePrice'),
      value: `AED ${Math.round(avgBasePrice)}`,
      color: 'blue',
      icon: IconCurrencyDollar
    },
    {
      label: t('categories'),
      value: totalCategories.toString(),
      color: 'purple',
      icon: IconTag
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'inactive': return 'red'
      case 'draft': return 'yellow'
      case 'scheduled': return 'blue'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'daily': return 'blue'
      case 'weekly': return 'green'
      case 'monthly': return 'purple'
      case 'hourly': return 'orange'
      case 'seasonal': return 'cyan'
      case 'dynamic': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('pricingManagement')}</Title>
              <Text c="dimmed" size="sm">{t('managePricingRulesAndStrategies')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconRefresh size={16} />}>
                {t('refresh')}
              </Button>
              <Button variant="light" leftSection={<IconCalculator size={16} />} onClick={() => {
                // Use the first active rule as default for calculator
                const defaultRule = pricingRules.find(r => r.status === 'active') || pricingRules[0]
                if (defaultRule) {
                  setSelectedRule(defaultRule)
                  setCalculatorModalOpen(true)
                }
              }}>
                {t('priceCalculator')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setCreateModalOpen(true)}
              >
                {t('createRule')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-rules">{t('allRules')}</Tabs.Tab>
            <Tabs.Tab value="active">{t('active')}</Tabs.Tab>
            <Tabs.Tab value="draft">{t('draft')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-rules">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder={t('searchPricingRules')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'active', label: t('active') },
                      { value: 'inactive', label: t('inactive') },
                      { value: 'draft', label: t('draft') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('pricingType')}
                    value={typeFilter}
                    onChange={(value) => setTypeFilter(value || '')}
                    data={[
                      { value: '', label: t('allTypes') },
                      { value: 'daily', label: t('daily') },
                      { value: 'weekly', label: t('weekly') },
                      { value: 'monthly', label: t('monthly') },
                      { value: 'hourly', label: t('hourly') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <TextInput
                    placeholder={t('location')}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setTypeFilter('')
                      setLocationFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Pricing Rules Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('ruleName')}</Table.Th>
                    <Table.Th>{t('category')}</Table.Th>
                    <Table.Th>{t('basePrice')}</Table.Th>
                    <Table.Th>{t('pricingType')}</Table.Th>
                    <Table.Th>{t('location')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedRules.map((rule) => (
                    <Table.Tr key={rule.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color={getStatusColor(rule.status)}>
                            <IconTag size={16} />
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{rule.rule_name}</Text>
                            <Text size="xs" c="dimmed">
                              {t('effectiveFrom')}: {new Date(rule.effective_from).toLocaleDateString()}
                            </Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Badge color="blue" variant="light" mb="xs">
                            {rule.vehicle_category}
                          </Badge>
                          {(rule.vehicle_make || rule.vehicle_model) && (
                            <div>
                              <Text size="xs" c="dimmed">
                                {rule.vehicle_make && `${rule.vehicle_make}`}
                                {rule.vehicle_make && rule.vehicle_model && ' '}
                                {rule.vehicle_model && `${rule.vehicle_model}`}
                              </Text>
                            </div>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">
                            {rule.currency} {rule.base_price}
                          </Text>
                          <Text size="xs" c="dimmed">
                            /{t(rule.pricing_type)}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getTypeColor(rule.pricing_type)} size="sm">
                          {t(rule.pricing_type)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{rule.location}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(rule.status)} size="sm">
                          {t(rule.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedRule(rule)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedRule(rule)
                              setEditModalOpen(true)
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="green"
                            onClick={() => {
                              setSelectedRule(rule)
                              setCalculatorModalOpen(true)
                            }}
                          >
                            <IconCalculator size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('pricingRules')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="active">
            <Grid>
              {pricingRules
                .filter(r => r.status === 'active')
                .map((rule) => (
                  <Grid.Col key={rule.id} span={{ base: 12, md: 6, lg: 4 }}>
                    <Card withBorder>
                      <Group justify="space-between" mb="md">
                        <Badge color="green" size="sm">
                          {t('active')}
                        </Badge>
                        <Badge color={getTypeColor(rule.pricing_type)} variant="light" size="sm">
                          {t(rule.pricing_type)}
                        </Badge>
                      </Group>

                      <Text fw={500} size="lg" mb="xs">{rule.rule_name}</Text>
                      <Text size="sm" c="dimmed" mb="md">{rule.vehicle_category} • {rule.location}</Text>

                      <Group justify="space-between" mb="md">
                        <div>
                          <Text size="xs" c="dimmed">{t('basePrice')}</Text>
                          <Text fw={700} size="xl" c="blue">
                            {rule.currency} {rule.base_price}
                          </Text>
                          <Text size="xs" c="dimmed">/{t(rule.pricing_type)}</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Text size="xs" c="dimmed">{t('peakSeason')}</Text>
                          <Text fw={500} c="orange">
                            {rule.currency} {Math.round(rule.base_price * rule.seasonal_multipliers.peak_season)}
                          </Text>
                        </div>
                      </Group>

                      <Group justify="flex-end" mt="md">
                        <Button size="xs" variant="light" onClick={() => {
                          setSelectedRule(rule)
                          setCalculatorModalOpen(true)
                        }}>
                          {t('calculate')}
                        </Button>
                        <Button size="xs" color="blue" onClick={() => {
                          setSelectedRule(rule)
                          setViewModalOpen(true)
                        }}>
                          {t('viewDetails')}
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
              {pricingRules.filter(r => r.status === 'active').length === 0 && (
                <Grid.Col span={12}>
                  <Card withBorder>
                    <Stack align="center" py="xl">
                      <ThemeIcon size="xl" color="blue" variant="light">
                        <IconSettings size={24} />
                      </ThemeIcon>
                      <Text fw={500}>{t('noActiveRules')}</Text>
                      <Text size="sm" c="dimmed">{t('createPricingRulesToStart')}</Text>
                      <Button leftSection={<IconPlus size={16} />} onClick={() => setCreateModalOpen(true)}>
                        {t('createRule')}
                      </Button>
                    </Stack>
                  </Card>
                </Grid.Col>
              )}
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="draft">
            <Grid>
              {pricingRules
                .filter(r => r.status === 'draft')
                .map((rule) => (
                  <Grid.Col key={rule.id} span={{ base: 12, md: 6 }}>
                    <Card withBorder>
                      <Alert color="yellow" icon={<IconSettings size={16} />} mb="md">
                        <Text fw={500}>{t('draftRule')}</Text>
                        <Text size="sm">{t('ruleNotYetActive')}</Text>
                      </Alert>

                      <Group justify="space-between" mb="md">
                        <Group>
                          <Avatar color="yellow">
                            <IconTag size={16} />
                          </Avatar>
                          <div>
                            <Text fw={500}>{rule.rule_name}</Text>
                            <Text size="sm" c="dimmed">{rule.vehicle_category}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Text fw={700} size="lg">
                            {rule.currency} {rule.base_price}
                          </Text>
                          <Text size="xs" c="dimmed">/{t(rule.pricing_type)}</Text>
                        </div>
                      </Group>

                      <Group justify="flex-end" mt="md">
                        <Button size="xs" variant="light">
                          {t('edit')}
                        </Button>
                        <Button size="xs" color="green">
                          {t('activate')}
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
              {pricingRules.filter(r => r.status === 'draft').length === 0 && (
                <Grid.Col span={12}>
                  <Card withBorder>
                    <Stack align="center" py="xl">
                      <ThemeIcon size="xl" color="green" variant="light">
                        <IconCheck size={24} />
                      </ThemeIcon>
                      <Text fw={500}>{t('noDraftRules')}</Text>
                      <Text size="sm" c="dimmed">{t('allRulesAreActive')}</Text>
                    </Stack>
                  </Card>
                </Grid.Col>
              )}
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* Create Pricing Rule Modal */}
        <Modal
          opened={createModalOpen}
          onClose={() => setCreateModalOpen(false)}
          title={t('createPricingRule')}
          size="xl"
        >
          <Stack>
            <Alert color="blue" icon={<IconTag size={16} />}>
              <Text fw={500}>{t('pricingRuleInfo')}</Text>
              <Text size="sm">{t('pricingRuleDescription')}</Text>
            </Alert>

            <Grid>
              <Grid.Col span={12}>
                <TextInput
                  label={t('ruleName')}
                  placeholder={t('enterRuleName')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={4}>
                <Select
                  label={t('vehicleCategory')}
                  placeholder={t('selectCategory')}
                  data={[
                    { value: 'Economy', label: 'Economy' },
                    { value: 'Compact', label: 'Compact' },
                    { value: 'Mid-size', label: 'Mid-size' },
                    { value: 'Full-size', label: 'Full-size' },
                    { value: 'Luxury', label: 'Luxury' },
                    { value: 'Ultra-Luxury', label: 'Ultra-Luxury' },
                    { value: 'SUV', label: 'SUV' },
                    { value: 'SUV-Premium', label: 'SUV Premium' },
                    { value: 'Sports', label: 'Sports' },
                    { value: 'Exotic', label: 'Exotic' },
                    { value: 'Premium', label: 'Premium' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <Select
                  label={t('vehicleMake')}
                  placeholder={t('selectMake')}
                  data={[
                    { value: '', label: t('anyMake') },
                    { value: 'Toyota', label: 'Toyota' },
                    { value: 'BMW', label: 'BMW' },
                    { value: 'Mercedes-Benz', label: 'Mercedes-Benz' },
                    { value: 'Audi', label: 'Audi' },
                    { value: 'Nissan', label: 'Nissan' },
                    { value: 'Honda', label: 'Honda' },
                    { value: 'Hyundai', label: 'Hyundai' },
                    { value: 'Kia', label: 'Kia' },
                    { value: 'Ford', label: 'Ford' },
                    { value: 'Chevrolet', label: 'Chevrolet' },
                    { value: 'Land Rover', label: 'Land Rover' },
                    { value: 'Range Rover', label: 'Range Rover' },
                    { value: 'Porsche', label: 'Porsche' },
                    { value: 'Ferrari', label: 'Ferrari' },
                    { value: 'Lamborghini', label: 'Lamborghini' },
                    { value: 'McLaren', label: 'McLaren' },
                    { value: 'Bentley', label: 'Bentley' },
                    { value: 'Rolls Royce', label: 'Rolls Royce' },
                    { value: 'Maserati', label: 'Maserati' },
                    { value: 'Jaguar', label: 'Jaguar' },
                    { value: 'Volvo', label: 'Volvo' },
                    { value: 'Lexus', label: 'Lexus' },
                    { value: 'Infiniti', label: 'Infiniti' },
                    { value: 'Cadillac', label: 'Cadillac' },
                    { value: 'Genesis', label: 'Genesis' }
                  ]}
                  searchable
                  clearable
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <TextInput
                  label={t('vehicleModel')}
                  placeholder={t('enterModel')}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label={t('location')}
                  placeholder={t('enterLocation')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('pricingType')}
                  placeholder={t('selectPricingType')}
                  data={[
                    { value: 'daily', label: t('daily') },
                    { value: 'weekly', label: t('weekly') },
                    { value: 'monthly', label: t('monthly') },
                    { value: 'hourly', label: t('hourly') },
                    { value: 'seasonal', label: t('seasonal') },
                    { value: 'dynamic', label: t('dynamic') }
                  ]}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('basePrice')}
                  placeholder="0.00"
                  min={0}
                  decimalScale={2}
                  fixedDecimalScale
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('currency')}
                  placeholder={t('selectCurrency')}
                  data={[
                    { value: 'AED', label: 'AED - UAE Dirham' },
                    { value: 'USD', label: 'USD - US Dollar' },
                    { value: 'EUR', label: 'EUR - Euro' },
                    { value: 'GBP', label: 'GBP - British Pound' }
                  ]}
                  defaultValue="AED"
                  required
                />
              </Grid.Col>
            </Grid>

            <Divider label={t('seasonalMultipliers')} />

            <Grid>
              <Grid.Col span={3}>
                <NumberInput
                  label={t('peakSeason')}
                  placeholder="1.5"
                  min={0.1}
                  max={5}
                  step={0.1}
                  decimalScale={1}
                  defaultValue={1.5}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <NumberInput
                  label={t('highSeason')}
                  placeholder="1.3"
                  min={0.1}
                  max={5}
                  step={0.1}
                  decimalScale={1}
                  defaultValue={1.3}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <NumberInput
                  label={t('regularSeason')}
                  placeholder="1.0"
                  min={0.1}
                  max={5}
                  step={0.1}
                  decimalScale={1}
                  defaultValue={1.0}
                />
              </Grid.Col>
              <Grid.Col span={3}>
                <NumberInput
                  label={t('lowSeason')}
                  placeholder="0.8"
                  min={0.1}
                  max={5}
                  step={0.1}
                  decimalScale={1}
                  defaultValue={0.8}
                />
              </Grid.Col>
            </Grid>

            <Divider label={t('additionalFees')} />

            <Grid>
              <Grid.Col span={4}>
                <NumberInput
                  label={t('cleaningFee')}
                  placeholder="50"
                  min={0}
                  defaultValue={50}
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label={t('deliveryFee')}
                  placeholder="100"
                  min={0}
                  defaultValue={100}
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <NumberInput
                  label={t('airportSurcharge')}
                  placeholder="50"
                  min={0}
                  defaultValue={50}
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Group justify="space-between">
              <Group>
                <Checkbox label={t('activateImmediately')} />
                <Checkbox label={t('saveAsDraft')} defaultChecked />
              </Group>
              <Group>
                <Button variant="light" onClick={() => setCreateModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button leftSection={<IconCheck size={16} />} onClick={() => setCreateModalOpen(false)}>
                  {t('createRule')}
                </Button>
              </Group>
            </Group>
          </Stack>
        </Modal>

        {/* Edit Pricing Rule Modal */}
        <Modal
          opened={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          title={selectedRule ? `${t('editRule')}: ${selectedRule.rule_name}` : t('editPricingRule')}
          size="xl"
        >
          {selectedRule && (
            <Stack>
              <Alert color="blue" icon={<IconEdit size={16} />}>
                <Text fw={500}>{t('editingRule')}: {selectedRule.rule_name}</Text>
                <Text size="sm">{selectedRule.vehicle_category} • {selectedRule.location}</Text>
              </Alert>

              <Grid>
                <Grid.Col span={12}>
                  <TextInput
                    label={t('ruleName')}
                    placeholder={t('enterRuleName')}
                    defaultValue={selectedRule.rule_name}
                    required
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={4}>
                  <Select
                    label={t('vehicleCategory')}
                    placeholder={t('selectCategory')}
                    defaultValue={selectedRule.vehicle_category}
                    data={[
                      { value: 'Economy', label: 'Economy' },
                      { value: 'Compact', label: 'Compact' },
                      { value: 'Mid-size', label: 'Mid-size' },
                      { value: 'Full-size', label: 'Full-size' },
                      { value: 'Luxury', label: 'Luxury' },
                      { value: 'Ultra-Luxury', label: 'Ultra-Luxury' },
                      { value: 'SUV', label: 'SUV' },
                      { value: 'SUV-Premium', label: 'SUV Premium' },
                      { value: 'Sports', label: 'Sports' },
                      { value: 'Exotic', label: 'Exotic' },
                      { value: 'Premium', label: 'Premium' }
                    ]}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <Select
                    label={t('vehicleMake')}
                    placeholder={t('selectMake')}
                    defaultValue={selectedRule.vehicle_make || ''}
                    data={[
                      { value: '', label: t('anyMake') },
                      { value: 'Toyota', label: 'Toyota' },
                      { value: 'BMW', label: 'BMW' },
                      { value: 'Mercedes-Benz', label: 'Mercedes-Benz' },
                      { value: 'Audi', label: 'Audi' },
                      { value: 'Nissan', label: 'Nissan' },
                      { value: 'Honda', label: 'Honda' },
                      { value: 'Hyundai', label: 'Hyundai' },
                      { value: 'Kia', label: 'Kia' },
                      { value: 'Ford', label: 'Ford' },
                      { value: 'Chevrolet', label: 'Chevrolet' },
                      { value: 'Land Rover', label: 'Land Rover' },
                      { value: 'Range Rover', label: 'Range Rover' },
                      { value: 'Porsche', label: 'Porsche' },
                      { value: 'Ferrari', label: 'Ferrari' },
                      { value: 'Lamborghini', label: 'Lamborghini' },
                      { value: 'McLaren', label: 'McLaren' },
                      { value: 'Bentley', label: 'Bentley' },
                      { value: 'Rolls Royce', label: 'Rolls Royce' },
                      { value: 'Maserati', label: 'Maserati' },
                      { value: 'Jaguar', label: 'Jaguar' },
                      { value: 'Volvo', label: 'Volvo' },
                      { value: 'Lexus', label: 'Lexus' },
                      { value: 'Infiniti', label: 'Infiniti' },
                      { value: 'Cadillac', label: 'Cadillac' },
                      { value: 'Genesis', label: 'Genesis' }
                    ]}
                    searchable
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <TextInput
                    label={t('vehicleModel')}
                    placeholder={t('enterModel')}
                    defaultValue={selectedRule.vehicle_model || ''}
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label={t('location')}
                    placeholder={t('enterLocation')}
                    defaultValue={selectedRule.location}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Select
                    label={t('pricingType')}
                    placeholder={t('selectPricingType')}
                    defaultValue={selectedRule.pricing_type}
                    data={[
                      { value: 'daily', label: t('daily') },
                      { value: 'weekly', label: t('weekly') },
                      { value: 'monthly', label: t('monthly') },
                      { value: 'hourly', label: t('hourly') },
                      { value: 'seasonal', label: t('seasonal') },
                      { value: 'dynamic', label: t('dynamic') }
                    ]}
                    required
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <NumberInput
                    label={t('basePrice')}
                    placeholder="0.00"
                    min={0}
                    decimalScale={2}
                    fixedDecimalScale
                    defaultValue={selectedRule.base_price}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Select
                    label={t('currency')}
                    placeholder={t('selectCurrency')}
                    defaultValue={selectedRule.currency}
                    data={[
                      { value: 'AED', label: 'AED - UAE Dirham' },
                      { value: 'USD', label: 'USD - US Dollar' },
                      { value: 'EUR', label: 'EUR - Euro' },
                      { value: 'GBP', label: 'GBP - British Pound' }
                    ]}
                    required
                  />
                </Grid.Col>
              </Grid>

              <Divider label={t('seasonalMultipliers')} />

              <Grid>
                <Grid.Col span={3}>
                  <NumberInput
                    label={t('peakSeason')}
                    placeholder="1.5"
                    min={0.1}
                    max={5}
                    step={0.1}
                    decimalScale={1}
                    defaultValue={selectedRule.seasonal_multipliers.peak_season}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <NumberInput
                    label={t('highSeason')}
                    placeholder="1.3"
                    min={0.1}
                    max={5}
                    step={0.1}
                    decimalScale={1}
                    defaultValue={selectedRule.seasonal_multipliers.high_season}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <NumberInput
                    label={t('regularSeason')}
                    placeholder="1.0"
                    min={0.1}
                    max={5}
                    step={0.1}
                    decimalScale={1}
                    defaultValue={selectedRule.seasonal_multipliers.regular_season}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <NumberInput
                    label={t('lowSeason')}
                    placeholder="0.8"
                    min={0.1}
                    max={5}
                    step={0.1}
                    decimalScale={1}
                    defaultValue={selectedRule.seasonal_multipliers.low_season}
                  />
                </Grid.Col>
              </Grid>

              <Divider label={t('additionalFees')} />

              <Grid>
                <Grid.Col span={4}>
                  <NumberInput
                    label={t('cleaningFee')}
                    placeholder="50"
                    min={0}
                    defaultValue={selectedRule.additional_fees.cleaning_fee}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <NumberInput
                    label={t('deliveryFee')}
                    placeholder="100"
                    min={0}
                    defaultValue={selectedRule.additional_fees.delivery_fee}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <NumberInput
                    label={t('airportSurcharge')}
                    placeholder="50"
                    min={0}
                    defaultValue={selectedRule.additional_fees.airport_surcharge}
                  />
                </Grid.Col>
              </Grid>

              <Divider />

              <Group justify="space-between">
                <Group>
                  <Switch
                    label={t('activeRule')}
                    defaultChecked={selectedRule.status === 'active'}
                  />
                </Group>
                <Group>
                  <Button variant="light" onClick={() => setEditModalOpen(false)}>
                    {t('cancel')}
                  </Button>
                  <Button leftSection={<IconCheck size={16} />} onClick={() => setEditModalOpen(false)}>
                    {t('saveChanges')}
                  </Button>
                </Group>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* View Pricing Rule Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedRule ? selectedRule.rule_name : t('pricingRuleDetails')}
          size="xl"
        >
          {selectedRule && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('vehicleCategory')}</Text>
                      <Text fw={500}>{selectedRule.vehicle_category}</Text>
                      {(selectedRule.vehicle_make || selectedRule.vehicle_model) && (
                        <div>
                          <Text size="xs" c="dimmed" mt="xs">
                            {selectedRule.vehicle_make && `${selectedRule.vehicle_make}`}
                            {selectedRule.vehicle_make && selectedRule.vehicle_model && ' '}
                            {selectedRule.vehicle_model && `${selectedRule.vehicle_model}`}
                          </Text>
                        </div>
                      )}
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('location')}</Text>
                      <Text fw={500}>{selectedRule.location}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('pricingType')}</Text>
                      <Badge color={getTypeColor(selectedRule.pricing_type)}>
                        {t(selectedRule.pricing_type)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('effectiveFrom')}</Text>
                      <Text fw={500}>
                        {new Date(selectedRule.effective_from).toLocaleDateString()}
                      </Text>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <ThemeIcon size="xl" color={getStatusColor(selectedRule.status)}>
                        <IconCurrencyDollar size={24} />
                      </ThemeIcon>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="xl">
                          {selectedRule.currency} {selectedRule.base_price}
                        </Text>
                        <Text size="sm" c="dimmed">/{t(selectedRule.pricing_type)}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <div>
                <Text fw={500} mb="md">{t('seasonalPricing')}</Text>
                <Grid>
                  <Grid.Col span={3}>
                    <Card withBorder p="sm">
                      <Text size="xs" c="dimmed">{t('peakSeason')}</Text>
                      <Text fw={700} c="red">
                        {selectedRule.currency} {Math.round(selectedRule.base_price * selectedRule.seasonal_multipliers.peak_season)}
                      </Text>
                      <Text size="xs" c="dimmed">x{selectedRule.seasonal_multipliers.peak_season}</Text>
                    </Card>
                  </Grid.Col>
                  <Grid.Col span={3}>
                    <Card withBorder p="sm">
                      <Text size="xs" c="dimmed">{t('highSeason')}</Text>
                      <Text fw={700} c="orange">
                        {selectedRule.currency} {Math.round(selectedRule.base_price * selectedRule.seasonal_multipliers.high_season)}
                      </Text>
                      <Text size="xs" c="dimmed">x{selectedRule.seasonal_multipliers.high_season}</Text>
                    </Card>
                  </Grid.Col>
                  <Grid.Col span={3}>
                    <Card withBorder p="sm">
                      <Text size="xs" c="dimmed">{t('regularSeason')}</Text>
                      <Text fw={700} c="blue">
                        {selectedRule.currency} {Math.round(selectedRule.base_price * selectedRule.seasonal_multipliers.regular_season)}
                      </Text>
                      <Text size="xs" c="dimmed">x{selectedRule.seasonal_multipliers.regular_season}</Text>
                    </Card>
                  </Grid.Col>
                  <Grid.Col span={3}>
                    <Card withBorder p="sm">
                      <Text size="xs" c="dimmed">{t('lowSeason')}</Text>
                      <Text fw={700} c="green">
                        {selectedRule.currency} {Math.round(selectedRule.base_price * selectedRule.seasonal_multipliers.low_season)}
                      </Text>
                      <Text size="xs" c="dimmed">x{selectedRule.seasonal_multipliers.low_season}</Text>
                    </Card>
                  </Grid.Col>
                </Grid>
              </div>

              <div>
                <Text fw={500} mb="md">{t('additionalFees')}</Text>
                <Grid>
                  <Grid.Col span={4}>
                    <Text size="sm" c="dimmed">{t('cleaningFee')}</Text>
                    <Text fw={500}>{selectedRule.currency} {selectedRule.additional_fees.cleaning_fee}</Text>
                  </Grid.Col>
                  <Grid.Col span={4}>
                    <Text size="sm" c="dimmed">{t('deliveryFee')}</Text>
                    <Text fw={500}>{selectedRule.currency} {selectedRule.additional_fees.delivery_fee}</Text>
                  </Grid.Col>
                  <Grid.Col span={4}>
                    <Text size="sm" c="dimmed">{t('airportSurcharge')}</Text>
                    <Text fw={500}>{selectedRule.currency} {selectedRule.additional_fees.airport_surcharge}</Text>
                  </Grid.Col>
                </Grid>
              </div>

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" leftSection={<IconCalculator size={16} />} onClick={() => {
                  setViewModalOpen(false)
                  setCalculatorModalOpen(true)
                }}>
                  {t('priceCalculator')}
                </Button>
                <Button variant="light" leftSection={<IconEdit size={16} />} onClick={() => {
                  setViewModalOpen(false)
                  setEditModalOpen(true)
                }}>
                  {t('editRule')}
                </Button>
                <Button leftSection={<IconCopy size={16} />}>
                  {t('duplicateRule')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Price Calculator Modal */}
        <Modal
          opened={calculatorModalOpen}
          onClose={() => setCalculatorModalOpen(false)}
          title={t('priceCalculator')}
          size="lg"
        >
          {selectedRule && (
            <Stack>
              <Select
                label={t('selectPricingRule')}
                placeholder={t('choosePricingRule')}
                value={selectedRule.id}
                onChange={(value) => {
                  const rule = pricingRules.find(r => r.id === value)
                  if (rule) setSelectedRule(rule)
                }}
                data={pricingRules.map(rule => ({
                  value: rule.id,
                  label: `${rule.rule_name} - ${rule.vehicle_category} (${rule.currency} ${rule.base_price})`
                }))}
              />

              <Alert color="blue" icon={<IconCalculator size={16} />}>
                <Text fw={500}>{t('calculatingPriceFor')}: {selectedRule.rule_name}</Text>
                <Text size="sm">{selectedRule.vehicle_category} • {selectedRule.location}</Text>
              </Alert>

              <Grid>
                <Grid.Col span={6}>
                  <DateInput
                    label={t('startDate')}
                    placeholder={t('selectStartDate')}
                    defaultValue={new Date()}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <DateInput
                    label={t('endDate')}
                    placeholder={t('selectEndDate')}
                    defaultValue={new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)}
                    required
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <NumberInput
                    label={t('numberOfDays')}
                    placeholder="3"
                    min={1}
                    max={365}
                    value={calculatorDays}
                    onChange={(value) => setCalculatorDays(Number(value) || 3)}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Select
                    label={t('season')}
                    placeholder={t('selectSeason')}
                    value={calculatorSeason}
                    onChange={(value) => setCalculatorSeason(value || 'regular_season')}
                    data={[
                      { value: 'peak_season', label: t('peakSeason') },
                      { value: 'high_season', label: t('highSeason') },
                      { value: 'regular_season', label: t('regularSeason') },
                      { value: 'low_season', label: t('lowSeason') }
                    ]}
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <NumberInput
                    label={t('customerAge')}
                    placeholder="25"
                    min={18}
                    max={99}
                    defaultValue={25}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Select
                    label={t('insuranceType')}
                    placeholder={t('selectInsurance')}
                    value={calculatorInsurance}
                    onChange={(value) => setCalculatorInsurance(value || 'basic')}
                    data={[
                      { value: 'basic', label: t('basicInsurance') },
                      { value: 'comprehensive', label: t('comprehensiveInsurance') },
                      { value: 'premium', label: t('premiumInsurance') }
                    ]}
                  />
                </Grid.Col>
              </Grid>

              <Divider />

              <Card withBorder>
                <Text fw={500} mb="md">{t('priceBreakdown')}</Text>
                <Stack gap="xs">
                  {(() => {
                    const baseAmount = selectedRule.base_price * calculatorDays
                    const seasonMultiplier = selectedRule.seasonal_multipliers[calculatorSeason as keyof typeof selectedRule.seasonal_multipliers]
                    const seasonalAdjustment = baseAmount * (seasonMultiplier - 1)
                    const insuranceRate = calculatorInsurance === 'basic' ? 15 : calculatorInsurance === 'comprehensive' ? 35 : 55
                    const insuranceAmount = insuranceRate * calculatorDays
                    const additionalFees = selectedRule.additional_fees.cleaning_fee
                    const subtotal = baseAmount + seasonalAdjustment + insuranceAmount + additionalFees

                    return (
                      <>
                        <Group justify="space-between">
                          <Text size="sm">{t('basePrice')} ({calculatorDays} {calculatorDays === 1 ? t('day') : t('days')})</Text>
                          <Text fw={500}>{selectedRule.currency} {baseAmount.toLocaleString()}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">{t('seasonalAdjustment')} ({t(calculatorSeason)} x{seasonMultiplier})</Text>
                          <Text fw={500} c={seasonalAdjustment >= 0 ? "red" : "green"}>
                            {seasonalAdjustment >= 0 ? '+' : ''}{selectedRule.currency} {Math.round(seasonalAdjustment).toLocaleString()}
                          </Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">{t('insurance')} ({t(calculatorInsurance + 'Insurance')})</Text>
                          <Text fw={500}>+{selectedRule.currency} {insuranceAmount.toLocaleString()}</Text>
                        </Group>
                        <Group justify="space-between">
                          <Text size="sm">{t('additionalFees')}</Text>
                          <Text fw={500}>+{selectedRule.currency} {additionalFees.toLocaleString()}</Text>
                        </Group>
                        <Divider />
                        <Group justify="space-between">
                          <Text fw={700}>{t('totalPrice')}</Text>
                          <Text fw={700} size="lg" c="blue">
                            {selectedRule.currency} {Math.round(subtotal).toLocaleString()}
                          </Text>
                        </Group>
                      </>
                    )
                  })()}
                </Stack>
              </Card>

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setCalculatorModalOpen(false)}>
                  {t('close')}
                </Button>
                <Button leftSection={<IconCheck size={16} />}>
                  {t('createQuote')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
