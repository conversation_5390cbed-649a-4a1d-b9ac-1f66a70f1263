import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Center,
  Progress,
  Alert,
  RingProgress,
  Timeline,
  Tooltip,
  Switch,
  Indicator
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconTrash,
  IconPhone,
  IconMail,
  IconMapPin,
  IconCalendar,
  IconCurrencyDollar,
  IconTrendingUp,
  IconUsers,
  IconCalendarEvent,
  IconCreditCard,
  IconFileText,
  IconRefresh,
  IconGasStation,
  IconRoad,
  IconGauge,
  IconLocation,
  IconFlag,
  IconMessage,
  IconBell
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface ActiveRentalTableData {
  id: string
  reservation_id: string
  reservation_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  customer_emergency_contact?: string
  vehicle_id: string
  vehicle_name: string
  vehicle_make: string
  vehicle_model: string
  vehicle_year: number
  plate_number: string
  vin_number: string
  pickup_date: string
  return_date: string
  actual_pickup_date: string
  expected_return_date: string
  pickup_location: string
  return_location: string
  current_location?: string
  status: 'active' | 'overdue' | 'due_today' | 'due_tomorrow' | 'extended' | 'early_return'
  rental_days: number
  days_remaining: number
  total_amount: number
  paid_amount: number
  outstanding_balance: number
  daily_rate: number
  mileage_start: number
  mileage_current?: number
  mileage_limit?: number
  fuel_level_start: number
  fuel_level_current?: number
  fuel_policy: 'full_to_full' | 'same_to_same' | 'prepaid'
  insurance_type: string
  deposit_amount: number
  deposit_status: 'held' | 'released' | 'partial_release'
  gps_tracking: boolean
  last_location_update?: string
  driver_license_verified: boolean
  additional_drivers: string[]
  special_instructions?: string
  alerts: string[]
  violations: string[]
  maintenance_due?: boolean
  created_by: string
  created_at: string
  updated_at: string
}

export function ActiveRentalsComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [activeRentals, setActiveRentals] = useState<ActiveRentalTableData[]>([])
  const [activeTab, setActiveTab] = useState('all-rentals')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [alertFilter, setAlertFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [extendModalOpen, setExtendModalOpen] = useState(false)
  const [alertModalOpen, setAlertModalOpen] = useState(false)
  const [contactModalOpen, setContactModalOpen] = useState(false)
  const [selectedRental, setSelectedRental] = useState<ActiveRentalTableData | null>(null)

  // Mock data for development - comprehensive active rental structure
  const mockActiveRentals: ActiveRentalTableData[] = [
    {
      id: '1',
      reservation_id: '1',
      reservation_number: 'RES-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971501234567',
      customer_emergency_contact: '+971501234568',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      vehicle_make: 'Toyota',
      vehicle_model: 'Camry',
      vehicle_year: 2023,
      plate_number: 'ABC-123',
      vin_number: 'JT2BF22K123456789',
      pickup_date: '2024-01-15T09:00:00Z',
      return_date: '2024-01-20T18:00:00Z',
      actual_pickup_date: '2024-01-15T09:15:00Z',
      expected_return_date: '2024-01-20T18:00:00Z',
      pickup_location: 'Dubai Airport Terminal 1',
      return_location: 'Dubai Airport Terminal 1',
      current_location: 'Dubai Marina',
      status: 'due_today',
      rental_days: 5,
      days_remaining: 0,
      total_amount: 630,
      paid_amount: 630,
      outstanding_balance: 0,
      daily_rate: 120,
      mileage_start: 15000,
      mileage_current: 15420,
      mileage_limit: 16000,
      fuel_level_start: 100,
      fuel_level_current: 75,
      fuel_policy: 'full_to_full',
      insurance_type: 'Comprehensive',
      deposit_amount: 500,
      deposit_status: 'held',
      gps_tracking: true,
      last_location_update: '2024-01-20T14:30:00Z',
      driver_license_verified: true,
      additional_drivers: [],
      special_instructions: 'Customer prefers white vehicle',
      alerts: ['due_today', 'fuel_low'],
      violations: [],
      maintenance_due: false,
      created_by: 'admin',
      created_at: '2024-01-15T08:00:00Z',
      updated_at: '2024-01-20T14:30:00Z'
    },
    {
      id: '2',
      reservation_id: '2',
      reservation_number: 'RES-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+**********',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      vehicle_make: 'BMW',
      vehicle_model: 'X5',
      vehicle_year: 2022,
      plate_number: 'XYZ-456',
      vin_number: 'WBAFR7C50BC123456',
      pickup_date: '2024-01-10T10:00:00Z',
      return_date: '2024-01-18T17:00:00Z',
      actual_pickup_date: '2024-01-10T10:30:00Z',
      expected_return_date: '2024-01-18T17:00:00Z',
      pickup_location: 'Downtown Dubai',
      return_location: 'Downtown Dubai',
      current_location: 'Abu Dhabi',
      status: 'overdue',
      rental_days: 8,
      days_remaining: -2,
      total_amount: 1050,
      paid_amount: 1050,
      outstanding_balance: 120, // Late fees
      daily_rate: 130,
      mileage_start: 28000,
      mileage_current: 28750,
      mileage_limit: 29000,
      fuel_level_start: 100,
      fuel_level_current: 45,
      fuel_policy: 'full_to_full',
      insurance_type: 'Premium',
      deposit_amount: 1000,
      deposit_status: 'held',
      gps_tracking: true,
      last_location_update: '2024-01-20T16:45:00Z',
      driver_license_verified: true,
      additional_drivers: ['John Smith'],
      alerts: ['overdue', 'fuel_low', 'mileage_warning'],
      violations: ['speeding_violation'],
      maintenance_due: false,
      created_by: 'admin',
      created_at: '2024-01-10T09:00:00Z',
      updated_at: '2024-01-20T16:45:00Z'
    },
    {
      id: '3',
      reservation_id: '3',
      reservation_number: 'RES-2024-003',
      customer_id: '3',
      customer_name: 'Mohammed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+971509876543',
      vehicle_id: '3',
      vehicle_name: 'Mercedes C-Class 2023',
      vehicle_make: 'Mercedes',
      vehicle_model: 'C-Class',
      vehicle_year: 2023,
      plate_number: 'DEF-789',
      vin_number: 'WDD2050461A123456',
      pickup_date: '2024-01-16T14:00:00Z',
      return_date: '2024-01-21T12:00:00Z',
      actual_pickup_date: '2024-01-16T14:15:00Z',
      expected_return_date: '2024-01-21T12:00:00Z',
      pickup_location: 'Abu Dhabi Mall',
      return_location: 'Abu Dhabi Mall',
      current_location: 'Al Ain',
      status: 'due_tomorrow',
      rental_days: 5,
      days_remaining: 1,
      total_amount: 945,
      paid_amount: 450,
      outstanding_balance: 495,
      daily_rate: 180,
      mileage_start: 12000,
      mileage_current: 12340,
      mileage_limit: 13000,
      fuel_level_start: 100,
      fuel_level_current: 90,
      fuel_policy: 'full_to_full',
      insurance_type: 'Standard',
      deposit_amount: 750,
      deposit_status: 'held',
      gps_tracking: true,
      last_location_update: '2024-01-20T18:20:00Z',
      driver_license_verified: true,
      additional_drivers: [],
      special_instructions: 'First time customer - requires verification',
      alerts: ['payment_due'],
      violations: [],
      maintenance_due: false,
      created_by: 'admin',
      created_at: '2024-01-16T13:00:00Z',
      updated_at: '2024-01-20T18:20:00Z'
    }
  ]

  useEffect(() => {
    loadActiveRentals()
  }, [])

  const loadActiveRentals = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await activeRentalService.getAllActiveRentals()
      setActiveRentals(mockActiveRentals)
    } catch (error) {
      console.error('Error loading active rentals:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredRentals = activeRentals.filter(rental => {
    const matchesSearch = !searchQuery || 
      rental.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.reservation_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.plate_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.vehicle_name.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || rental.status === statusFilter
    const matchesLocation = !locationFilter || 
      rental.current_location?.toLowerCase().includes(locationFilter.toLowerCase()) ||
      rental.pickup_location.toLowerCase().includes(locationFilter.toLowerCase())
    const matchesAlert = !alertFilter || rental.alerts.includes(alertFilter)
    
    return matchesSearch && matchesStatus && matchesLocation && matchesAlert
  })

  // Pagination
  const totalItems = filteredRentals.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedRentals = filteredRentals.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const totalRevenue = activeRentals.reduce((sum, r) => sum + r.paid_amount, 0)
  const overdueRentals = activeRentals.filter(r => r.status === 'overdue').length
  const dueTodayRentals = activeRentals.filter(r => r.status === 'due_today').length
  const totalOutstanding = activeRentals.reduce((sum, r) => sum + r.outstanding_balance, 0)

  const stats = [
    {
      label: t('totalActiveRentals'),
      value: activeRentals.length.toString(),
      color: 'blue',
      icon: IconCar
    },
    {
      label: t('dueToday'),
      value: dueTodayRentals.toString(),
      color: 'orange',
      icon: IconClock
    },
    {
      label: t('overdueRentals'),
      value: overdueRentals.toString(),
      color: 'red',
      icon: IconAlertTriangle
    },
    {
      label: t('activeRevenue'),
      value: `$${totalRevenue.toLocaleString()}`,
      color: 'green',
      icon: IconTrendingUp
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'overdue': return 'red'
      case 'due_today': return 'orange'
      case 'due_tomorrow': return 'yellow'
      case 'extended': return 'blue'
      case 'early_return': return 'cyan'
      default: return 'gray'
    }
  }

  const getAlertColor = (alert: string) => {
    switch (alert) {
      case 'overdue': return 'red'
      case 'due_today': return 'orange'
      case 'fuel_low': return 'yellow'
      case 'mileage_warning': return 'orange'
      case 'payment_due': return 'red'
      case 'maintenance_due': return 'blue'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getFuelLevelColor = (level: number) => {
    if (level >= 75) return 'green'
    if (level >= 50) return 'yellow'
    if (level >= 25) return 'orange'
    return 'red'
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('activeRentalsManagement')}</Title>
              <Text c="dimmed" size="sm">{t('monitorAndManageAllActiveRentals')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconRefresh size={16} />}>
                {t('refresh')}
              </Button>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-rentals">{t('allActiveRentals')}</Tabs.Tab>
            <Tabs.Tab value="alerts">{t('alerts')}</Tabs.Tab>
            <Tabs.Tab value="map">{t('mapView')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-rentals">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder={t('searchActiveRentals')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'active', label: t('active') },
                      { value: 'due_today', label: t('dueToday') },
                      { value: 'due_tomorrow', label: t('dueTomorrow') },
                      { value: 'overdue', label: t('overdue') },
                      { value: 'extended', label: t('extended') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <TextInput
                    placeholder={t('location')}
                    leftSection={<IconMapPin size={16} />}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('alerts')}
                    leftSection={<IconBell size={16} />}
                    value={alertFilter}
                    onChange={(value) => setAlertFilter(value || '')}
                    data={[
                      { value: '', label: t('allAlerts') },
                      { value: 'overdue', label: t('overdue') },
                      { value: 'due_today', label: t('dueToday') },
                      { value: 'fuel_low', label: t('fuelLow') },
                      { value: 'mileage_warning', label: t('mileageWarning') },
                      { value: 'payment_due', label: t('paymentDue') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setLocationFilter('')
                      setAlertFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Active Rentals Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('vehicle')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('location')}</Table.Th>
                    <Table.Th>{t('daysRemaining')}</Table.Th>
                    <Table.Th>{t('fuel')}</Table.Th>
                    <Table.Th>{t('mileage')}</Table.Th>
                    <Table.Th>{t('alerts')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedRentals.map((rental) => (
                    <Table.Tr key={rental.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color={getStatusColor(rental.status)}>
                            {getInitials(rental.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{rental.customer_name}</Text>
                            <Text size="xs" c="dimmed">{rental.reservation_number}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{rental.vehicle_name}</Text>
                          <Text size="xs" c="dimmed">{rental.plate_number}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(rental.status)} size="sm">
                          {t(rental.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">{rental.current_location || rental.pickup_location}</Text>
                          {rental.gps_tracking && rental.last_location_update && (
                            <Text size="xs" c="dimmed">
                              {t('updated')}: {new Date(rental.last_location_update).toLocaleTimeString()}
                            </Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text
                            fw={500}
                            size="sm"
                            c={rental.days_remaining < 0 ? 'red' : rental.days_remaining === 0 ? 'orange' : 'dark'}
                          >
                            {rental.days_remaining < 0
                              ? `${Math.abs(rental.days_remaining)} ${t('daysOverdue')}`
                              : rental.days_remaining === 0
                                ? t('dueToday')
                                : `${rental.days_remaining} ${t('daysLeft')}`
                            }
                          </Text>
                          <Text size="xs" c="dimmed">
                            {t('due')}: {new Date(rental.expected_return_date).toLocaleDateString()}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Group gap="xs">
                            <IconGasStation size={14} color={`var(--mantine-color-${getFuelLevelColor(rental.fuel_level_current || 0)}-6)`} />
                            <Text size="sm">{rental.fuel_level_current || 0}%</Text>
                          </Group>
                          <Progress
                            value={rental.fuel_level_current || 0}
                            size="xs"
                            color={getFuelLevelColor(rental.fuel_level_current || 0)}
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Group gap="xs">
                            <IconRoad size={14} />
                            <Text size="sm">{rental.mileage_current?.toLocaleString() || 'N/A'}</Text>
                          </Group>
                          {rental.mileage_limit && rental.mileage_current && (
                            <Progress
                              value={(rental.mileage_current / rental.mileage_limit) * 100}
                              size="xs"
                              color={rental.mileage_current > rental.mileage_limit * 0.9 ? 'red' : 'blue'}
                            />
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          {rental.alerts.slice(0, 2).map((alert, index) => (
                            <Indicator key={index} size={6} color={getAlertColor(alert)}>
                              <Badge size="xs" color={getAlertColor(alert)}>
                                {t(alert)}
                              </Badge>
                            </Indicator>
                          ))}
                          {rental.alerts.length > 2 && (
                            <Text size="xs" c="dimmed">+{rental.alerts.length - 2}</Text>
                          )}
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedRental(rental)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedRental(rental)
                              setContactModalOpen(true)
                            }}
                          >
                            <IconPhone size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="green"
                            onClick={() => {
                              setSelectedRental(rental)
                              setExtendModalOpen(true)
                            }}
                          >
                            <IconCalendar size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="orange"
                          >
                            <IconMapPin size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('activeRentals')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="alerts">
            <Card withBorder>
              <Title order={4} mb="md">{t('activeAlertsAndNotifications')}</Title>
              <Stack>
                {activeRentals.filter(r => r.alerts.length > 0).map((rental) => (
                  <Alert
                    key={rental.id}
                    color={getAlertColor(rental.alerts[0])}
                    icon={<IconBell size={16} />}
                  >
                    <Group justify="space-between">
                      <div>
                        <Text fw={500}>{rental.customer_name} - {rental.vehicle_name}</Text>
                        <Text size="sm">
                          {rental.alerts.map(alert => t(alert)).join(', ')}
                        </Text>
                      </div>
                      <Button size="xs" variant="light">
                        {t('resolve')}
                      </Button>
                    </Group>
                  </Alert>
                ))}
              </Stack>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="map">
            <Card withBorder>
              <Center py="xl">
                <Stack align="center">
                  <IconMapPin size={48} color="var(--mantine-color-gray-5)" />
                  <Title order={4} c="dimmed">{t('vehicleLocationMap')}</Title>
                  <Text c="dimmed" ta="center">
                    {t('realTimeVehicleTrackingWillBeImplementedHere')}
                  </Text>
                </Stack>
              </Center>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* View Rental Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedRental ? selectedRental.reservation_number : t('rentalDetails')}
          size="xl"
        >
          {selectedRental && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('customer')}</Text>
                      <Text fw={500}>{selectedRental.customer_name}</Text>
                      <Text size="xs" c="dimmed">{selectedRental.customer_email}</Text>
                      <Text size="xs" c="dimmed">{selectedRental.customer_phone}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('vehicle')}</Text>
                      <Text fw={500}>{selectedRental.vehicle_name}</Text>
                      <Text size="xs" c="dimmed">{selectedRental.plate_number}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('status')}</Text>
                      <Badge color={getStatusColor(selectedRental.status)}>
                        {t(selectedRental.status)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('currentLocation')}</Text>
                      <Text fw={500}>{selectedRental.current_location || t('unknown')}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('pickupDate')}</Text>
                      <Text fw={500}>{new Date(selectedRental.actual_pickup_date).toLocaleDateString()}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('returnDate')}</Text>
                      <Text fw={500}>{new Date(selectedRental.expected_return_date).toLocaleDateString()}</Text>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <Avatar size="xl" color={getStatusColor(selectedRental.status)}>
                        {getInitials(selectedRental.customer_name)}
                      </Avatar>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="lg">{selectedRental.customer_name}</Text>
                        <Text size="sm" c="dimmed">{selectedRental.reservation_number}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <Grid>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('fuelLevel')}</Text>
                  <Group gap="xs">
                    <IconGasStation size={16} color={`var(--mantine-color-${getFuelLevelColor(selectedRental.fuel_level_current || 0)}-6)`} />
                    <Text fw={700}>{selectedRental.fuel_level_current || 0}%</Text>
                  </Group>
                  <Progress value={selectedRental.fuel_level_current || 0} size="sm" color={getFuelLevelColor(selectedRental.fuel_level_current || 0)} />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('mileage')}</Text>
                  <Group gap="xs">
                    <IconRoad size={16} />
                    <Text fw={700}>{selectedRental.mileage_current?.toLocaleString() || 'N/A'}</Text>
                  </Group>
                  <Text size="xs" c="dimmed">{t('limit')}: {selectedRental.mileage_limit?.toLocaleString() || 'Unlimited'}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('totalAmount')}</Text>
                  <Text fw={700} size="xl" c="green">${selectedRental.total_amount}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('outstandingBalance')}</Text>
                  <Text fw={700} size="xl" c={selectedRental.outstanding_balance > 0 ? 'red' : 'green'}>
                    ${selectedRental.outstanding_balance}
                  </Text>
                </Grid.Col>
              </Grid>

              {selectedRental.alerts.length > 0 && (
                <>
                  <Divider />
                  <div>
                    <Text size="sm" c="dimmed" mb="xs">{t('activeAlerts')}</Text>
                    <Group gap="xs">
                      {selectedRental.alerts.map((alert, index) => (
                        <Badge key={index} color={getAlertColor(alert)}>
                          {t(alert)}
                        </Badge>
                      ))}
                    </Group>
                  </div>
                </>
              )}

              {selectedRental.special_instructions && (
                <>
                  <Divider />
                  <div>
                    <Text size="sm" c="dimmed">{t('specialInstructions')}</Text>
                    <Text>{selectedRental.special_instructions}</Text>
                  </div>
                </>
              )}
            </Stack>
          )}
        </Modal>

        {/* Contact Customer Modal */}
        <Modal
          opened={contactModalOpen}
          onClose={() => setContactModalOpen(false)}
          title={t('contactCustomer')}
          size="md"
        >
          {selectedRental && (
            <Stack>
              <Group>
                <Avatar size="lg" color={getStatusColor(selectedRental.status)}>
                  {getInitials(selectedRental.customer_name)}
                </Avatar>
                <div>
                  <Text fw={500} size="lg">{selectedRental.customer_name}</Text>
                  <Text size="sm" c="dimmed">{selectedRental.reservation_number}</Text>
                </div>
              </Group>

              <Divider />

              <Grid>
                <Grid.Col span={6}>
                  <Button
                    fullWidth
                    leftSection={<IconPhone size={16} />}
                    variant="light"
                    color="blue"
                  >
                    {t('callCustomer')}
                  </Button>
                  <Text size="xs" c="dimmed" ta="center" mt="xs">
                    {selectedRental.customer_phone}
                  </Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Button
                    fullWidth
                    leftSection={<IconMail size={16} />}
                    variant="light"
                    color="green"
                  >
                    {t('emailCustomer')}
                  </Button>
                  <Text size="xs" c="dimmed" ta="center" mt="xs">
                    {selectedRental.customer_email}
                  </Text>
                </Grid.Col>
              </Grid>

              <Textarea
                label={t('quickMessage')}
                placeholder={t('typeMessageToCustomer')}
                rows={3}
              />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setContactModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button leftSection={<IconMessage size={16} />}>
                  {t('sendMessage')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Extend Rental Modal */}
        <Modal
          opened={extendModalOpen}
          onClose={() => setExtendModalOpen(false)}
          title={t('extendRental')}
          size="md"
        >
          {selectedRental && (
            <Stack>
              <Alert color="blue" icon={<IconCalendar size={16} />}>
                <Text fw={500}>{t('extendRentalPeriod')}</Text>
                <Text size="sm">
                  {t('currentReturnDate')}: {new Date(selectedRental.expected_return_date).toLocaleDateString()}
                </Text>
              </Alert>

              <NumberInput
                label={t('additionalDays')}
                placeholder="0"
                min={1}
                max={30}
                required
              />

              <TextInput
                label={t('newReturnDate')}
                placeholder={t('selectNewReturnDate')}
                type="date"
                required
              />

              <NumberInput
                label={t('additionalAmount')}
                placeholder="0"
                min={0}
                prefix="$"
                description={t('additionalChargesForExtension')}
              />

              <Textarea
                label={t('extensionReason')}
                placeholder={t('enterReasonForExtension')}
                rows={2}
              />

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setExtendModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button leftSection={<IconCheck size={16} />} onClick={() => setExtendModalOpen(false)}>
                  {t('extendRental')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
