import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEye,
  IconKey,
  IconMail,
  IconPhone,
  IconPlus,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface CheckOutReservation {
  id: string
  reservationNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  returnDate: string
  returnTime: string
  status: 'due-today' | 'overdue' | 'returned'
  location: string
  totalAmount: number
}

export function CheckOutSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const reservations: CheckOutReservation[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      returnDate: '2024-01-20',
      returnTime: '05:00 PM',
      status: 'due-today',
      location: 'Dubai Airport',
      totalAmount: 600
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      returnDate: '2024-01-18',
      returnTime: '03:00 PM',
      status: 'overdue',
      location: 'Abu Dhabi',
      totalAmount: 800
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      returnDate: '2024-01-20',
      returnTime: '02:00 PM',
      status: 'returned',
      location: 'Sharjah',
      totalAmount: 900
    }
  ]

  const stats = [
    { label: t('todayReturns'), value: '8', color: 'blue', icon: IconCar},
    { label: t('overdueReturns'), value: '2', color: 'red', icon: IconAlertTriangle },
    { label: t('completed'), value: '12', color: 'green', icon: IconCheck },
    { label: t('totalReturns'), value: '45', color: 'gray', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'due-today': return 'blue'
      case 'overdue': return 'red'
      case 'returned': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredReservations = reservations.filter(reservation => {
    const matchesSearch = reservation.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reservation.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || reservation.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('vehicleCheckOut')}</Title>
          <Text c="dimmed" size="sm">{t('manageVehicleReturnProcess')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportReport')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('newCheckOut')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="pending">{t('pendingReturns')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('urgentActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('overdueReturns')}</Text>
                    <Text size="xs">2 {t('vehiclesOverdueForReturn')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('dueToday')}</Text>
                    <Text size="xs">8 {t('vehiclesDueForReturnToday')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Schedule - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('todaysReturns')}</Title>
                
                <Stack gap="sm">
                  {reservations.slice(0, 3).map((reservation) => (
                    <Paper key={reservation.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getStatusColor(reservation.status)} radius="xl" size="sm">
                            {getInitials(reservation.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{reservation.customerName}</Text>
                            <Text size="xs" c="dimmed">{reservation.vehicleName} - {reservation.returnTime}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(reservation.status)} size="sm">
                            {t(reservation.status)}
                          </Badge>
                          <Text size="xs" c="dimmed">{reservation.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="pending" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchReturns')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'due-today', label: t('dueToday') },
                    { value: 'overdue', label: t('overdue') },
                    { value: 'returned', label: t('returned') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('returnTime')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('location')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredReservations.map((reservation) => (
                  <Table.Tr key={reservation.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(reservation.status)} radius="xl" size="sm">
                          {getInitials(reservation.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{reservation.customerName}</Text>
                          <Text size="xs" c="dimmed">{reservation.customerPhone}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{reservation.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{reservation.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{reservation.returnTime}</Text>
                        <Text size="xs" c="dimmed">{reservation.returnDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(reservation.status)}>
                        {t(reservation.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{reservation.location}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPhone size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconMail size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconKey size={12} />}>
                          {t('startCheckOut')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('vehicleCheckOut')}
        size="md"
        withCloseButton
      >
        <Stack>
          <TextInput
            label={t('reservationNumber')}
            placeholder={t('enterReservationNumber')}
            required
          />

          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid' },
              { value: '2', label: 'Sarah Johnson' },
              { value: '3', label: 'Mohammed Hassan' }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('completeCheckOut')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
