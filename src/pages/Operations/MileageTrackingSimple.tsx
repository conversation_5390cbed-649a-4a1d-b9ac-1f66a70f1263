import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconKey,
  IconPlus,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface MileageRecord {
  id: string
  reservationNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  startMileage: number
  endMileage: number
  totalMiles: number
  recordDate: string
  recordType: 'check-in' | 'check-out' | 'maintenance'
  status: 'normal' | 'high' | 'excessive'
  location: string
}

export function MileageTrackingSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const mileageRecords: MileageRecord[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      startMileage: 15000,
      endMileage: 15420,
      totalMiles: 420,
      recordDate: '2024-01-20',
      recordType: 'check-out',
      status: 'normal',
      location: 'Dubai Airport'
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      startMileage: 28000,
      endMileage: 28750,
      totalMiles: 750,
      recordDate: '2024-01-20',
      recordType: 'check-out',
      status: 'high',
      location: 'Abu Dhabi'
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      startMileage: 12000,
      endMileage: 12340,
      totalMiles: 340,
      recordDate: '2024-01-20',
      recordType: 'check-in',
      status: 'normal',
      location: 'Sharjah'
    }
  ]

  const stats = [
    { label: t('totalMilesToday'), value: '2,450', color: 'blue', icon: IconCar},
    { label: t('averageMiles'), value: '285', color: 'green', icon: IconCheck },
    { label: t('highMileageAlerts'), value: '3', color: 'orange', icon: IconAlertTriangle },
    { label: t('totalRecords'), value: '89', color: 'gray', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'green'
      case 'high': return 'orange'
      case 'excessive': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredRecords = mileageRecords.filter(record => {
    const matchesSearch = record.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || record.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('mileageTracking')}
        description={t('trackVehicleMileageAndUsage')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportReport')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('addMileageRecord')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="records">{t('mileageRecords')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('mileageAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('highMileageUsage')}</Text>
                    <Text size="xs">3 {t('vehiclesWithHighMileage')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('maintenanceDue')}</Text>
                    <Text size="xs">2 {t('vehiclesDueForMaintenance')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Records - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentMileageRecords')}</Title>
                
                <Stack gap="sm">
                  {mileageRecords.slice(0, 3).map((record) => (
                    <Paper key={record.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getStatusColor(record.status)} radius="xl" size="sm">
                            {getInitials(record.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{record.customerName}</Text>
                            <Text size="xs" c="dimmed">{record.vehicleName} - {record.totalMiles} miles</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(record.status)} size="sm">
                            {record.totalMiles} {t('miles')}
                          </Badge>
                          <Text size="xs" c="dimmed">{record.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="records" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchMileageRecords')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'normal', label: t('normal') },
                    { value: 'high', label: t('high') },
                    { value: 'excessive', label: t('excessive') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('startMileage')}</Table.Th>
                  <Table.Th>{t('endMileage')}</Table.Th>
                  <Table.Th>{t('totalMiles')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredRecords.map((record) => (
                  <Table.Tr key={record.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(record.status)} radius="xl" size="sm">
                          {getInitials(record.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{record.customerName}</Text>
                          <Text size="xs" c="dimmed">{record.customerPhone}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{record.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{record.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{record.startMileage.toLocaleString()}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{record.endMileage.toLocaleString()}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{record.totalMiles}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(record.status)}>
                        {t(record.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconKey size={12} />}>
                          {t('update')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('addMileageRecord')}
        size="md"
      >
        <Stack>
          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023 - ABC-123' },
              { value: '2', label: 'BMW X5 2022 - XYZ-456' },
              { value: '3', label: 'Mercedes C-Class 2023 - DEF-789' }
            ]}
            required
          />

          <TextInput
            label={t('currentMileage')}
            placeholder={t('enterCurrentMileage')}
            required
          />

          <Select
            label={t('recordType')}
            placeholder={t('selectRecordType')}
            data={[
              { value: 'check-in', label: t('checkIn') },
              { value: 'check-out', label: t('checkOut') },
              { value: 'maintenance', label: t('maintenance') }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('addRecord')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
