import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconKey,
  IconPlus,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface FuelRecord {
  id: string
  reservationNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  fuelLevel: number
  recordDate: string
  recordType: 'check-in' | 'check-out' | 'refuel'
  status: 'normal' | 'low' | 'empty' | 'full'
  location: string
  cost: number
}

export function FuelManagementSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const fuelRecords: FuelRecord[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      fuelLevel: 25,
      recordDate: '2024-01-20',
      recordType: 'check-out',
      status: 'low',
      location: 'Dubai Airport',
      cost: 0
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      fuelLevel: 100,
      recordDate: '2024-01-20',
      recordType: 'refuel',
      status: 'full',
      location: 'Abu Dhabi',
      cost: 180
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      fuelLevel: 75,
      recordDate: '2024-01-20',
      recordType: 'check-in',
      status: 'normal',
      location: 'Sharjah',
      cost: 0
    }
  ]

  const stats = [
    { label: t('lowFuelVehicles'), value: '5', color: 'red', icon: IconAlertTriangle },
    { label: t('fuelCostToday'), value: '$450', color: 'blue', icon: IconCar},
    { label: t('refuelsToday'), value: '8', color: 'green', icon: IconCheck },
    { label: t('totalRecords'), value: '156', color: 'gray', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'green'
      case 'low': return 'orange'
      case 'empty': return 'red'
      case 'full': return 'blue'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredRecords = fuelRecords.filter(record => {
    const matchesSearch = record.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || record.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('fuelManagement')}</Title>
          <Text c="dimmed" size="sm">{t('manageFuelLevelsAndRefueling')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportReport')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('addFuelRecord')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="records">{t('fuelRecords')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('fuelAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('lowFuelAlert')}</Text>
                    <Text size="xs">5 {t('vehiclesWithLowFuel')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('refuelRequired')}</Text>
                    <Text size="xs">3 {t('vehiclesNeedRefueling')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Records - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentFuelRecords')}</Title>
                
                <Stack gap="sm">
                  {fuelRecords.slice(0, 3).map((record) => (
                    <Paper key={record.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getStatusColor(record.status)} radius="xl" size="sm">
                            {getInitials(record.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{record.customerName}</Text>
                            <Text size="xs" c="dimmed">{record.vehicleName} - {record.fuelLevel}%</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(record.status)} size="sm">
                            {record.fuelLevel}% {t('fuel')}
                          </Badge>
                          <Text size="xs" c="dimmed">{record.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="records" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchFuelRecords')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'normal', label: t('normal') },
                    { value: 'low', label: t('low') },
                    { value: 'empty', label: t('empty') },
                    { value: 'full', label: t('full') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('fuelLevel')}</Table.Th>
                  <Table.Th>{t('recordType')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('cost')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredRecords.map((record) => (
                  <Table.Tr key={record.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(record.status)} radius="xl" size="sm">
                          {getInitials(record.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{record.customerName}</Text>
                          <Text size="xs" c="dimmed">{record.customerPhone}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{record.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{record.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{record.fuelLevel}%</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="light">
                        {t(record.recordType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(record.status)}>
                        {t(record.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">${record.cost}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconKey size={12} />}>
                          {t('refuel')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('addFuelRecord')}
        size="md"
      >
        <Stack>
          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023 - ABC-123' },
              { value: '2', label: 'BMW X5 2022 - XYZ-456' },
              { value: '3', label: 'Mercedes C-Class 2023 - DEF-789' }
            ]}
            required
          />

          <Select
            label={t('fuelLevel')}
            placeholder={t('selectFuelLevel')}
            data={[
              { value: '100', label: '100% - Full' },
              { value: '75', label: '75%' },
              { value: '50', label: '50%' },
              { value: '25', label: '25% - Low' },
              { value: '0', label: '0% - Empty' }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('addRecord')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
