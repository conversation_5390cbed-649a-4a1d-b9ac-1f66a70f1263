import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Progress,
  Alert,
  Timeline,
  Stepper,
  Checkbox,
  FileInput
} from '@mantine/core'
import {
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEye,
  IconSearch,
  IconCalendar,
  IconCalendarEvent,
  IconCurrencyDollar,
  IconRefresh,
  IconKey,
  IconClipboardCheck,
  IconUserCheck,
  IconId,
  IconFilter,
  IconMapPin
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface CheckInReservationData {
  id: string
  reservation_id: string
  reservation_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  customer_license_number: string
  customer_license_expiry: string
  customer_license_country: string
  vehicle_id: string
  vehicle_name: string
  vehicle_make: string
  vehicle_model: string
  vehicle_year: number
  plate_number: string
  vin_number: string
  pickup_date: string
  pickup_time: string
  pickup_location: string
  return_date: string
  return_location: string
  status: 'scheduled' | 'arrived' | 'in_progress' | 'inspection' | 'documentation' | 'completed' | 'cancelled'
  checkin_status: 'pending' | 'documents_verified' | 'inspection_completed' | 'keys_handed' | 'completed'
  total_amount: number
  deposit_amount: number
  payment_status: 'pending' | 'partial' | 'completed'
  insurance_verified: boolean
  license_verified: boolean
  additional_drivers: string[]
  special_instructions?: string
  vehicle_condition: {
    exterior_damage: string[]
    interior_condition: string
    fuel_level: number
    mileage: number
    cleanliness_score: number
    accessories: string[]
  }
  documents_required: string[]
  documents_collected: string[]
  inspection_photos: string[]
  customer_signature?: string
  staff_signature?: string
  checkin_notes?: string
  estimated_duration: number
  actual_start_time?: string
  actual_completion_time?: string
  assigned_staff: string
  created_at: string
  updated_at: string
}

export function CheckInComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [checkInReservations, setCheckInReservations] = useState<CheckInReservationData[]>([])
  const [activeTab, setActiveTab] = useState('all-checkins')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [checkInModalOpen, setCheckInModalOpen] = useState(false)
  const [inspectionModalOpen, setInspectionModalOpen] = useState(false)
  const [selectedReservation, setSelectedReservation] = useState<CheckInReservationData | null>(null)
  const [checkInStep, setCheckInStep] = useState(0)

  // Mock data for development - comprehensive check-in structure
  const mockCheckInReservations: CheckInReservationData[] = [
    {
      id: '1',
      reservation_id: '1',
      reservation_number: 'RES-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971501234567',
      customer_license_number: 'DL123456789',
      customer_license_expiry: '2026-03-15',
      customer_license_country: 'UAE',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      vehicle_make: 'Toyota',
      vehicle_model: 'Camry',
      vehicle_year: 2023,
      plate_number: 'ABC-123',
      vin_number: 'JT2BF22K123456789',
      pickup_date: '2024-01-20',
      pickup_time: '09:00',
      pickup_location: 'Dubai Airport Terminal 1',
      return_date: '2024-01-25',
      return_location: 'Dubai Airport Terminal 1',
      status: 'arrived',
      checkin_status: 'pending',
      total_amount: 630,
      deposit_amount: 500,
      payment_status: 'completed',
      insurance_verified: false,
      license_verified: false,
      additional_drivers: [],
      special_instructions: 'Customer prefers white vehicle',
      vehicle_condition: {
        exterior_damage: [],
        interior_condition: 'excellent',
        fuel_level: 100,
        mileage: 15000,
        cleanliness_score: 95,
        accessories: ['GPS', 'Phone Charger', 'First Aid Kit']
      },
      documents_required: ['driving_license', 'passport', 'credit_card', 'insurance'],
      documents_collected: [],
      inspection_photos: [],
      estimated_duration: 30,
      assigned_staff: 'John Smith',
      created_at: '2024-01-20T08:00:00Z',
      updated_at: '2024-01-20T08:30:00Z'
    },
    {
      id: '2',
      reservation_id: '2',
      reservation_number: 'RES-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+**********',
      customer_license_number: 'US987654321',
      customer_license_expiry: '2025-07-22',
      customer_license_country: 'USA',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      vehicle_make: 'BMW',
      vehicle_model: 'X5',
      vehicle_year: 2022,
      plate_number: 'XYZ-456',
      vin_number: 'WBAFR7C50BC123456',
      pickup_date: '2024-01-20',
      pickup_time: '11:30',
      pickup_location: 'Downtown Dubai',
      return_date: '2024-01-24',
      return_location: 'Downtown Dubai',
      status: 'in_progress',
      checkin_status: 'documents_verified',
      total_amount: 1050,
      deposit_amount: 1000,
      payment_status: 'completed',
      insurance_verified: true,
      license_verified: true,
      additional_drivers: ['John Smith'],
      vehicle_condition: {
        exterior_damage: ['minor_scratch_rear_bumper'],
        interior_condition: 'good',
        fuel_level: 100,
        mileage: 28000,
        cleanliness_score: 90,
        accessories: ['GPS', 'Phone Charger', 'First Aid Kit', 'Child Seat']
      },
      documents_required: ['driving_license', 'passport', 'credit_card', 'insurance'],
      documents_collected: ['driving_license', 'passport', 'credit_card'],
      inspection_photos: ['exterior_1.jpg', 'interior_1.jpg'],
      estimated_duration: 45,
      actual_start_time: '2024-01-20T11:30:00Z',
      assigned_staff: 'Maria Garcia',
      created_at: '2024-01-20T10:00:00Z',
      updated_at: '2024-01-20T12:00:00Z'
    },
    {
      id: '3',
      reservation_id: '3',
      reservation_number: 'RES-2024-003',
      customer_id: '3',
      customer_name: 'Mohammed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+971509876543',
      customer_license_number: 'EG456789123',
      customer_license_expiry: '2025-11-10',
      customer_license_country: 'Egypt',
      vehicle_id: '3',
      vehicle_name: 'Mercedes C-Class 2023',
      vehicle_make: 'Mercedes',
      vehicle_model: 'C-Class',
      vehicle_year: 2023,
      plate_number: 'DEF-789',
      vin_number: 'WDD2050461A123456',
      pickup_date: '2024-01-20',
      pickup_time: '14:00',
      pickup_location: 'Abu Dhabi Mall',
      return_date: '2024-01-25',
      return_location: 'Abu Dhabi Mall',
      status: 'scheduled',
      checkin_status: 'pending',
      total_amount: 945,
      deposit_amount: 750,
      payment_status: 'partial',
      insurance_verified: false,
      license_verified: false,
      additional_drivers: [],
      special_instructions: 'First time customer - requires verification',
      vehicle_condition: {
        exterior_damage: [],
        interior_condition: 'excellent',
        fuel_level: 100,
        mileage: 12000,
        cleanliness_score: 98,
        accessories: ['GPS', 'Phone Charger', 'First Aid Kit', 'Umbrella']
      },
      documents_required: ['driving_license', 'passport', 'credit_card', 'insurance'],
      documents_collected: [],
      inspection_photos: [],
      estimated_duration: 35,
      assigned_staff: 'Ahmed Ali',
      created_at: '2024-01-20T12:00:00Z',
      updated_at: '2024-01-20T12:00:00Z'
    }
  ]

  useEffect(() => {
    loadCheckInReservations()
  }, [])

  const loadCheckInReservations = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await checkInService.getAllCheckInReservations()
      setCheckInReservations(mockCheckInReservations)
    } catch (error) {
      console.error('Error loading check-in reservations:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredReservations = checkInReservations.filter(reservation => {
    const matchesSearch = !searchQuery || 
      reservation.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reservation.reservation_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reservation.plate_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reservation.vehicle_name.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || reservation.status === statusFilter
    const matchesLocation = !locationFilter || 
      reservation.pickup_location.toLowerCase().includes(locationFilter.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesLocation
  })

  // Pagination
  const totalItems = filteredReservations.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedReservations = filteredReservations.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const todayPickups = checkInReservations.filter(r => 
    new Date(r.pickup_date).toDateString() === new Date().toDateString()
  ).length
  const arrivedCustomers = checkInReservations.filter(r => r.status === 'arrived').length
  const inProgressCheckIns = checkInReservations.filter(r => r.status === 'in_progress').length
  const completedToday = checkInReservations.filter(r => 
    r.status === 'completed' && 
    new Date(r.actual_completion_time || '').toDateString() === new Date().toDateString()
  ).length

  const stats = [
    {
      label: 'Today Pickups',
      value: todayPickups.toString(),
      color: 'blue',
      icon: IconCalendarEvent
    },
    {
      label: 'Customers Arrived',
      value: arrivedCustomers.toString(),
      color: 'orange',
      icon: IconUserCheck
    },
    {
      label: 'In Progress',
      value: inProgressCheckIns.toString(),
      color: 'yellow',
      icon: IconClock
    },
    {
      label: 'Completed Today',
      value: completedToday.toString(),
      color: 'green',
      icon: IconCheck
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'blue'
      case 'arrived': return 'orange'
      case 'in_progress': return 'yellow'
      case 'inspection': return 'cyan'
      case 'documentation': return 'purple'
      case 'completed': return 'green'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getCheckInStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'gray'
      case 'documents_verified': return 'blue'
      case 'inspection_completed': return 'cyan'
      case 'keys_handed': return 'yellow'
      case 'completed': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getCheckInProgress = (status: string) => {
    switch (status) {
      case 'pending': return 0
      case 'documents_verified': return 25
      case 'inspection_completed': return 50
      case 'keys_handed': return 75
      case 'completed': return 100
      default: return 0
    }
  }

  const handleCompleteCheckIn = async () => {
    if (!selectedReservation) return

    try {
      setLoading(true)

      // Update the reservation status to completed
      const updatedReservations = checkInReservations.map(reservation => {
        if (reservation.id === selectedReservation.id) {
          return {
            ...reservation,
            status: 'completed' as const,
            checkin_status: 'completed' as const,
            actual_completion_time: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        }
        return reservation
      })

      setCheckInReservations(updatedReservations)

      // Close the modal and reset state
      setCheckInModalOpen(false)
      setSelectedReservation(null)
      setCheckInStep(0)

      // TODO: In a real app, this would:
      // 1. Update the database
      // 2. Change vehicle status to 'available'
      // 3. Process final billing/refunds
      // 4. Send confirmation email to customer
      // 5. Generate completion report

      console.log('Check-in completed successfully for:', selectedReservation.reservation_number)

    } catch (error) {
      console.error('Error completing check-in:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>Check In Management</Title>
              <Text c="dimmed" size="sm">Manage vehicle pickup and check-in process</Text>
            </div>
            <Group>
              <Button
                variant="light"
                leftSection={<IconRefresh size={16} />}
                onClick={loadCheckInReservations}
              >
                Refresh
              </Button>
              <Button
                variant="light"
                leftSection={<IconDownload size={16} />}
                onClick={() => {
                  // TODO: Implement export functionality
                  console.log('Exporting check-in data...')
                }}
              >
                Export
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-checkins">All Check Ins</Tabs.Tab>
            <Tabs.Tab value="today">Today Schedule</Tabs.Tab>
            <Tabs.Tab value="in-progress">In Progress</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-checkins">
            {/* Statistics Cards */}
            <Grid mb="lg" mt="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <TextInput
                    placeholder="Search check ins"
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder="Status"
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: 'All Statuses' },
                      { value: 'scheduled', label: 'Scheduled' },
                      { value: 'arrived', label: 'Arrived' },
                      { value: 'in_progress', label: 'In Progress' },
                      { value: 'completed', label: 'Completed' }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder="Location"
                    leftSection={<IconMapPin size={16} />}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setLocationFilter('')
                    }}
                  >
                    Clear Filters
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Check-In Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Customer</Table.Th>
                    <Table.Th>Vehicle</Table.Th>
                    <Table.Th>Pickup Time</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Progress</Table.Th>
                    <Table.Th>Staff</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedReservations.map((reservation) => (
                    <Table.Tr key={reservation.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color={getStatusColor(reservation.status)}>
                            {getInitials(reservation.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{reservation.customer_name}</Text>
                            <Text size="xs" c="dimmed">{reservation.reservation_number}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{reservation.vehicle_name}</Text>
                          <Text size="xs" c="dimmed">{reservation.plate_number}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">{reservation.pickup_time}</Text>
                          <Text size="xs" c="dimmed">{new Date(reservation.pickup_date).toLocaleDateString()}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(reservation.status)} size="sm">
                          {reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1).replace('_', ' ')}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Group gap="xs" mb={4}>
                            <Text size="xs" c="dimmed">{t(reservation.checkin_status)}</Text>
                            <Text size="xs" fw={500}>
                              {getCheckInProgress(reservation.checkin_status)}%
                            </Text>
                          </Group>
                          <Progress
                            value={getCheckInProgress(reservation.checkin_status)}
                            size="sm"
                            color={getCheckInStatusColor(reservation.checkin_status)}
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{reservation.assigned_staff}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs" align="center" justify="flex-start">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedReservation(reservation)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedReservation(reservation)
                              setCheckInModalOpen(true)
                            }}
                          >
                            <IconKey size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="orange"
                            onClick={() => {
                              setSelectedReservation(reservation)
                              setInspectionModalOpen(true)
                            }}
                          >
                            <IconClipboardCheck size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  Showing {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} of {totalItems} check ins
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="today">
            <Card withBorder mt="lg">
              <Title order={4} mb="md">Today Check In Schedule</Title>
              <Timeline active={1} bulletSize={24} lineWidth={2}>
                {checkInReservations
                  .filter(r => new Date(r.pickup_date).toDateString() === new Date().toDateString())
                  .map((reservation, index) => (
                    <Timeline.Item
                      key={reservation.id}
                      bullet={<IconCar size={12} />}
                      title={`${reservation.pickup_time} - ${reservation.customer_name}`}
                    >
                      <Text c="dimmed" size="sm">
                        {reservation.vehicle_name} ({reservation.plate_number})
                      </Text>
                      <Text size="xs" mt={4}>
                        {reservation.pickup_location}
                      </Text>
                      <Badge size="xs" color={getStatusColor(reservation.status)} mt={4}>
                        {t(reservation.status)}
                      </Badge>
                    </Timeline.Item>
                  ))}
              </Timeline>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="in-progress">
            <Grid>
              {checkInReservations
                .filter(r => r.status === 'in_progress')
                .map((reservation) => (
                  <Grid.Col key={reservation.id} span={{ base: 12, md: 6 }}>
                    <Card withBorder>
                      <Group justify="space-between" mb="md">
                        <Group>
                          <Avatar color={getStatusColor(reservation.status)}>
                            {getInitials(reservation.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500}>{reservation.customer_name}</Text>
                            <Text size="sm" c="dimmed">{reservation.vehicle_name}</Text>
                          </div>
                        </Group>
                        <Badge color={getStatusColor(reservation.status)}>
                          {t(reservation.status)}
                        </Badge>
                      </Group>

                      <Stepper active={getCheckInProgress(reservation.checkin_status) / 25} size="sm">
                        <Stepper.Step
                          label={t('documents')}
                          description={t('verifyDocuments')}
                          icon={<IconId size={16} />}
                        />
                        <Stepper.Step
                          label={t('inspection')}
                          description={t('vehicleInspection')}
                          icon={<IconClipboardCheck size={16} />}
                        />
                        <Stepper.Step
                          label={t('keys')}
                          description={t('handOverKeys')}
                          icon={<IconKey size={16} />}
                        />
                        <Stepper.Step
                          label={t('complete')}
                          description={t('checkInComplete')}
                          icon={<IconCheck size={16} />}
                        />
                      </Stepper>

                      <Group justify="flex-end" mt="md">
                        <Button
                          size="xs"
                          variant="light"
                          onClick={() => {
                            setSelectedReservation(reservation)
                            setCheckInModalOpen(true)
                          }}
                        >
                          Continue Check In
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* View Check-In Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedReservation ? selectedReservation.reservation_number : 'Check In Details'}
          size="xl"
        >
          {selectedReservation && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('customer')}</Text>
                      <Text fw={500}>{selectedReservation.customer_name}</Text>
                      <Text size="xs" c="dimmed">{selectedReservation.customer_email}</Text>
                      <Text size="xs" c="dimmed">{selectedReservation.customer_phone}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('vehicle')}</Text>
                      <Text fw={500}>{selectedReservation.vehicle_name}</Text>
                      <Text size="xs" c="dimmed">{selectedReservation.plate_number}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('pickupDateTime')}</Text>
                      <Text fw={500}>
                        {new Date(selectedReservation.pickup_date).toLocaleDateString()} {selectedReservation.pickup_time}
                      </Text>
                      <Text size="xs" c="dimmed">{selectedReservation.pickup_location}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('status')}</Text>
                      <Badge color={getStatusColor(selectedReservation.status)}>
                        {t(selectedReservation.status)}
                      </Badge>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <Avatar size="xl" color={getStatusColor(selectedReservation.status)}>
                        {getInitials(selectedReservation.customer_name)}
                      </Avatar>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="lg">{selectedReservation.customer_name}</Text>
                        <Text size="sm" c="dimmed">{selectedReservation.reservation_number}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <div>
                <Text size="sm" c="dimmed" mb="xs">{t('checkInProgress')}</Text>
                <Progress
                  value={getCheckInProgress(selectedReservation.checkin_status)}
                  size="lg"
                  color={getCheckInStatusColor(selectedReservation.checkin_status)}
                />
                <Text size="xs" c="dimmed" ta="center">
                  {getCheckInProgress(selectedReservation.checkin_status)}%
                </Text>
                <Text size="xs" c="dimmed" mt="xs">
                  {t('currentStep')}: {t(selectedReservation.checkin_status)}
                </Text>
              </div>

              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('documentsStatus')}</Text>
                  <Group gap="xs">
                    <IconId size={16} color={selectedReservation.license_verified ? 'green' : 'orange'} />
                    <Text size="sm">{selectedReservation.license_verified ? t('verified') : t('pending')}</Text>
                  </Group>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('paymentStatus')}</Text>
                  <Badge color={selectedReservation.payment_status === 'completed' ? 'green' : 'orange'}>
                    {t(selectedReservation.payment_status)}
                  </Badge>
                </Grid.Col>
              </Grid>

              {selectedReservation.special_instructions && (
                <>
                  <Divider />
                  <div>
                    <Text size="sm" c="dimmed">{t('specialInstructions')}</Text>
                    <Text>{selectedReservation.special_instructions}</Text>
                  </div>
                </>
              )}
            </Stack>
          )}
        </Modal>

        {/* Check-In Process Modal */}
        <Modal
          opened={checkInModalOpen}
          onClose={() => setCheckInModalOpen(false)}
          title="Vehicle Return Process"
          size="xl"
          padding="lg"
        >
          {selectedReservation && (
            <Stack gap="lg">
              {/* Customer & Vehicle Info Header */}
              <Card withBorder p="md" bg="var(--mantine-color-gray-0)">
                <Group justify="space-between" align="center">
                  <div>
                    <Text fw={600} size="lg" c="dark">
                      {selectedReservation.customer_name}
                    </Text>
                    <Text size="sm" c="dimmed">
                      {selectedReservation.vehicle_name} • {selectedReservation.plate_number}
                    </Text>
                    <Text size="xs" c="dimmed">
                      Reservation: {selectedReservation.reservation_number}
                    </Text>
                  </div>
                  <Badge color={getStatusColor(selectedReservation.status)} size="lg">
                    {selectedReservation.status.charAt(0).toUpperCase() + selectedReservation.status.slice(1).replace('_', ' ')}
                  </Badge>
                </Group>
              </Card>

              <Stepper active={checkInStep} onStepClick={setCheckInStep} allowNextStepsSelect={false} size="sm">
                <Stepper.Step
                  label="Vehicle Return"
                  description="Customer returns the vehicle"
                  icon={<IconCar size={16} />}
                >
                  <Stack gap="md" mt="md">
                    <Alert color="blue" icon={<IconCar size={16} />} variant="light">
                      <Text fw={500}>Customer has returned the vehicle</Text>
                      <Text size="sm">Verify customer identity and begin return inspection</Text>
                    </Alert>

                    <Card withBorder p="md">
                      <Text fw={500} mb="sm">Return Verification</Text>
                      <Grid>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="Customer ID verified"
                            size="sm"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="Return on time"
                            size="sm"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="All keys received"
                            size="sm"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="Accessories returned"
                            size="sm"
                          />
                        </Grid.Col>
                      </Grid>
                    </Card>

                    <Textarea
                      label="Return Notes"
                      placeholder="Any issues or observations during return..."
                      rows={3}
                      variant="filled"
                    />
                  </Stack>
                </Stepper.Step>

                <Stepper.Step
                  label="Damage Inspection"
                  description="Inspect vehicle for damage and condition"
                  icon={<IconClipboardCheck size={16} />}
                >
                  <Stack gap="md" mt="md">
                    <Card withBorder p="md">
                      <Text fw={500} mb="sm">Vehicle Readings</Text>
                      <Grid>
                        <Grid.Col span={6}>
                          <NumberInput
                            label="Current Fuel Level (%)"
                            placeholder="Enter fuel percentage"
                            min={0}
                            max={100}
                            suffix="%"
                            defaultValue={selectedReservation.vehicle_condition.fuel_level}
                            variant="filled"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <NumberInput
                            label="Current Mileage"
                            placeholder="Enter odometer reading"
                            min={0}
                            defaultValue={selectedReservation.vehicle_condition.mileage}
                            variant="filled"
                          />
                        </Grid.Col>
                      </Grid>
                    </Card>

                    <Card withBorder p="md">
                      <Text fw={500} mb="sm">Condition Assessment</Text>
                      <Stack gap="sm">
                        <Textarea
                          label="Exterior Condition"
                          placeholder="Document any new scratches, dents, or damage not present at pickup..."
                          rows={2}
                          variant="filled"
                        />
                        <Textarea
                          label="Interior Condition"
                          placeholder="Check cleanliness, missing items, stains, etc..."
                          rows={2}
                          variant="filled"
                        />
                        <FileInput
                          label="Damage Photos"
                          placeholder="Upload photos of any damage or issues"
                          multiple
                          accept="image/*"
                          variant="filled"
                        />
                      </Stack>
                    </Card>
                  </Stack>
                </Stepper.Step>

                <Stepper.Step
                  label="Calculate Charges"
                  description="Calculate additional fees and charges"
                  icon={<IconCurrencyDollar size={16} />}
                >
                  <Stack gap="md" mt="md">
                    <Card withBorder p="md">
                      <Text fw={500} mb="sm">Additional Charges</Text>
                      <Grid>
                        <Grid.Col span={6}>
                          <NumberInput
                            label="Fuel Charge"
                            placeholder="0.00"
                            min={0}
                            prefix="$"
                            decimalScale={2}
                            variant="filled"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <NumberInput
                            label="Damage Charge"
                            placeholder="0.00"
                            min={0}
                            prefix="$"
                            decimalScale={2}
                            variant="filled"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <NumberInput
                            label="Late Return Fee"
                            placeholder="0.00"
                            min={0}
                            prefix="$"
                            decimalScale={2}
                            variant="filled"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <NumberInput
                            label="Cleaning Fee"
                            placeholder="0.00"
                            min={0}
                            prefix="$"
                            decimalScale={2}
                            variant="filled"
                          />
                        </Grid.Col>
                      </Grid>
                    </Card>

                    <Alert color="green" icon={<IconCurrencyDollar size={16} />} variant="light">
                      <Text fw={500}>Security Deposit Refund: ${selectedReservation.deposit_amount}</Text>
                      <Text size="sm">Total additional charges will be deducted from deposit</Text>
                    </Alert>
                  </Stack>
                </Stepper.Step>

                <Stepper.Step
                  label="Complete Return"
                  description="Finalize vehicle return process"
                  icon={<IconCheck size={16} />}
                >
                  <Stack gap="md" mt="md">
                    <Alert color="green" icon={<IconCheck size={16} />} variant="light">
                      <Text fw={500}>Ready to complete vehicle return</Text>
                      <Text size="sm">Review all details and process final payment</Text>
                    </Alert>

                    <Card withBorder p="md">
                      <Text fw={500} mb="sm">Final Verification</Text>
                      <Grid>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="Customer signature obtained"
                            size="sm"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="Final payment processed"
                            size="sm"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="Vehicle inspection complete"
                            size="sm"
                          />
                        </Grid.Col>
                        <Grid.Col span={6}>
                          <Checkbox
                            label="Keys and accessories collected"
                            size="sm"
                          />
                        </Grid.Col>
                      </Grid>
                    </Card>

                    <Textarea
                      label="Final Notes"
                      placeholder="Any final observations or customer feedback..."
                      rows={3}
                      variant="filled"
                    />

                    <Alert color="blue" icon={<IconCurrencyDollar size={16} />} variant="light">
                      <Text fw={500} mb="xs">Payment Summary</Text>
                      <Group justify="space-between">
                        <Text size="sm">Security Deposit:</Text>
                        <Text size="sm" fw={500}>${selectedReservation.deposit_amount}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm">Additional Charges:</Text>
                        <Text size="sm" fw={500}>$0.00</Text>
                      </Group>
                      <Divider my="xs" />
                      <Group justify="space-between">
                        <Text fw={600}>Refund Amount:</Text>
                        <Text fw={700} size="lg" c="green">${selectedReservation.deposit_amount}</Text>
                      </Group>
                    </Alert>
                  </Stack>
                </Stepper.Step>
              </Stepper>

              <Divider mt="xl" />
              <Group justify="space-between" mt="lg">
                <Button
                  variant="light"
                  color="gray"
                  onClick={() => setCheckInModalOpen(false)}
                >
                  Cancel
                </Button>
                <Group gap="sm">
                  {checkInStep > 0 && (
                    <Button
                      variant="light"
                      onClick={() => setCheckInStep(checkInStep - 1)}
                    >
                      Previous
                    </Button>
                  )}
                  {checkInStep < 3 ? (
                    <Button
                      onClick={() => setCheckInStep(checkInStep + 1)}
                      rightSection={<IconCar size={16} />}
                    >
                      Next Step
                    </Button>
                  ) : (
                    <Button
                      color="green"
                      leftSection={<IconCheck size={16} />}
                      onClick={handleCompleteCheckIn}
                      size="md"
                    >
                      Complete Return
                    </Button>
                  )}
                </Group>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Vehicle Inspection Modal */}
        <Modal
          opened={inspectionModalOpen}
          onClose={() => setInspectionModalOpen(false)}
          title="Vehicle Inspection Report"
          size="xl"
        >
          {selectedReservation && (
            <Stack>
              <Group justify="space-between">
                <div>
                  <Text fw={500} size="lg">{selectedReservation.customer_name}</Text>
                  <Text size="sm" c="dimmed">{selectedReservation.vehicle_name} - {selectedReservation.plate_number}</Text>
                </div>
                <Badge color={getStatusColor(selectedReservation.status)}>
                  {t(selectedReservation.status)}
                </Badge>
              </Group>

              <Divider />

              <Grid>
                <Grid.Col span={6}>
                  <NumberInput
                    label="Current Fuel Level (%)"
                    placeholder="Enter fuel percentage"
                    min={0}
                    max={100}
                    suffix="%"
                    defaultValue={selectedReservation.vehicle_condition.fuel_level}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <NumberInput
                    label="Current Mileage"
                    placeholder="Enter odometer reading"
                    min={0}
                    defaultValue={selectedReservation.vehicle_condition.mileage}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Exterior Condition"
                    placeholder="Document any scratches, dents, or exterior damage..."
                    rows={3}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label="Interior Condition"
                    placeholder="Check cleanliness, missing items, interior damage..."
                    rows={3}
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <FileInput
                    label="Inspection Photos"
                    placeholder="Upload photos of vehicle condition"
                    multiple
                    accept="image/*"
                  />
                </Grid.Col>
              </Grid>

              <Group justify="flex-end" mt="md">
                <Button variant="light" onClick={() => setInspectionModalOpen(false)}>
                  Cancel
                </Button>
                <Button
                  color="blue"
                  onClick={() => {
                    // TODO: Save inspection data
                    console.log('Inspection saved for:', selectedReservation.reservation_number)
                    setInspectionModalOpen(false)
                  }}
                >
                  Save Inspection
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
