import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,Alert,
  Tabs,
  Avatar,
  Progress
} from '@mantine/core'
import {IconSearch,IconEye,
  IconPhone,
  IconMail,
  IconCar,
  IconMapPin,
  IconClock,
  IconAlertTriangle,
  IconDownload,
  IconRefresh,
  IconGasStation,
  IconRoad} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface ActiveRental {
  id: string
  reservationNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  pickupDate: string
  returnDate: string
  daysRemaining: number
  status: 'active' | 'overdue' | 'due-today' | 'due-tomorrow'
  location: string
  mileage: number
  fuelLevel: number
  totalAmount: number
  paidAmount: number
}

export function ActiveRentalsBasic() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')

  // Simple mock data
  const activeRentals: ActiveRental[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      pickupDate: '2024-01-15',
      returnDate: '2024-01-20',
      daysRemaining: 0,
      status: 'due-today',
      location: 'Dubai Airport',
      mileage: 15420,
      fuelLevel: 75,
      totalAmount: 600,
      paidAmount: 600
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      pickupDate: '2024-01-10',
      returnDate: '2024-01-18',
      daysRemaining: -2,
      status: 'overdue',
      location: 'Abu Dhabi',
      mileage: 28750,
      fuelLevel: 45,
      totalAmount: 800,
      paidAmount: 800
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      pickupDate: '2024-01-16',
      returnDate: '2024-01-21',
      daysRemaining: 1,
      status: 'due-tomorrow',
      location: 'Sharjah',
      mileage: 12340,
      fuelLevel: 90,
      totalAmount: 900,
      paidAmount: 450
    },
    {
      id: '4',
      reservationNumber: 'RES-2024-004',
      customerName: 'Emma Wilson',
      customerPhone: '+44-20-1234-5678',
      vehicleName: 'Honda Civic 2023',
      plateNumber: 'GHI-012',
      pickupDate: '2024-01-17',
      returnDate: '2024-01-24',
      daysRemaining: 4,
      status: 'active',
      location: 'Dubai Marina',
      mileage: 8950,
      fuelLevel: 60,
      totalAmount: 420,
      paidAmount: 420
    }
  ]

  const stats = [
    { label: t('totalActiveRentals'), value: '23', color: 'blue', icon: IconCar },
    { label: t('dueToday'), value: '5', color: 'orange', icon: IconClock },
    { label: t('overdue'), value: '2', color: 'red', icon: IconAlertTriangle },
    { label: t('totalRevenue'), value: '$12,450', color: 'green', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'due-today': return 'orange'
      case 'due-tomorrow': return 'yellow'
      case 'overdue': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredRentals = activeRentals.filter(rental => {
    const matchesSearch = rental.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         rental.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         rental.plateNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || rental.status === statusFilter
    const matchesLocation = !locationFilter || rental.location.toLowerCase().includes(locationFilter.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesLocation
  })

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('activeRentals')}</Title>
          <Text c="dimmed" size="sm">{t('monitorAllCurrentlyRentedVehicles')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconRefresh size={16} />}>
            {t('refresh')}
          </Button>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportReport')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="rentals">{t('allActiveRentals')}</Tabs.Tab>
          <Tabs.Tab value="map">{t('mapView')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Urgent Actions */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('urgentActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('overdueReturns')}</Text>
                    <Text size="xs">2 {t('vehiclesOverdueForReturn')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('dueToday')}</Text>
                    <Text size="xs">5 {t('vehiclesDueForReturnToday')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconGasStation size={16} />} color="yellow">
                    <Text fw={500} size="sm">{t('lowFuel')}</Text>
                    <Text size="xs">3 {t('vehiclesWithLowFuelLevels')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Fleet Status */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('fleetStatus')}</Title>
                
                <Stack gap="sm">
                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconCar size={16} color="var(--mantine-color-green-6)" />
                      <Text size="sm">{t('activeRentals')}</Text>
                    </Group>
                    <Text fw={700}>23</Text>
                  </Group>
                  <Progress value={76.7} color="green" size="sm" />

                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconCar size={16} color="var(--mantine-color-blue-6)" />
                      <Text size="sm">{t('availableVehicles')}</Text>
                    </Group>
                    <Text fw={700}>7</Text>
                  </Group>
                  <Progress value={23.3} color="blue" size="sm" />

                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconCar size={16} color="var(--mantine-color-gray-6)" />
                      <Text size="sm">{t('underMaintenance')}</Text>
                    </Group>
                    <Text fw={700}>0</Text>
                  </Group>
                  <Progress value={0} color="gray" size="sm" />
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Due Today */}
          <Card withBorder mt="lg">
            <Title order={4} mb="md">{t('dueForReturnToday')}</Title>
            
            <Stack gap="sm">
              {activeRentals.filter(r => r.status === 'due-today' || r.status === 'overdue').map((rental) => (
                <Paper key={rental.id} p="sm" withBorder>
                  <Group justify="space-between">
                    <Group>
                      <Avatar color={getStatusColor(rental.status)} radius="xl">
                        {getInitials(rental.customerName)}
                      </Avatar>
                      <div>
                        <Text fw={500} size="sm">{rental.customerName}</Text>
                        <Text size="xs" c="dimmed">{rental.vehicleName} - {rental.plateNumber}</Text>
                      </div>
                    </Group>
                    <div style={{ textAlign: 'right' }}>
                      <Badge color={getStatusColor(rental.status)} size="sm">
                        {rental.status === 'overdue' ? `${Math.abs(rental.daysRemaining)} days overdue` : 'Due today'}
                      </Badge>
                      <Text size="xs" c="dimmed">{rental.location}</Text>
                    </div>
                  </Group>
                </Paper>
              ))}
            </Stack>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="rentals" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <TextInput
                  placeholder={t('searchRentals')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'active', label: t('active') },
                    { value: 'due-today', label: t('dueToday') },
                    { value: 'due-tomorrow', label: t('dueTomorrow') },
                    { value: 'overdue', label: t('overdue') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <TextInput
                  placeholder={t('filterByLocation')}
                  leftSection={<IconMapPin size={16} />}
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Active Rentals Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('dates')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('location')}</Table.Th>
                  <Table.Th>{t('fuel')}</Table.Th>
                  <Table.Th>{t('mileage')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredRentals.map((rental) => (
                  <Table.Tr key={rental.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(rental.status)} radius="xl" size="sm">
                          {getInitials(rental.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{rental.customerName}</Text>
                          <Text size="xs" c="dimmed">{rental.customerPhone}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{rental.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{rental.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{rental.pickupDate} - {rental.returnDate}</Text>
                        <Text size="xs" c="dimmed">
                          {rental.daysRemaining > 0 ? `${rental.daysRemaining} days left` : 
                           rental.daysRemaining === 0 ? 'Due today' : 
                           `${Math.abs(rental.daysRemaining)} days overdue`}
                        </Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(rental.status)}>
                        {t(rental.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{rental.location}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <IconGasStation size={14} />
                        <Text size="sm">{rental.fuelLevel}%</Text>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <IconRoad size={14} />
                        <Text size="sm">{rental.mileage.toLocaleString()}</Text>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPhone size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconMail size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconMapPin size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="map" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('vehicleLocationMap')}</Title>
            <Text c="dimmed">{t('mapViewWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>
      </Tabs>
    </Container>
  )
}
