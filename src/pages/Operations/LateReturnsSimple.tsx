import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEye,
  IconKey,
  IconMail,
  IconPhone,
  IconPlus,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface LateReturn {
  id: string
  reservationNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  returnDate: string
  daysOverdue: number
  lateFee: number
  status: 'overdue' | 'contacted' | 'resolved' | 'escalated'
  location: string
  totalAmount: number
}

export function LateReturnsSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const lateReturns: LateReturn[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      returnDate: '2024-01-18',
      daysOverdue: 2,
      lateFee: 100,
      status: 'overdue',
      location: 'Dubai Airport',
      totalAmount: 600
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      returnDate: '2024-01-15',
      daysOverdue: 5,
      lateFee: 250,
      status: 'contacted',
      location: 'Abu Dhabi',
      totalAmount: 800
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      returnDate: '2024-01-10',
      daysOverdue: 10,
      lateFee: 500,
      status: 'escalated',
      location: 'Sharjah',
      totalAmount: 900
    }
  ]

  const stats = [
    { label: t('totalLateReturns'), value: '12', color: 'red', icon: IconAlertTriangle },
    { label: t('totalLateFees'), value: '$1,250', color: 'orange', icon: IconCar},
    { label: t('resolvedToday'), value: '3', color: 'green', icon: IconCheck },
    { label: t('escalatedCases'), value: '2', color: 'red', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'overdue': return 'red'
      case 'contacted': return 'orange'
      case 'resolved': return 'green'
      case 'escalated': return 'dark'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredReturns = lateReturns.filter(lateReturn => {
    const matchesSearch = lateReturn.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         lateReturn.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || lateReturn.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('lateReturns')}
        description={t('manageOverdueVehicleReturns')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportReport')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
              {t('addLateReturn')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="late-returns">{t('lateReturns')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('urgentActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('criticalOverdue')}</Text>
                    <Text size="xs">2 {t('vehiclesOverdue7Days')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('requiresContact')}</Text>
                    <Text size="xs">5 {t('customersNeedContacting')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Late Returns - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentLateReturns')}</Title>
                
                <Stack gap="sm">
                  {lateReturns.slice(0, 3).map((lateReturn) => (
                    <Paper key={lateReturn.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getStatusColor(lateReturn.status)} radius="xl" size="sm">
                            {getInitials(lateReturn.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{lateReturn.customerName}</Text>
                            <Text size="xs" c="dimmed">{lateReturn.vehicleName} - {lateReturn.daysOverdue} days late</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(lateReturn.status)} size="sm">
                            ${lateReturn.lateFee} {t('fee')}
                          </Badge>
                          <Text size="xs" c="dimmed">{lateReturn.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="late-returns" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchLateReturns')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'overdue', label: t('overdue') },
                    { value: 'contacted', label: t('contacted') },
                    { value: 'resolved', label: t('resolved') },
                    { value: 'escalated', label: t('escalated') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('daysOverdue')}</Table.Th>
                  <Table.Th>{t('lateFee')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('location')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredReturns.map((lateReturn) => (
                  <Table.Tr key={lateReturn.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(lateReturn.status)} radius="xl" size="sm">
                          {getInitials(lateReturn.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{lateReturn.customerName}</Text>
                          <Text size="xs" c="dimmed">{lateReturn.customerPhone}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{lateReturn.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{lateReturn.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm" c="red">{lateReturn.daysOverdue} days</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">${lateReturn.lateFee}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(lateReturn.status)}>
                        {t(lateReturn.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{lateReturn.location}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPhone size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconMail size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconKey size={12} />}>
                          {t('contact')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('addLateReturn')}
        size="md"
      >
        <Stack>
          <Select
            label={t('reservation')}
            placeholder={t('selectReservation')}
            data={[
              { value: '1', label: 'RES-2024-001 - Ahmed Al-Rashid' },
              { value: '2', label: 'RES-2024-002 - Sarah Johnson' },
              { value: '3', label: 'RES-2024-003 - Mohammed Hassan' }
            ]}
            required
          />

          <TextInput
            label={t('daysOverdue')}
            placeholder={t('enterDaysOverdue')}
            required
          />

          <TextInput
            label={t('lateFee')}
            placeholder={t('enterLateFee')}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setModalOpen(false)}>
              {t('addRecord')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
