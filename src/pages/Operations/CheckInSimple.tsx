import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEye,
  IconKey,
  IconPrinter,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface SimpleCheckIn {
  id: string
  reservationNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  pickupDate: string
  pickupTime: string
  status: 'scheduled' | 'arrived' | 'ready'
  location: string
  totalAmount: number
}

export function CheckInSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [checkInModalOpen, setCheckInModalOpen] = useState(false)
  const [selectedReservation, setSelectedReservation] = useState<SimpleCheckIn | null>(null)

  // Simple mock data
  const pendingCheckIns: SimpleCheckIn[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-005',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      pickupDate: '2024-01-20',
      pickupTime: '09:00 AM',
      status: 'arrived',
      location: 'Dubai Airport',
      totalAmount: 600
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-006',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      pickupDate: '2024-01-20',
      pickupTime: '11:30 AM',
      status: 'scheduled',
      location: 'Abu Dhabi',
      totalAmount: 800
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-007',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      pickupDate: '2024-01-20',
      pickupTime: '02:00 PM',
      status: 'ready',
      location: 'Sharjah',
      totalAmount: 900
    }
  ]

  const stats = [
    { label: t('todayPickups'), value: '12', color: 'blue', icon: IconCar},
    { label: t('customersArrived'), value: '3', color: 'green', icon: IconCar},
    { label: t('readyForPickup'), value: '2', color: 'orange', icon: IconKey },
    { label: t('completed'), value: '7', color: 'green', icon: IconCheck }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'blue'
      case 'arrived': return 'orange'
      case 'ready': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredCheckIns = pendingCheckIns.filter(checkIn => {
    const matchesSearch = checkIn.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         checkIn.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || checkIn.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const handleStartCheckIn = (reservation: SimpleCheckIn) => {
    setSelectedReservation(reservation)
    setCheckInModalOpen(true)
  }

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('vehicleCheckIn')}</Title>
          <Text c="dimmed" size="sm">{t('manageVehiclePickupProcess')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconPrinter size={16} />}>
            {t('printSchedule')}
          </Button>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportReport')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="pending">{t('pendingCheckIns')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Priority Actions */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('priorityActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('customersWaiting')}</Text>
                    <Text size="xs">3 {t('customersArrivedAndWaiting')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('upcomingPickups')}</Text>
                    <Text size="xs">5 {t('pickupsScheduledNext2Hours')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Today's Schedule */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('todaysSchedule')}</Title>
                
                <Stack gap="sm">
                  {pendingCheckIns.slice(0, 3).map((checkIn) => (
                    <Paper key={checkIn.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getStatusColor(checkIn.status)} radius="xl" size="sm">
                            {getInitials(checkIn.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{checkIn.customerName}</Text>
                            <Text size="xs" c="dimmed">{checkIn.vehicleName} - {checkIn.pickupTime}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(checkIn.status)} size="sm">
                            {t(checkIn.status)}
                          </Badge>
                          <Text size="xs" c="dimmed">{checkIn.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="pending" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchCheckIns')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'scheduled', label: t('scheduled') },
                    { value: 'arrived', label: t('arrived') },
                    { value: 'ready', label: t('ready') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Pending Check-Ins Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('pickupTime')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('location')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredCheckIns.map((checkIn) => (
                  <Table.Tr key={checkIn.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(checkIn.status)} radius="xl" size="sm">
                          {getInitials(checkIn.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{checkIn.customerName}</Text>
                          <Text size="xs" c="dimmed">{checkIn.customerPhone}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{checkIn.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{checkIn.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{checkIn.pickupTime}</Text>
                        <Text size="xs" c="dimmed">{checkIn.pickupDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(checkIn.status)}>
                        {t(checkIn.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{checkIn.location}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <Button 
                          size="xs" 
                          leftSection={<IconKey size={12} />}
                          onClick={() => handleStartCheckIn(checkIn)}
                          disabled={checkIn.status === 'ready'}
                        >
                          {checkIn.status === 'ready' ? t('completed') : t('startCheckIn')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Check-In Modal */}
      <Modal
        opened={checkInModalOpen}
        onClose={() => setCheckInModalOpen(false)}
        title={t('vehicleCheckInProcess')}
        size="md"
      >
        {selectedReservation && (
          <Stack>
            <Group>
              <Avatar color="blue" radius="xl">
                {getInitials(selectedReservation.customerName)}
              </Avatar>
              <div>
                <Text fw={500}>{selectedReservation.customerName}</Text>
                <Text size="sm" c="dimmed">{selectedReservation.vehicleName} - {selectedReservation.plateNumber}</Text>
              </div>
            </Group>

            <Divider />

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label={t('currentMileage')}
                  placeholder={t('enterMileage')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('fuelLevel')}
                  placeholder={t('selectFuelLevel')}
                  data={[
                    { value: 'full', label: t('full') },
                    { value: '3/4', label: '3/4' },
                    { value: '1/2', label: '1/2' },
                    { value: '1/4', label: '1/4' }
                  ]}
                />
              </Grid.Col>
            </Grid>

            <TextInput
              label={t('notes')}
              placeholder={t('enterAnyNotes')}
            />

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setCheckInModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconCheck size={16} />} onClick={() => setCheckInModalOpen(false)}>
                {t('completeCheckIn')}
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Container>
  )
}
