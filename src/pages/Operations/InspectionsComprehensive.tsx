import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Progress,
  Alert,
  Stepper,
  Checkbox,
  FileInput,
  Rating,
  Timeline,
  ThemeIcon
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCheck,
  IconClock,
  IconDownload,
  IconEye,
  IconFilter,
  IconSearch,
  IconMapPin,
  IconRefresh,
  IconClipboardCheck,
  IconCamera,
  IconCar,
  IconCalendar,
  IconSettings,
  IconTool,
  IconShield,
  IconExclamationMark,
  IconCheckbox,
  IconX
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface InspectionData {
  id: string
  inspection_number: string
  vehicle_id: string
  vehicle_name: string
  vehicle_make: string
  vehicle_model: string
  vehicle_year: number
  plate_number: string
  vin_number: string
  inspection_type: 'pre_rental' | 'post_rental' | 'maintenance' | 'damage_assessment' | 'periodic' | 'insurance'
  inspection_date: string
  inspection_time: string
  scheduled_date: string
  location: string
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed' | 'cancelled' | 'requires_attention'
  priority: 'low' | 'medium' | 'high' | 'critical'
  inspector_id: string
  inspector_name: string
  customer_id?: string
  customer_name?: string
  reservation_id?: string
  mileage: number
  fuel_level: number
  exterior_condition: {
    overall_rating: number
    damages: Array<{
      type: 'scratch' | 'dent' | 'crack' | 'rust' | 'missing_part' | 'other'
      location: string
      severity: 'minor' | 'moderate' | 'major' | 'critical'
      description: string
      estimated_cost: number
      photos: string[]
    }>
    photos: string[]
  }
  interior_condition: {
    overall_rating: number
    cleanliness_score: number
    damages: Array<{
      type: 'stain' | 'tear' | 'burn' | 'wear' | 'missing_item' | 'other'
      location: string
      severity: 'minor' | 'moderate' | 'major' | 'critical'
      description: string
      estimated_cost: number
      photos: string[]
    }>
    photos: string[]
  }
  mechanical_condition: {
    engine_status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
    transmission_status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
    brakes_status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
    tires_condition: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
    lights_status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
    issues: Array<{
      component: string
      issue: string
      severity: 'minor' | 'moderate' | 'major' | 'critical'
      action_required: string
      estimated_cost: number
    }>
  }
  safety_compliance: {
    seat_belts: boolean
    airbags: boolean
    fire_extinguisher: boolean
    first_aid_kit: boolean
    warning_triangle: boolean
    spare_tire: boolean
    jack_tools: boolean
    registration_documents: boolean
    insurance_documents: boolean
    compliance_score: number
  }
  checklist_items: Array<{
    category: string
    item: string
    status: 'pass' | 'fail' | 'na' | 'attention_required'
    notes?: string
    photos?: string[]
  }>
  total_estimated_cost: number
  recommendations: string[]
  next_inspection_due: string
  inspection_notes: string
  customer_signature?: string
  inspector_signature?: string
  completion_time?: string
  created_at: string
  updated_at: string
}

export function InspectionsComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [inspections, setInspections] = useState<InspectionData[]>([])
  const [activeTab, setActiveTab] = useState('all-inspections')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [priorityFilter, setPriorityFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [inspectionModalOpen, setInspectionModalOpen] = useState(false)
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false)
  const [selectedInspection, setSelectedInspection] = useState<InspectionData | null>(null)
  const [inspectionStep, setInspectionStep] = useState(0)

  // Mock data for development - comprehensive inspection structure
  const mockInspections: InspectionData[] = [
    {
      id: '1',
      inspection_number: 'INS-2024-001',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      vehicle_make: 'Toyota',
      vehicle_model: 'Camry',
      vehicle_year: 2023,
      plate_number: 'ABC-123',
      vin_number: 'JT2BF22K123456789',
      inspection_type: 'pre_rental',
      inspection_date: '2024-01-20T09:00:00Z',
      inspection_time: '09:00',
      scheduled_date: '2024-01-20T09:00:00Z',
      location: 'Dubai Airport Terminal 1',
      status: 'completed',
      priority: 'medium',
      inspector_id: '1',
      inspector_name: 'John Smith',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      reservation_id: 'RES-2024-001',
      mileage: 15000,
      fuel_level: 100,
      exterior_condition: {
        overall_rating: 9,
        damages: [],
        photos: ['exterior_1.jpg', 'exterior_2.jpg']
      },
      interior_condition: {
        overall_rating: 8,
        cleanliness_score: 95,
        damages: [],
        photos: ['interior_1.jpg']
      },
      mechanical_condition: {
        engine_status: 'excellent',
        transmission_status: 'excellent',
        brakes_status: 'good',
        tires_condition: 'good',
        lights_status: 'excellent',
        issues: []
      },
      safety_compliance: {
        seat_belts: true,
        airbags: true,
        fire_extinguisher: true,
        first_aid_kit: true,
        warning_triangle: true,
        spare_tire: true,
        jack_tools: true,
        registration_documents: true,
        insurance_documents: true,
        compliance_score: 100
      },
      checklist_items: [
        { category: 'Exterior', item: 'Body condition', status: 'pass' },
        { category: 'Interior', item: 'Seat condition', status: 'pass' },
        { category: 'Mechanical', item: 'Engine check', status: 'pass' }
      ],
      total_estimated_cost: 0,
      recommendations: ['Regular maintenance recommended', 'Tire rotation due in 5000 miles'],
      next_inspection_due: '2024-04-20T09:00:00Z',
      inspection_notes: 'Vehicle in excellent condition, ready for rental',
      inspector_signature: 'John Smith',
      completion_time: '2024-01-20T09:30:00Z',
      created_at: '2024-01-20T08:00:00Z',
      updated_at: '2024-01-20T09:30:00Z'
    },
    {
      id: '2',
      inspection_number: 'INS-2024-002',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      vehicle_make: 'BMW',
      vehicle_model: 'X5',
      vehicle_year: 2022,
      plate_number: 'XYZ-456',
      vin_number: 'WBAFR7C50BC123456',
      inspection_type: 'damage_assessment',
      inspection_date: '2024-01-20T14:00:00Z',
      inspection_time: '14:00',
      scheduled_date: '2024-01-20T14:00:00Z',
      location: 'Downtown Dubai',
      status: 'requires_attention',
      priority: 'high',
      inspector_id: '2',
      inspector_name: 'Maria Garcia',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      reservation_id: 'RES-2024-002',
      mileage: 28750,
      fuel_level: 45,
      exterior_condition: {
        overall_rating: 6,
        damages: [
          {
            type: 'scratch',
            location: 'Rear bumper',
            severity: 'minor',
            description: 'Small scratch on rear bumper',
            estimated_cost: 150,
            photos: ['damage_1.jpg']
          },
          {
            type: 'dent',
            location: 'Driver side door',
            severity: 'moderate',
            description: 'Small dent on driver side door',
            estimated_cost: 200,
            photos: ['damage_2.jpg']
          }
        ],
        photos: ['exterior_damaged_1.jpg', 'exterior_damaged_2.jpg']
      },
      interior_condition: {
        overall_rating: 7,
        cleanliness_score: 70,
        damages: [
          {
            type: 'stain',
            location: 'Front passenger seat',
            severity: 'minor',
            description: 'Coffee stain on seat',
            estimated_cost: 50,
            photos: ['interior_stain.jpg']
          }
        ],
        photos: ['interior_damaged_1.jpg']
      },
      mechanical_condition: {
        engine_status: 'good',
        transmission_status: 'good',
        brakes_status: 'good',
        tires_condition: 'fair',
        lights_status: 'good',
        issues: [
          {
            component: 'Tires',
            issue: 'Front tires showing wear',
            severity: 'moderate',
            action_required: 'Replace front tires',
            estimated_cost: 400
          }
        ]
      },
      safety_compliance: {
        seat_belts: true,
        airbags: true,
        fire_extinguisher: true,
        first_aid_kit: true,
        warning_triangle: true,
        spare_tire: true,
        jack_tools: true,
        registration_documents: true,
        insurance_documents: true,
        compliance_score: 100
      },
      checklist_items: [
        { category: 'Exterior', item: 'Body condition', status: 'attention_required', notes: 'Minor damages found' },
        { category: 'Interior', item: 'Seat condition', status: 'attention_required', notes: 'Stain on passenger seat' },
        { category: 'Mechanical', item: 'Tire condition', status: 'attention_required', notes: 'Front tires need replacement' }
      ],
      total_estimated_cost: 800,
      recommendations: ['Repair exterior damages before next rental', 'Replace front tires', 'Professional cleaning required'],
      next_inspection_due: '2024-02-20T14:00:00Z',
      inspection_notes: 'Vehicle requires attention before next rental. Multiple issues identified.',
      inspector_signature: 'Maria Garcia',
      completion_time: '2024-01-20T15:00:00Z',
      created_at: '2024-01-20T13:00:00Z',
      updated_at: '2024-01-20T15:00:00Z'
    }
  ]

  useEffect(() => {
    loadInspections()
  }, [])

  const loadInspections = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await inspectionService.getAllInspections()
      setInspections(mockInspections)
    } catch (error) {
      console.error('Error loading inspections:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredInspections = inspections.filter(inspection => {
    const matchesSearch = !searchQuery ||
      inspection.vehicle_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inspection.inspection_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inspection.plate_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inspection.inspector_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (inspection.customer_name && inspection.customer_name.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesStatus = !statusFilter || inspection.status === statusFilter
    const matchesType = !typeFilter || inspection.inspection_type === typeFilter
    const matchesPriority = !priorityFilter || inspection.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesType && matchesPriority
  })

  // Pagination
  const totalItems = filteredInspections.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedInspections = filteredInspections.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const scheduledToday = inspections.filter(i =>
    new Date(i.scheduled_date).toDateString() === new Date().toDateString()
  ).length
  const requiresAttention = inspections.filter(i => i.status === 'requires_attention').length
  const inProgress = inspections.filter(i => i.status === 'in_progress').length
  const completedToday = inspections.filter(i =>
    i.status === 'completed' &&
    new Date(i.completion_time || '').toDateString() === new Date().toDateString()
  ).length

  const stats = [
    {
      label: t('scheduledToday'),
      value: scheduledToday.toString(),
      color: 'blue',
      icon: IconCalendar
    },
    {
      label: t('requiresAttention'),
      value: requiresAttention.toString(),
      color: 'red',
      icon: IconAlertTriangle
    },
    {
      label: t('inProgress'),
      value: inProgress.toString(),
      color: 'yellow',
      icon: IconClock
    },
    {
      label: t('completedToday'),
      value: completedToday.toString(),
      color: 'green',
      icon: IconCheck
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'blue'
      case 'in_progress': return 'yellow'
      case 'completed': return 'green'
      case 'failed': return 'red'
      case 'cancelled': return 'gray'
      case 'requires_attention': return 'orange'
      default: return 'gray'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'green'
      case 'medium': return 'yellow'
      case 'high': return 'orange'
      case 'critical': return 'red'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'pre_rental': return 'blue'
      case 'post_rental': return 'cyan'
      case 'maintenance': return 'purple'
      case 'damage_assessment': return 'orange'
      case 'periodic': return 'green'
      case 'insurance': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getOverallConditionScore = (inspection: InspectionData) => {
    const exteriorScore = inspection.exterior_condition.overall_rating * 10
    const interiorScore = inspection.interior_condition.overall_rating * 10
    const complianceScore = inspection.safety_compliance.compliance_score
    return Math.round((exteriorScore + interiorScore + complianceScore) / 3)
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('inspectionsManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageVehicleInspectionsAndAssessments')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconRefresh size={16} />}>
                {t('refresh')}
              </Button>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconClipboardCheck size={16} />}
                onClick={() => setScheduleModalOpen(true)}
              >
                {t('scheduleInspection')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-inspections">{t('allInspections')}</Tabs.Tab>
            <Tabs.Tab value="scheduled">{t('scheduled')}</Tabs.Tab>
            <Tabs.Tab value="requires-attention">{t('requiresAttention')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-inspections">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder={t('searchInspections')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'scheduled', label: t('scheduled') },
                      { value: 'in_progress', label: t('inProgress') },
                      { value: 'completed', label: t('completed') },
                      { value: 'requires_attention', label: t('requiresAttention') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('inspectionType')}
                    value={typeFilter}
                    onChange={(value) => setTypeFilter(value || '')}
                    data={[
                      { value: '', label: t('allTypes') },
                      { value: 'pre_rental', label: t('preRental') },
                      { value: 'post_rental', label: t('postRental') },
                      { value: 'maintenance', label: t('maintenance') },
                      { value: 'damage_assessment', label: t('damageAssessment') },
                      { value: 'periodic', label: t('periodic') },
                      { value: 'insurance', label: t('insurance') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('priority')}
                    value={priorityFilter}
                    onChange={(value) => setPriorityFilter(value || '')}
                    data={[
                      { value: '', label: t('allPriorities') },
                      { value: 'low', label: t('low') },
                      { value: 'medium', label: t('medium') },
                      { value: 'high', label: t('high') },
                      { value: 'critical', label: t('critical') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setTypeFilter('')
                      setPriorityFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Inspections Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('vehicle')}</Table.Th>
                    <Table.Th>{t('inspectionType')}</Table.Th>
                    <Table.Th>{t('scheduledDate')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('priority')}</Table.Th>
                    <Table.Th>{t('condition')}</Table.Th>
                    <Table.Th>{t('inspector')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedInspections.map((inspection) => (
                    <Table.Tr key={inspection.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color={getStatusColor(inspection.status)}>
                            <IconCar size={16} />
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{inspection.vehicle_name}</Text>
                            <Text size="xs" c="dimmed">{inspection.plate_number}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getTypeColor(inspection.inspection_type)} size="sm">
                          {t(inspection.inspection_type)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">
                            {new Date(inspection.scheduled_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {new Date(inspection.scheduled_date).toLocaleTimeString()}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(inspection.status)} size="sm">
                          {t(inspection.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getPriorityColor(inspection.priority)} size="sm" variant="light">
                          {t(inspection.priority)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Progress
                            value={getOverallConditionScore(inspection)}
                            size="sm"
                            color={getOverallConditionScore(inspection) >= 80 ? 'green' :
                                   getOverallConditionScore(inspection) >= 60 ? 'yellow' : 'red'}
                          />
                          <Text size="xs" c="dimmed" mt={2}>
                            {getOverallConditionScore(inspection)}% {t('condition')}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{inspection.inspector_name}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedInspection(inspection)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedInspection(inspection)
                              setInspectionModalOpen(true)
                            }}
                          >
                            <IconClipboardCheck size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="orange"
                          >
                            <IconCamera size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('inspections')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="scheduled">
            <Timeline active={1} bulletSize={24} lineWidth={2}>
              {inspections
                .filter(i => i.status === 'scheduled')
                .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
                .map((inspection, index) => (
                  <Timeline.Item
                    key={inspection.id}
                    bullet={<IconClipboardCheck size={12} />}
                    title={`${new Date(inspection.scheduled_date).toLocaleTimeString()} - ${inspection.vehicle_name}`}
                  >
                    <Text c="dimmed" size="sm">
                      {t(inspection.inspection_type)} - {inspection.inspector_name}
                    </Text>
                    <Text size="xs" mt={4}>
                      {inspection.location}
                    </Text>
                    <Group gap="xs" mt={4}>
                      <Badge size="xs" color={getTypeColor(inspection.inspection_type)}>
                        {t(inspection.inspection_type)}
                      </Badge>
                      <Badge size="xs" color={getPriorityColor(inspection.priority)} variant="light">
                        {t(inspection.priority)}
                      </Badge>
                    </Group>
                  </Timeline.Item>
                ))}
            </Timeline>
          </Tabs.Panel>

          <Tabs.Panel value="requires-attention">
            <Grid>
              {inspections
                .filter(i => i.status === 'requires_attention')
                .map((inspection) => (
                  <Grid.Col key={inspection.id} span={{ base: 12, md: 6 }}>
                    <Card withBorder>
                      <Alert color="orange" icon={<IconAlertTriangle size={16} />} mb="md">
                        <Text fw={500}>{t('attentionRequired')}</Text>
                        <Text size="sm">
                          {t('estimatedCost')}: ${inspection.total_estimated_cost}
                        </Text>
                      </Alert>

                      <Group justify="space-between" mb="md">
                        <Group>
                          <Avatar color="orange">
                            <IconCar size={16} />
                          </Avatar>
                          <div>
                            <Text fw={500}>{inspection.vehicle_name}</Text>
                            <Text size="sm" c="dimmed">{inspection.plate_number}</Text>
                          </div>
                        </Group>
                        <Badge color={getTypeColor(inspection.inspection_type)}>
                          {t(inspection.inspection_type)}
                        </Badge>
                      </Group>

                      <Stack gap="xs" mb="md">
                        <Text size="sm" fw={500}>{t('issues')}:</Text>
                        {inspection.exterior_condition.damages.map((damage, idx) => (
                          <Text key={idx} size="xs" c="dimmed">
                            • {damage.location}: {damage.description} (${damage.estimated_cost})
                          </Text>
                        ))}
                        {inspection.interior_condition.damages.map((damage, idx) => (
                          <Text key={idx} size="xs" c="dimmed">
                            • {damage.location}: {damage.description} (${damage.estimated_cost})
                          </Text>
                        ))}
                        {inspection.mechanical_condition.issues.map((issue, idx) => (
                          <Text key={idx} size="xs" c="dimmed">
                            • {issue.component}: {issue.issue} (${issue.estimated_cost})
                          </Text>
                        ))}
                      </Stack>

                      <Group justify="flex-end" mt="md">
                        <Button
                          size="xs"
                          variant="light"
                          color="orange"
                          onClick={() => {
                            setSelectedInspection(inspection)
                            setViewModalOpen(true)
                          }}
                        >
                          {t('viewDetails')}
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* View Inspection Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedInspection ? selectedInspection.inspection_number : t('inspectionDetails')}
          size="xl"
        >
          {selectedInspection && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('vehicle')}</Text>
                      <Text fw={500}>{selectedInspection.vehicle_name}</Text>
                      <Text size="xs" c="dimmed">{selectedInspection.plate_number}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('inspectionType')}</Text>
                      <Badge color={getTypeColor(selectedInspection.inspection_type)}>
                        {t(selectedInspection.inspection_type)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('scheduledDate')}</Text>
                      <Text fw={500}>
                        {new Date(selectedInspection.scheduled_date).toLocaleDateString()}
                      </Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('status')}</Text>
                      <Badge color={getStatusColor(selectedInspection.status)}>
                        {t(selectedInspection.status)}
                      </Badge>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <ThemeIcon size="xl" color={getStatusColor(selectedInspection.status)}>
                        <IconClipboardCheck size={24} />
                      </ThemeIcon>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="lg">{getOverallConditionScore(selectedInspection)}%</Text>
                        <Text size="sm" c="dimmed">{t('overallCondition')}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <Grid>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('exteriorCondition')}</Text>
                  <Text fw={700} size="xl">{selectedInspection.exterior_condition.overall_rating}/10</Text>
                  <Text size="xs" c="dimmed">
                    {selectedInspection.exterior_condition.damages.length} {t('damagesFound')}
                  </Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('interiorCondition')}</Text>
                  <Text fw={700} size="xl">{selectedInspection.interior_condition.overall_rating}/10</Text>
                  <Text size="xs" c="dimmed">
                    {selectedInspection.interior_condition.cleanliness_score}% {t('cleanliness')}
                  </Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('safetyCompliance')}</Text>
                  <Text fw={700} size="xl">{selectedInspection.safety_compliance.compliance_score}%</Text>
                  <Text size="xs" c="dimmed">{t('compliant')}</Text>
                </Grid.Col>
              </Grid>

              {selectedInspection.total_estimated_cost > 0 && (
                <>
                  <Divider />
                  <Alert color="orange" icon={<IconAlertTriangle size={16} />}>
                    <Text fw={500}>{t('repairCostsEstimated')}</Text>
                    <Text size="lg" fw={700}>${selectedInspection.total_estimated_cost}</Text>
                  </Alert>
                </>
              )}

              {selectedInspection.inspection_notes && (
                <>
                  <Divider />
                  <div>
                    <Text size="sm" c="dimmed">{t('inspectionNotes')}</Text>
                    <Text>{selectedInspection.inspection_notes}</Text>
                  </div>
                </>
              )}
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
