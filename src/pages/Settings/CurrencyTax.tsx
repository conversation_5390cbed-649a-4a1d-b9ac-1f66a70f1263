import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Button,
  Stack,
  Grid,
  Select,
  NumberInput,
  Switch,
  Table,
  ActionIcon,
  Modal,
  TextInput,
  Badge,
  Alert,
  Divider
} from '@mantine/core'
import {
  IconCalculator,
  IconDeviceFloppy,
  IconPlus,
  IconEdit,
  IconTrash,
  IconCurrencyDollar,
  IconPercentage,
  IconCheck,
  IconRefresh
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'
import { useAppStore } from '../../store/useAppStore'

interface TaxRate {
  id: string
  name: string
  rate: number
  type: 'percentage' | 'fixed'
  isActive: boolean
  description: string
  applicableToVehicles: boolean
  applicableToServices: boolean
}

interface CurrencySettings {
  baseCurrency: string
  displayFormat: string
  decimalPlaces: number
  thousandSeparator: string
  decimalSeparator: string
  currencyPosition: 'before' | 'after'
  exchangeRates: { [key: string]: number }
}

export function CurrencyTax() {
  const { t } = useTranslation()
  const { settings, updateSettings } = useAppStore()

  const [currencySettings, setCurrencySettings] = useState<CurrencySettings>({
    baseCurrency: settings?.currency || 'AED',
    displayFormat: 'symbol',
    decimalPlaces: 2,
    thousandSeparator: ',',
    decimalSeparator: '.',
    currencyPosition: 'before',
    exchangeRates: {
      // Major currencies (rates relative to AED)
      'USD': 3.67,
      'EUR': 4.02,
      'GBP': 4.58,
      'JPY': 0.025,
      'CHF': 4.12,
      'CAD': 2.71,
      'AUD': 2.45,
      'CNY': 0.51,

      // Gulf currencies
      'SAR': 0.98,
      'QAR': 1.01,
      'KWD': 12.05,
      'BHD': 9.73,
      'OMR': 9.54,

      // Latin America
      'MXN': 0.18,
      'CLP': 0.0038,
      'BRL': 0.67,
      'ARS': 0.0041,
      'COP': 0.00088,
      'PEN': 0.97,

      // Asia Pacific
      'INR': 0.044,
      'KRW': 0.0028,
      'SGD': 2.72,
      'HKD': 0.47,
      'THB': 0.10,
      'MYR': 0.78,

      // Europe
      'SEK': 0.34,
      'NOK': 0.34,
      'DKK': 0.54,
      'PLN': 0.91,
      'CZK': 0.16,
      'RUB': 0.040,
      'TRY': 0.13,

      // Others
      'ZAR': 0.20,
      'ILS': 1.00,
      'NZD': 2.26
    }
  })

  const [taxRates, setTaxRates] = useState<TaxRate[]>([
    {
      id: '1',
      name: 'VAT',
      rate: 5,
      type: 'percentage',
      isActive: true,
      description: 'UAE Value Added Tax',
      applicableToVehicles: true,
      applicableToServices: true
    },
    {
      id: '2',
      name: 'Tourism Tax',
      rate: 10,
      type: 'fixed',
      isActive: true,
      description: 'Fixed tourism tax per rental',
      applicableToVehicles: true,
      applicableToServices: false
    },
    {
      id: '3',
      name: 'Service Charge',
      rate: 2.5,
      type: 'percentage',
      isActive: false,
      description: 'Additional service charge',
      applicableToVehicles: false,
      applicableToServices: true
    }
  ])

  const [addTaxModalOpen, setAddTaxModalOpen] = useState(false)
  const [editTaxModalOpen, setEditTaxModalOpen] = useState(false)
  const [selectedTax, setSelectedTax] = useState<TaxRate | null>(null)
  const [newTax, setNewTax] = useState<Partial<TaxRate>>({
    name: '',
    rate: 0,
    type: 'percentage',
    isActive: true,
    description: '',
    applicableToVehicles: true,
    applicableToServices: false
  })

  const handleSaveCurrencySettings = () => {
    updateSettings({ 
      currency: currencySettings.baseCurrency,
      currencySettings: currencySettings 
    })
    
    notifications.show({
      title: 'Success',
      message: 'Currency settings have been saved successfully!',
      color: 'green',
      icon: <IconCheck size={16} />
    })
  }

  const handleAddTax = () => {
    if (!newTax.name || newTax.rate === undefined) {
      notifications.show({
        title: 'Error',
        message: 'Please fill in all required fields',
        color: 'red'
      })
      return
    }

    const tax: TaxRate = {
      id: `tax-${Date.now()}`,
      name: newTax.name!,
      rate: newTax.rate!,
      type: newTax.type || 'percentage',
      isActive: newTax.isActive !== false,
      description: newTax.description || '',
      applicableToVehicles: newTax.applicableToVehicles !== false,
      applicableToServices: newTax.applicableToServices || false
    }

    setTaxRates(prev => [...prev, tax])

    notifications.show({
      title: 'Success',
      message: `${tax.name} tax rate has been added successfully!`,
      color: 'green',
      icon: <IconCheck size={16} />
    })

    // Reset form and close modal
    setNewTax({
      name: '',
      rate: 0,
      type: 'percentage',
      isActive: true,
      description: '',
      applicableToVehicles: true,
      applicableToServices: false
    })
    setAddTaxModalOpen(false)
  }

  const handleEditTax = () => {
    if (!selectedTax) return

    const updatedTaxRates = taxRates.map(tax => 
      tax.id === selectedTax.id ? selectedTax : tax
    )
    setTaxRates(updatedTaxRates)

    notifications.show({
      title: 'Success',
      message: `${selectedTax.name} has been updated successfully!`,
      color: 'green',
      icon: <IconCheck size={16} />
    })

    setEditTaxModalOpen(false)
    setSelectedTax(null)
  }

  const handleDeleteTax = (tax: TaxRate) => {
    const updatedTaxRates = taxRates.filter(t => t.id !== tax.id)
    setTaxRates(updatedTaxRates)

    notifications.show({
      title: 'Success',
      message: `${tax.name} tax rate has been deleted`,
      color: 'green'
    })
  }

  const handleToggleTaxStatus = (tax: TaxRate) => {
    const updatedTaxRates = taxRates.map(t => 
      t.id === tax.id ? { ...t, isActive: !t.isActive } : t
    )
    setTaxRates(updatedTaxRates)

    notifications.show({
      title: 'Success',
      message: `${tax.name} has been ${tax.isActive ? 'deactivated' : 'activated'}`,
      color: 'green'
    })
  }

  const formatCurrency = (amount: number) => {
    const formatted = amount.toFixed(currencySettings.decimalPlaces)
      .replace('.', currencySettings.decimalSeparator)
      .replace(/\B(?=(\d{3})+(?!\d))/g, currencySettings.thousandSeparator)
    
    const symbol = currencySettings.baseCurrency
    return currencySettings.currencyPosition === 'before' 
      ? `${symbol} ${formatted}` 
      : `${formatted} ${symbol}`
  }

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Group gap="sm">
              <IconCalculator size={24} />
              <div>
                <Title order={2}>Currency & Tax Settings</Title>
                <Text c="dimmed">Configure currency display and tax rates</Text>
              </div>
            </Group>
          </div>
          <Button
            onClick={handleSaveCurrencySettings}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Save Settings
          </Button>
        </Group>

        {/* Currency Settings */}
        <Card withBorder>
          <Group gap="sm" mb="md">
            <IconCurrencyDollar size={20} />
            <Title order={4}>Currency Settings</Title>
          </Group>
          <Grid>
            <Grid.Col span={6}>
              <Select
                label="Base Currency"
                description="Primary currency for the application"
                value={currencySettings.baseCurrency}
                onChange={(value) => setCurrencySettings(prev => ({ ...prev, baseCurrency: value || 'AED' }))}
                data={[
                  // Major World Currencies (Most Popular)
                  { value: 'USD', label: 'USD - US Dollar' },
                  { value: 'EUR', label: 'EUR - Euro' },
                  { value: 'GBP', label: 'GBP - British Pound' },
                  { value: 'JPY', label: 'JPY - Japanese Yen' },
                  { value: 'CHF', label: 'CHF - Swiss Franc' },
                  { value: 'CAD', label: 'CAD - Canadian Dollar' },
                  { value: 'AUD', label: 'AUD - Australian Dollar' },
                  { value: 'CNY', label: 'CNY - Chinese Yuan' },

                  // Middle East & Gulf
                  { value: 'AED', label: 'AED - UAE Dirham' },
                  { value: 'SAR', label: 'SAR - Saudi Riyal' },
                  { value: 'QAR', label: 'QAR - Qatari Riyal' },
                  { value: 'KWD', label: 'KWD - Kuwaiti Dinar' },
                  { value: 'BHD', label: 'BHD - Bahraini Dinar' },
                  { value: 'OMR', label: 'OMR - Omani Rial' },

                  // Latin America
                  { value: 'MXN', label: 'MXN - Mexican Peso' },
                  { value: 'CLP', label: 'CLP - Chilean Peso' },
                  { value: 'BRL', label: 'BRL - Brazilian Real' },
                  { value: 'ARS', label: 'ARS - Argentine Peso' },
                  { value: 'COP', label: 'COP - Colombian Peso' },
                  { value: 'PEN', label: 'PEN - Peruvian Sol' },
                  { value: 'UYU', label: 'UYU - Uruguayan Peso' },

                  // Asia Pacific
                  { value: 'INR', label: 'INR - Indian Rupee' },
                  { value: 'KRW', label: 'KRW - South Korean Won' },
                  { value: 'SGD', label: 'SGD - Singapore Dollar' },
                  { value: 'HKD', label: 'HKD - Hong Kong Dollar' },
                  { value: 'THB', label: 'THB - Thai Baht' },
                  { value: 'MYR', label: 'MYR - Malaysian Ringgit' },
                  { value: 'IDR', label: 'IDR - Indonesian Rupiah' },
                  { value: 'PHP', label: 'PHP - Philippine Peso' },
                  { value: 'VND', label: 'VND - Vietnamese Dong' },

                  // Europe
                  { value: 'SEK', label: 'SEK - Swedish Krona' },
                  { value: 'NOK', label: 'NOK - Norwegian Krone' },
                  { value: 'DKK', label: 'DKK - Danish Krone' },
                  { value: 'PLN', label: 'PLN - Polish Zloty' },
                  { value: 'CZK', label: 'CZK - Czech Koruna' },
                  { value: 'HUF', label: 'HUF - Hungarian Forint' },
                  { value: 'RON', label: 'RON - Romanian Leu' },
                  { value: 'BGN', label: 'BGN - Bulgarian Lev' },
                  { value: 'HRK', label: 'HRK - Croatian Kuna' },
                  { value: 'RUB', label: 'RUB - Russian Ruble' },
                  { value: 'TRY', label: 'TRY - Turkish Lira' },

                  // Africa
                  { value: 'ZAR', label: 'ZAR - South African Rand' },
                  { value: 'EGP', label: 'EGP - Egyptian Pound' },
                  { value: 'NGN', label: 'NGN - Nigerian Naira' },
                  { value: 'KES', label: 'KES - Kenyan Shilling' },
                  { value: 'MAD', label: 'MAD - Moroccan Dirham' },

                  // Other Important Currencies
                  { value: 'NZD', label: 'NZD - New Zealand Dollar' },
                  { value: 'ILS', label: 'ILS - Israeli Shekel' },
                  { value: 'TWD', label: 'TWD - Taiwan Dollar' }
                ]}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Currency Position"
                value={currencySettings.currencyPosition}
                onChange={(value) => setCurrencySettings(prev => ({ ...prev, currencyPosition: value as 'before' | 'after' || 'before' }))}
                data={[
                  { value: 'before', label: 'Before amount (AED 100.00)' },
                  { value: 'after', label: 'After amount (100.00 AED)' }
                ]}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <NumberInput
                label="Decimal Places"
                value={currencySettings.decimalPlaces}
                onChange={(value) => setCurrencySettings(prev => ({ ...prev, decimalPlaces: value || 2 }))}
                min={0}
                max={4}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <TextInput
                label="Thousand Separator"
                value={currencySettings.thousandSeparator}
                onChange={(e) => setCurrencySettings(prev => ({ ...prev, thousandSeparator: e.target.value }))}
                maxLength={1}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <TextInput
                label="Decimal Separator"
                value={currencySettings.decimalSeparator}
                onChange={(e) => setCurrencySettings(prev => ({ ...prev, decimalSeparator: e.target.value }))}
                maxLength={1}
              />
            </Grid.Col>
          </Grid>
          
          <Divider my="md" />
          
          <Alert color="blue" title="Preview">
            <Text>Sample amount: {formatCurrency(1234.56)}</Text>
          </Alert>
        </Card>

        {/* Exchange Rates */}
        <Card withBorder>
          <Title order={4} mb="md">Exchange Rates (to {currencySettings.baseCurrency})</Title>
          <Grid>
            {Object.entries(currencySettings.exchangeRates).map(([currency, rate]) => (
              <Grid.Col key={currency} span={4}>
                <NumberInput
                  label={`1 ${currency} =`}
                  value={rate}
                  onChange={(value) => setCurrencySettings(prev => ({
                    ...prev,
                    exchangeRates: { ...prev.exchangeRates, [currency]: value || 0 }
                  }))}
                  precision={4}
                  step={0.01}
                  rightSection={<Text size="sm">{currencySettings.baseCurrency}</Text>}
                />
              </Grid.Col>
            ))}
          </Grid>
        </Card>

        {/* Tax Rates */}
        <Card withBorder>
          <Group justify="space-between" mb="md">
            <Group gap="sm">
              <IconPercentage size={20} />
              <Title order={4}>Tax Rates</Title>
            </Group>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => setAddTaxModalOpen(true)}
            >
              Add Tax Rate
            </Button>
          </Group>

          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Tax Name</Table.Th>
                <Table.Th>Rate</Table.Th>
                <Table.Th>Type</Table.Th>
                <Table.Th>Applies To</Table.Th>
                <Table.Th>Status</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {taxRates.map((tax) => (
                <Table.Tr key={tax.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{tax.name}</Text>
                      <Text size="sm" c="dimmed">{tax.description}</Text>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Text fw={500}>
                      {tax.type === 'percentage' ? `${tax.rate}%` : formatCurrency(tax.rate)}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge variant="light" color={tax.type === 'percentage' ? 'blue' : 'green'}>
                      {tax.type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Stack gap="xs">
                      {tax.applicableToVehicles && (
                        <Badge size="xs" variant="light" color="blue">Vehicles</Badge>
                      )}
                      {tax.applicableToServices && (
                        <Badge size="xs" variant="light" color="green">Services</Badge>
                      )}
                    </Stack>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      color={tax.isActive ? 'green' : 'red'}
                      variant="light"
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleToggleTaxStatus(tax)}
                    >
                      {tax.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon
                        variant="light"
                        size="sm"
                        onClick={() => {
                          setSelectedTax(tax)
                          setEditTaxModalOpen(true)
                        }}
                      >
                        <IconEdit size={14} />
                      </ActionIcon>
                      <ActionIcon
                        variant="light"
                        size="sm"
                        color="red"
                        onClick={() => handleDeleteTax(tax)}
                      >
                        <IconTrash size={14} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>

        {/* Add Tax Modal */}
        <Modal
          opened={addTaxModalOpen}
          onClose={() => setAddTaxModalOpen(false)}
          title="Add New Tax Rate"
          size="md"
          withCloseButton
        >
          <Stack>
            <TextInput
              label="Tax Name"
              placeholder="Enter tax name"
              value={newTax.name}
              onChange={(e) => setNewTax(prev => ({ ...prev, name: e.target.value }))}
              required
            />

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Tax Rate"
                  placeholder="Enter rate"
                  value={newTax.rate}
                  onChange={(value) => setNewTax(prev => ({ ...prev, rate: value || 0 }))}
                  min={0}
                  precision={2}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Type"
                  value={newTax.type}
                  onChange={(value) => setNewTax(prev => ({ ...prev, type: value as 'percentage' | 'fixed' || 'percentage' }))}
                  data={[
                    { value: 'percentage', label: 'Percentage (%)' },
                    { value: 'fixed', label: 'Fixed Amount' }
                  ]}
                />
              </Grid.Col>
            </Grid>

            <TextInput
              label="Description"
              placeholder="Enter description"
              value={newTax.description}
              onChange={(e) => setNewTax(prev => ({ ...prev, description: e.target.value }))}
            />

            <Grid>
              <Grid.Col span={6}>
                <Switch
                  label="Apply to Vehicles"
                  checked={newTax.applicableToVehicles}
                  onChange={(event) => setNewTax(prev => ({ ...prev, applicableToVehicles: event.currentTarget.checked }))}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Apply to Services"
                  checked={newTax.applicableToServices}
                  onChange={(event) => setNewTax(prev => ({ ...prev, applicableToServices: event.currentTarget.checked }))}
                />
              </Grid.Col>
            </Grid>

            <Switch
              label="Active"
              description="Tax rate is active and will be applied"
              checked={newTax.isActive}
              onChange={(event) => setNewTax(prev => ({ ...prev, isActive: event.currentTarget.checked }))}
            />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddTaxModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddTax} leftSection={<IconDeviceFloppy size={16} />}>
                Add Tax Rate
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Edit Tax Modal */}
        <Modal
          opened={editTaxModalOpen}
          onClose={() => setEditTaxModalOpen(false)}
          title={selectedTax ? `Edit ${selectedTax.name}` : 'Edit Tax Rate'}
          size="md"
          withCloseButton
        >
          {selectedTax && (
            <Stack>
              <TextInput
                label="Tax Name"
                value={selectedTax.name}
                onChange={(e) => setSelectedTax(prev => prev ? { ...prev, name: e.target.value } : null)}
                required
              />

              <Grid>
                <Grid.Col span={6}>
                  <NumberInput
                    label="Tax Rate"
                    value={selectedTax.rate}
                    onChange={(value) => setSelectedTax(prev => prev ? { ...prev, rate: value || 0 } : null)}
                    min={0}
                    precision={2}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Select
                    label="Type"
                    value={selectedTax.type}
                    onChange={(value) => setSelectedTax(prev => prev ? { ...prev, type: value as 'percentage' | 'fixed' || 'percentage' } : null)}
                    data={[
                      { value: 'percentage', label: 'Percentage (%)' },
                      { value: 'fixed', label: 'Fixed Amount' }
                    ]}
                  />
                </Grid.Col>
              </Grid>

              <TextInput
                label="Description"
                value={selectedTax.description}
                onChange={(e) => setSelectedTax(prev => prev ? { ...prev, description: e.target.value } : null)}
              />

              <Grid>
                <Grid.Col span={6}>
                  <Switch
                    label="Apply to Vehicles"
                    checked={selectedTax.applicableToVehicles}
                    onChange={(event) => setSelectedTax(prev => prev ? { ...prev, applicableToVehicles: event.currentTarget.checked } : null)}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Switch
                    label="Apply to Services"
                    checked={selectedTax.applicableToServices}
                    onChange={(event) => setSelectedTax(prev => prev ? { ...prev, applicableToServices: event.currentTarget.checked } : null)}
                  />
                </Grid.Col>
              </Grid>

              <Switch
                label="Active"
                description="Tax rate is active and will be applied"
                checked={selectedTax.isActive}
                onChange={(event) => setSelectedTax(prev => prev ? { ...prev, isActive: event.currentTarget.checked } : null)}
              />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setEditTaxModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditTax} leftSection={<IconDeviceFloppy size={16} />}>
                  Save Changes
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Stack>
    </Container>
  )
}
