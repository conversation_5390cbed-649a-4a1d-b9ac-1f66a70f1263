import { Container, Title, Text, Card, Group, Button, Stack, Grid, TextInput, Textarea, FileInput, Switch, Select } from '@mantine/core'
import { IconBuilding, IconDeviceFloppy, IconUpload, IconMail, IconPhone, IconMapPin, IconWorld, IconCheck } from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'
import { useAppStore } from '../../store/useAppStore'

export function CompanyProfile() {
  const { t } = useTranslation()
  const { settings, updateSettings } = useAppStore()

  const [formData, setFormData] = useState({
    company_name: settings?.companyProfile?.company_name || 'Carvio Rental Company',
    business_registration: settings?.companyProfile?.business_registration || 'BR-2024-001',
    tax_number: settings?.companyProfile?.tax_number || 'TRN-*********',
    address: settings?.companyProfile?.address || '123 Business Bay, Dubai, UAE',
    phone: settings?.companyProfile?.phone || '+971-4-123-4567',
    email: settings?.companyProfile?.email || '<EMAIL>',
    website: settings?.companyProfile?.website || 'www.carvio.com',
    description: settings?.companyProfile?.description || 'Professional car rental services in UAE',
    established_year: settings?.companyProfile?.established_year || '2020',
    license_number: settings?.companyProfile?.license_number || 'LIC-2024-001',
    currency: settings?.currency || 'AED',
    timezone: settings?.companyProfile?.timezone || 'Asia/Dubai',
    language: settings?.language || 'en',
    // Business Settings
    enableOnlineBooking: settings?.companyProfile?.enableOnlineBooking !== false,
    requireDepositForBooking: settings?.companyProfile?.requireDepositForBooking !== false,
    enableSMSNotifications: settings?.emailNotifications !== false,
    enableEmailNotifications: settings?.emailNotifications !== false,
    enableMultiLocation: settings?.companyProfile?.enableMultiLocation || false
  })

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    // Update settings with company profile data
    updateSettings({
      currency: formData.currency,
      language: formData.language,
      emailNotifications: formData.enableEmailNotifications,
      companyProfile: {
        company_name: formData.company_name,
        business_registration: formData.business_registration,
        tax_number: formData.tax_number,
        address: formData.address,
        phone: formData.phone,
        email: formData.email,
        website: formData.website,
        description: formData.description,
        established_year: formData.established_year,
        license_number: formData.license_number,
        timezone: formData.timezone,
        enableOnlineBooking: formData.enableOnlineBooking,
        requireDepositForBooking: formData.requireDepositForBooking,
        enableMultiLocation: formData.enableMultiLocation
      }
    })

    notifications.show({
      title: 'Success',
      message: 'Company profile has been saved successfully!',
      color: 'green',
      icon: <IconCheck size={16} />
    })
  }

  return (
    <Container size="lg" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={2}>{t('companyProfile')}</Title>
            <Text c="dimmed">{t('manageCompanyInformationAndSettings')}</Text>
          </div>
          <Button onClick={handleSave} leftSection={<IconDeviceFloppy size={16} />}>
            {t('saveChanges')}
          </Button>
        </Group>

        {/* Company Information */}
        <Card withBorder>
          <Title order={4} mb="md">{t('basicInformation')}</Title>
          <Grid>
            <Grid.Col span={12}>
              <Group>
                <IconBuilding size={20} />
                <Text fw={500}>{t('companyDetails')}</Text>
              </Group>
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('companyName')}
                value={formData.company_name}
                onChange={(e) => handleInputChange('company_name', e.target.value)}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('businessRegistration')}
                value={formData.business_registration}
                onChange={(e) => handleInputChange('business_registration', e.target.value)}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('taxNumber')}
                value={formData.tax_number}
                onChange={(e) => handleInputChange('tax_number', e.target.value)}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('licenseNumber')}
                value={formData.license_number}
                onChange={(e) => handleInputChange('license_number', e.target.value)}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('establishedYear')}
                value={formData.established_year}
                onChange={(e) => handleInputChange('established_year', e.target.value)}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <FileInput
                label={t('companyLogo')}
                placeholder={t('uploadLogo')}
                leftSection={<IconUpload size={16} />}
                accept="image/*"
              />
            </Grid.Col>
            <Grid.Col span={12}>
              <Textarea
                label={t('companyDescription')}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
              />
            </Grid.Col>
          </Grid>
        </Card>

        {/* Contact Information */}
        <Card withBorder>
          <Title order={4} mb="md">{t('contactInformation')}</Title>
          <Grid>
            <Grid.Col span={12}>
              <Textarea
                label={t('address')}
                leftSection={<IconMapPin size={16} />}
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                rows={2}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('phoneNumber')}
                leftSection={<IconPhone size={16} />}
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('emailAddress')}
                leftSection={<IconMail size={16} />}
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                type="email"
                required
              />
            </Grid.Col>
            <Grid.Col span={12}>
              <TextInput
                label={t('website')}
                leftSection={<IconWorld size={16} />}
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="https://www.example.com"
              />
            </Grid.Col>
          </Grid>
        </Card>

        {/* Regional Settings */}
        <Card withBorder>
          <Title order={4} mb="md">{t('regionalSettings')}</Title>
          <Grid>
            <Grid.Col span={4}>
              <Select
                label={t('currency')}
                value={formData.currency}
                onChange={(value) => handleInputChange('currency', value || 'AED')}
                data={[
                  { value: 'AED', label: 'AED - UAE Dirham' },
                  { value: 'USD', label: 'USD - US Dollar' },
                  { value: 'EUR', label: 'EUR - Euro' },
                  { value: 'GBP', label: 'GBP - British Pound' }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Select
                label={t('timezone')}
                value={formData.timezone}
                onChange={(value) => handleInputChange('timezone', value || 'Asia/Dubai')}
                data={[
                  { value: 'Asia/Dubai', label: 'Dubai (GMT+4)' },
                  { value: 'UTC', label: 'UTC (GMT+0)' },
                  { value: 'America/New_York', label: 'New York (GMT-5)' },
                  { value: 'Europe/London', label: 'London (GMT+0)' }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Select
                label={t('defaultLanguage')}
                value={formData.language}
                onChange={(value) => handleInputChange('language', value || 'en')}
                data={[
                  { value: 'en', label: 'English' },
                  { value: 'ar', label: 'العربية' },
                  { value: 'fr', label: 'Français' },
                  { value: 'es', label: 'Español' }
                ]}
                required
              />
            </Grid.Col>
          </Grid>
        </Card>

        {/* Business Settings */}
        <Card withBorder>
          <Title order={4} mb="md">{t('businessSettings')}</Title>
          <Stack gap="md">
            <Switch
              label={t('enableOnlineBooking')}
              description={t('allowCustomersToBookOnline')}
              checked={formData.enableOnlineBooking}
              onChange={(event) => handleInputChange('enableOnlineBooking', event.currentTarget.checked)}
            />
            <Switch
              label={t('requireDepositForBooking')}
              description={t('mandatorySecurityDeposit')}
              checked={formData.requireDepositForBooking}
              onChange={(event) => handleInputChange('requireDepositForBooking', event.currentTarget.checked)}
            />
            <Switch
              label={t('enableSMSNotifications')}
              description={t('sendSMSUpdatesToCustomers')}
              checked={formData.enableSMSNotifications}
              onChange={(event) => handleInputChange('enableSMSNotifications', event.currentTarget.checked)}
            />
            <Switch
              label={t('enableEmailNotifications')}
              description={t('sendEmailUpdatesToCustomers')}
              checked={formData.enableEmailNotifications}
              onChange={(event) => handleInputChange('enableEmailNotifications', event.currentTarget.checked)}
            />
            <Switch
              label={t('enableMultiLocation')}
              description={t('operateFromMultipleLocations')}
              checked={formData.enableMultiLocation}
              onChange={(event) => handleInputChange('enableMultiLocation', event.currentTarget.checked)}
            />
          </Stack>
        </Card>

        {/* Save Button */}
        <Group justify="flex-end">
          <Button variant="light">
            {t('cancel')}
          </Button>
          <Button onClick={handleSave} leftSection={<IconDeviceFloppy size={16} />}>
            {t('saveChanges')}
          </Button>
        </Group>
      </Stack>
    </Container>
  )
}
