import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Button,
  Stack,
  Grid,
  TextInput,
  Textarea,
  Table,
  ActionIcon,
  Modal,
  Badge,
  Switch,
  Select,
  Alert
} from '@mantine/core'
import {
  IconMapPin,
  IconPlus,
  IconEdit,
  IconTrash,
  IconDeviceFloppy,
  IconPhone,
  IconMail,
  IconClock,
  IconCheck,
  IconX
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'
import { useAppStore } from '../../store/useAppStore'

interface Location {
  id: string
  name: string
  address: string
  city: string
  country: string
  phone: string
  email: string
  manager: string
  operatingHours: string
  isActive: boolean
  isMainBranch: boolean
  capacity: number
  notes: string
}

export function Locations() {
  const { t } = useTranslation()
  const { locations, setLocations } = useAppStore()
  
  const [locationsList, setLocationsList] = useState<Location[]>([
    {
      id: '1',
      name: 'Dubai Main Branch',
      address: '123 Sheikh Zayed Road, Business Bay',
      city: 'Dubai',
      country: 'UAE',
      phone: '+971-4-123-4567',
      email: '<EMAIL>',
      manager: '<PERSON> Al-Rashid',
      operatingHours: '08:00 - 22:00',
      isActive: true,
      isMainBranch: true,
      capacity: 150,
      notes: 'Main headquarters and largest branch'
    },
    {
      id: '2',
      name: 'Abu Dhabi Branch',
      address: '456 Corniche Road, Al Markaziyah',
      city: 'Abu Dhabi',
      country: 'UAE',
      phone: '+971-2-987-6543',
      email: '<EMAIL>',
      manager: 'Fatima Al-Zahra',
      operatingHours: '09:00 - 21:00',
      isActive: true,
      isMainBranch: false,
      capacity: 80,
      notes: 'Capital city branch with premium services'
    },
    {
      id: '3',
      name: 'Sharjah Branch',
      address: '789 King Faisal Street, Al Majaz',
      city: 'Sharjah',
      country: 'UAE',
      phone: '+971-6-555-1234',
      email: '<EMAIL>',
      manager: 'Omar Hassan',
      operatingHours: '08:30 - 20:30',
      isActive: true,
      isMainBranch: false,
      capacity: 60,
      notes: 'Northern Emirates coverage'
    }
  ])

  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null)
  const [newLocation, setNewLocation] = useState<Partial<Location>>({
    name: '',
    address: '',
    city: '',
    country: 'UAE',
    phone: '',
    email: '',
    manager: '',
    operatingHours: '09:00 - 18:00',
    isActive: true,
    isMainBranch: false,
    capacity: 50,
    notes: ''
  })

  const handleAddLocation = () => {
    if (!newLocation.name || !newLocation.address || !newLocation.city) {
      notifications.show({
        title: 'Error',
        message: 'Please fill in all required fields',
        color: 'red'
      })
      return
    }

    const location: Location = {
      id: `location-${Date.now()}`,
      name: newLocation.name!,
      address: newLocation.address!,
      city: newLocation.city!,
      country: newLocation.country || 'UAE',
      phone: newLocation.phone || '',
      email: newLocation.email || '',
      manager: newLocation.manager || '',
      operatingHours: newLocation.operatingHours || '09:00 - 18:00',
      isActive: newLocation.isActive !== false,
      isMainBranch: newLocation.isMainBranch || false,
      capacity: newLocation.capacity || 50,
      notes: newLocation.notes || ''
    }

    setLocationsList(prev => [...prev, location])
    
    // Update app store
    setLocations([...locationsList, location])

    notifications.show({
      title: 'Success',
      message: `${location.name} has been added successfully!`,
      color: 'green',
      icon: <IconCheck size={16} />
    })

    // Reset form and close modal
    setNewLocation({
      name: '',
      address: '',
      city: '',
      country: 'UAE',
      phone: '',
      email: '',
      manager: '',
      operatingHours: '09:00 - 18:00',
      isActive: true,
      isMainBranch: false,
      capacity: 50,
      notes: ''
    })
    setAddModalOpen(false)
  }

  const handleEditLocation = () => {
    if (!selectedLocation) return

    const updatedLocations = locationsList.map(loc => 
      loc.id === selectedLocation.id ? selectedLocation : loc
    )
    setLocationsList(updatedLocations)
    setLocations(updatedLocations)

    notifications.show({
      title: 'Success',
      message: `${selectedLocation.name} has been updated successfully!`,
      color: 'green',
      icon: <IconCheck size={16} />
    })

    setEditModalOpen(false)
    setSelectedLocation(null)
  }

  const handleDeleteLocation = (location: Location) => {
    if (location.isMainBranch) {
      notifications.show({
        title: 'Error',
        message: 'Cannot delete the main branch',
        color: 'red'
      })
      return
    }

    const updatedLocations = locationsList.filter(loc => loc.id !== location.id)
    setLocationsList(updatedLocations)
    setLocations(updatedLocations)

    notifications.show({
      title: 'Success',
      message: `${location.name} has been deleted`,
      color: 'green'
    })
  }

  const handleToggleStatus = (location: Location) => {
    if (location.isMainBranch && location.isActive) {
      notifications.show({
        title: 'Error',
        message: 'Cannot deactivate the main branch',
        color: 'red'
      })
      return
    }

    const updatedLocations = locationsList.map(loc => 
      loc.id === location.id ? { ...loc, isActive: !loc.isActive } : loc
    )
    setLocationsList(updatedLocations)
    setLocations(updatedLocations)

    notifications.show({
      title: 'Success',
      message: `${location.name} has been ${location.isActive ? 'deactivated' : 'activated'}`,
      color: 'green'
    })
  }

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Group gap="sm">
              <IconMapPin size={24} />
              <div>
                <Title order={2}>Locations Management</Title>
                <Text c="dimmed">Manage rental locations and branches</Text>
              </div>
            </Group>
          </div>
          <Button
            leftSection={<IconPlus size={16} />}
            onClick={() => setAddModalOpen(true)}
          >
            Add Location
          </Button>
        </Group>

        {/* Statistics */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Locations</Text>
              <Text fw={700} size="xl">{locationsList.length}</Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Active Locations</Text>
              <Text fw={700} size="xl" c="green">
                {locationsList.filter(loc => loc.isActive).length}
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Capacity</Text>
              <Text fw={700} size="xl">
                {locationsList.reduce((sum, loc) => sum + loc.capacity, 0)}
              </Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Countries</Text>
              <Text fw={700} size="xl">
                {new Set(locationsList.map(loc => loc.country)).size}
              </Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Locations Table */}
        <Card withBorder>
          <Title order={4} mb="md">All Locations</Title>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Location</Table.Th>
                <Table.Th>Manager</Table.Th>
                <Table.Th>Contact</Table.Th>
                <Table.Th>Hours</Table.Th>
                <Table.Th>Capacity</Table.Th>
                <Table.Th>Status</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {locationsList.map((location) => (
                <Table.Tr key={location.id}>
                  <Table.Td>
                    <div>
                      <Group gap="xs">
                        <Text fw={500}>{location.name}</Text>
                        {location.isMainBranch && (
                          <Badge size="xs" color="blue">Main</Badge>
                        )}
                      </Group>
                      <Text size="sm" c="dimmed">
                        {location.city}, {location.country}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{location.manager || 'Not assigned'}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Stack gap="xs">
                      {location.phone && (
                        <Group gap="xs">
                          <IconPhone size={12} />
                          <Text size="xs">{location.phone}</Text>
                        </Group>
                      )}
                      {location.email && (
                        <Group gap="xs">
                          <IconMail size={12} />
                          <Text size="xs">{location.email}</Text>
                        </Group>
                      )}
                    </Stack>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <IconClock size={12} />
                      <Text size="xs">{location.operatingHours}</Text>
                    </Group>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{location.capacity} vehicles</Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      color={location.isActive ? 'green' : 'red'}
                      variant="light"
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleToggleStatus(location)}
                    >
                      {location.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon
                        variant="light"
                        size="sm"
                        onClick={() => {
                          setSelectedLocation(location)
                          setEditModalOpen(true)
                        }}
                      >
                        <IconEdit size={14} />
                      </ActionIcon>
                      {!location.isMainBranch && (
                        <ActionIcon
                          variant="light"
                          size="sm"
                          color="red"
                          onClick={() => handleDeleteLocation(location)}
                        >
                          <IconTrash size={14} />
                        </ActionIcon>
                      )}
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>

        {/* Add Location Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title="Add New Location"
          size="lg"
          withCloseButton
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Location Name"
                  placeholder="Enter location name"
                  value={newLocation.name}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Manager"
                  placeholder="Enter manager name"
                  value={newLocation.manager}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, manager: e.target.value }))}
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Address"
              placeholder="Enter full address"
              value={newLocation.address}
              onChange={(e) => setNewLocation(prev => ({ ...prev, address: e.target.value }))}
              required
              rows={2}
            />

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="City"
                  placeholder="Enter city"
                  value={newLocation.city}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, city: e.target.value }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Country"
                  value={newLocation.country}
                  onChange={(value) => setNewLocation(prev => ({ ...prev, country: value || 'UAE' }))}
                  data={[
                    { value: 'UAE', label: 'United Arab Emirates' },
                    { value: 'SA', label: 'Saudi Arabia' },
                    { value: 'QA', label: 'Qatar' },
                    { value: 'KW', label: 'Kuwait' },
                    { value: 'BH', label: 'Bahrain' },
                    { value: 'OM', label: 'Oman' }
                  ]}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Phone"
                  placeholder="+971-4-123-4567"
                  value={newLocation.phone}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, phone: e.target.value }))}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Email"
                  placeholder="<EMAIL>"
                  value={newLocation.email}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, email: e.target.value }))}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Operating Hours"
                  placeholder="09:00 - 18:00"
                  value={newLocation.operatingHours}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, operatingHours: e.target.value }))}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Vehicle Capacity"
                  placeholder="50"
                  type="number"
                  value={newLocation.capacity?.toString()}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, capacity: parseInt(e.target.value) || 0 }))}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Switch
                  label="Active Location"
                  description="Location is operational"
                  checked={newLocation.isActive}
                  onChange={(event) => setNewLocation(prev => ({ ...prev, isActive: event.currentTarget.checked }))}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Main Branch"
                  description="This is the main headquarters"
                  checked={newLocation.isMainBranch}
                  onChange={(event) => setNewLocation(prev => ({ ...prev, isMainBranch: event.currentTarget.checked }))}
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Notes"
              placeholder="Additional notes about this location"
              value={newLocation.notes}
              onChange={(e) => setNewLocation(prev => ({ ...prev, notes: e.target.value }))}
              rows={2}
            />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddLocation} leftSection={<IconDeviceFloppy size={16} />}>
                Add Location
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Edit Location Modal */}
        <Modal
          opened={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          title={selectedLocation ? `Edit ${selectedLocation.name}` : 'Edit Location'}
          size="lg"
          withCloseButton
        >
          {selectedLocation && (
            <Stack>
              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Location Name"
                    value={selectedLocation.name}
                    onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, name: e.target.value } : null)}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Manager"
                    value={selectedLocation.manager}
                    onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, manager: e.target.value } : null)}
                  />
                </Grid.Col>
              </Grid>

              <Textarea
                label="Address"
                value={selectedLocation.address}
                onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, address: e.target.value } : null)}
                required
                rows={2}
              />

              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="City"
                    value={selectedLocation.city}
                    onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, city: e.target.value } : null)}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Select
                    label="Country"
                    value={selectedLocation.country}
                    onChange={(value) => setSelectedLocation(prev => prev ? { ...prev, country: value || 'UAE' } : null)}
                    data={[
                      { value: 'UAE', label: 'United Arab Emirates' },
                      { value: 'SA', label: 'Saudi Arabia' },
                      { value: 'QA', label: 'Qatar' },
                      { value: 'KW', label: 'Kuwait' },
                      { value: 'BH', label: 'Bahrain' },
                      { value: 'OM', label: 'Oman' }
                    ]}
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Phone"
                    value={selectedLocation.phone}
                    onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, phone: e.target.value } : null)}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Email"
                    value={selectedLocation.email}
                    onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, email: e.target.value } : null)}
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <TextInput
                    label="Operating Hours"
                    value={selectedLocation.operatingHours}
                    onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, operatingHours: e.target.value } : null)}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label="Vehicle Capacity"
                    type="number"
                    value={selectedLocation.capacity.toString()}
                    onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, capacity: parseInt(e.target.value) || 0 } : null)}
                  />
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <Switch
                    label="Active Location"
                    description="Location is operational"
                    checked={selectedLocation.isActive}
                    onChange={(event) => setSelectedLocation(prev => prev ? { ...prev, isActive: event.currentTarget.checked } : null)}
                    disabled={selectedLocation.isMainBranch}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Switch
                    label="Main Branch"
                    description="This is the main headquarters"
                    checked={selectedLocation.isMainBranch}
                    onChange={(event) => setSelectedLocation(prev => prev ? { ...prev, isMainBranch: event.currentTarget.checked } : null)}
                  />
                </Grid.Col>
              </Grid>

              <Textarea
                label="Notes"
                value={selectedLocation.notes}
                onChange={(e) => setSelectedLocation(prev => prev ? { ...prev, notes: e.target.value } : null)}
                rows={2}
              />

              {selectedLocation.isMainBranch && (
                <Alert color="blue" title="Main Branch">
                  This is the main branch and cannot be deactivated or deleted.
                </Alert>
              )}

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditLocation} leftSection={<IconDeviceFloppy size={16} />}>
                  Save Changes
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Stack>
    </Container>
  )
}
