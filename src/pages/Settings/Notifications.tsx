import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Button,
  Stack,
  Grid,
  Switch,
  Select,
  TextInput,
  NumberInput,
  Textarea,
  Badge,
  Alert,
  Divider,
  Tabs,
  Table,
  ActionIcon,
  Modal
} from '@mantine/core'
import {
  IconBell,
  IconDeviceFloppy,
  IconMail,
  IconPhone,
  IconBrandTelegram,
  IconSettings,
  IconCheck,
  IconX,
  IconEdit,
  IconTrash,
  IconPlus,
  IconRefresh
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'
import { useAppStore } from '../../store/useAppStore'

interface NotificationTemplate {
  id: string
  name: string
  type: 'email' | 'sms' | 'push'
  event: string
  subject: string
  content: string
  isActive: boolean
}

export function Notifications() {
  const { t } = useTranslation()
  const { settings, updateSettings } = useAppStore()

  const [notificationSettings, setNotificationSettings] = useState({
    // Email Settings
    emailEnabled: settings?.emailNotifications !== false,
    emailProvider: 'smtp',
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    smtpSecure: true,
    fromEmail: '<EMAIL>',
    fromName: 'Carvio Car Rental',
    
    // SMS Settings
    smsEnabled: true,
    smsProvider: 'twilio',
    twilioAccountSid: '',
    twilioAuthToken: '',
    twilioPhoneNumber: '',
    
    // Push Notifications
    pushEnabled: settings?.desktopNotifications !== false,
    pushSound: settings?.soundEnabled !== false,
    pushBadge: true,
    
    // Notification Events
    events: {
      newReservation: { email: true, sms: false, push: true },
      reservationConfirmed: { email: true, sms: true, push: true },
      reservationCancelled: { email: true, sms: false, push: true },
      paymentReceived: { email: true, sms: false, push: false },
      paymentFailed: { email: true, sms: true, push: true },
      vehicleReturned: { email: true, sms: false, push: true },
      maintenanceDue: { email: true, sms: false, push: true },
      contractExpiring: { email: true, sms: true, push: false },
      overdueReturn: { email: true, sms: true, push: true },
      lowFuel: { email: false, sms: false, push: true },
      systemAlert: { email: true, sms: false, push: true }
    }
  })

  const [templates, setTemplates] = useState<NotificationTemplate[]>([
    {
      id: '1',
      name: 'Reservation Confirmation',
      type: 'email',
      event: 'reservationConfirmed',
      subject: 'Your Reservation is Confirmed - {{reservation_number}}',
      content: 'Dear {{customer_name}},\n\nYour reservation {{reservation_number}} has been confirmed.\n\nVehicle: {{vehicle_name}}\nPickup Date: {{pickup_date}}\nReturn Date: {{return_date}}\n\nThank you for choosing Carvio!',
      isActive: true
    },
    {
      id: '2',
      name: 'Payment Reminder',
      type: 'email',
      event: 'paymentDue',
      subject: 'Payment Reminder - {{reservation_number}}',
      content: 'Dear {{customer_name}},\n\nThis is a reminder that payment for reservation {{reservation_number}} is due.\n\nAmount: {{amount}}\nDue Date: {{due_date}}\n\nPlease make payment to avoid cancellation.',
      isActive: true
    },
    {
      id: '3',
      name: 'Return Reminder',
      type: 'sms',
      event: 'returnReminder',
      subject: '',
      content: 'Hi {{customer_name}}, reminder: Your rental {{vehicle_name}} is due for return tomorrow at {{return_time}}. Location: {{return_location}}. - Carvio',
      isActive: true
    }
  ])

  const [editingTemplate, setEditingTemplate] = useState<NotificationTemplate | null>(null)
  const [isAddingTemplate, setIsAddingTemplate] = useState(false)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [formData, setFormData] = useState<NotificationTemplate>({
    id: '',
    name: '',
    type: 'email',
    event: 'newReservation',
    subject: '',
    content: '',
    isActive: true
  })

  const handleSaveSettings = () => {
    updateSettings({
      emailNotifications: notificationSettings.emailEnabled,
      desktopNotifications: notificationSettings.pushEnabled,
      soundEnabled: notificationSettings.pushSound
    })

    notifications.show({
      title: 'Success',
      message: 'Notification settings have been saved successfully!',
      color: 'green',
      icon: <IconCheck size={16} />
    })
  }

  const handleTestEmail = () => {
    notifications.show({
      title: 'Test Email Sent',
      message: 'A test email has been sent to verify your configuration',
      color: 'blue'
    })
  }

  const handleTestSMS = () => {
    notifications.show({
      title: 'Test SMS Sent',
      message: 'A test SMS has been sent to verify your configuration',
      color: 'blue'
    })
  }

  const handleEventToggle = (event: string, type: 'email' | 'sms' | 'push', value: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      events: {
        ...prev.events,
        [event]: {
          ...prev.events[event as keyof typeof prev.events],
          [type]: value
        }
      }
    }))
  }

  const handleAddTemplate = () => {
    setFormData({
      id: '',
      name: '',
      type: 'email',
      event: 'newReservation',
      subject: '',
      content: '',
      isActive: true
    })
    setIsAddingTemplate(true)
    setEditingTemplate(null)
    setIsModalOpen(true)
  }

  const handleEditTemplate = (template: NotificationTemplate) => {
    setFormData(template)
    setEditingTemplate(template)
    setIsAddingTemplate(false)
    setIsModalOpen(true)
  }

  const handleDeleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(t => t.id !== templateId))

    notifications.show({
      title: 'Template Deleted',
      message: 'Template has been removed successfully.',
      color: 'red'
    })
  }

  const handleToggleTemplateStatus = (templateId: string) => {
    setTemplates(prev => prev.map(template =>
      template.id === templateId
        ? { ...template, isActive: !template.isActive }
        : template
    ))

    notifications.show({
      title: 'Status Updated',
      message: 'Template status has been changed.',
      color: 'blue'
    })
  }

  const handleSaveTemplate = () => {
    if (!formData.name.trim()) {
      notifications.show({
        title: 'Validation Error',
        message: 'Template name is required.',
        color: 'red'
      })
      return
    }

    if (isAddingTemplate) {
      // Add new template
      const newTemplate = {
        ...formData,
        id: Date.now().toString()
      }
      setTemplates(prev => [...prev, newTemplate])

      notifications.show({
        title: 'Template Added',
        message: 'New template has been created successfully.',
        color: 'green',
        icon: <IconCheck size={16} />
      })
    } else {
      // Update existing template
      setTemplates(prev => prev.map(template =>
        template.id === formData.id ? formData : template
      ))

      notifications.show({
        title: 'Template Updated',
        message: 'Template has been updated successfully.',
        color: 'green',
        icon: <IconCheck size={16} />
      })
    }

    setIsModalOpen(false)
    setEditingTemplate(null)
    setIsAddingTemplate(false)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingTemplate(null)
    setIsAddingTemplate(false)
  }

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Group gap="sm">
              <IconBell size={24} />
              <div>
                <Title order={2}>Notification Settings</Title>
                <Text c="dimmed">Configure email, SMS, and push notifications</Text>
              </div>
            </Group>
          </div>
          <Button
            onClick={handleSaveSettings}
            leftSection={<IconDeviceFloppy size={16} />}
          >
            Save Settings
          </Button>
        </Group>

        <Tabs defaultValue="general">
          <Tabs.List>
            <Tabs.Tab value="general" leftSection={<IconSettings size={16} />}>
              General Settings
            </Tabs.Tab>
            <Tabs.Tab value="email" leftSection={<IconMail size={16} />}>
              Email Configuration
            </Tabs.Tab>
            <Tabs.Tab value="sms" leftSection={<IconPhone size={16} />}>
              SMS Configuration
            </Tabs.Tab>
            <Tabs.Tab value="events" leftSection={<IconBell size={16} />}>
              Event Notifications
            </Tabs.Tab>
            <Tabs.Tab value="templates" leftSection={<IconEdit size={16} />}>
              Templates
            </Tabs.Tab>
          </Tabs.List>

          {/* General Settings Tab */}
          <Tabs.Panel value="general">
            <Card withBorder mt="md">
              <Title order={4} mb="md">General Notification Settings</Title>
              <Grid>
                <Grid.Col span={6}>
                  <Switch
                    label="Enable Email Notifications"
                    description="Send notifications via email"
                    checked={notificationSettings.emailEnabled}
                    onChange={(event) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      emailEnabled: event.currentTarget.checked 
                    }))}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Switch
                    label="Enable SMS Notifications"
                    description="Send notifications via SMS"
                    checked={notificationSettings.smsEnabled}
                    onChange={(event) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      smsEnabled: event.currentTarget.checked 
                    }))}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Switch
                    label="Enable Push Notifications"
                    description="Show browser notifications"
                    checked={notificationSettings.pushEnabled}
                    onChange={(event) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      pushEnabled: event.currentTarget.checked 
                    }))}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Switch
                    label="Notification Sounds"
                    description="Play sound for notifications"
                    checked={notificationSettings.pushSound}
                    onChange={(event) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      pushSound: event.currentTarget.checked 
                    }))}
                    disabled={!notificationSettings.pushEnabled}
                  />
                </Grid.Col>
              </Grid>
            </Card>
          </Tabs.Panel>

          {/* Email Configuration Tab */}
          <Tabs.Panel value="email">
            <Card withBorder mt="md">
              <Group justify="space-between" mb="md">
                <Title order={4}>Email Configuration</Title>
                <Button
                  variant="light"
                  onClick={handleTestEmail}
                  disabled={!notificationSettings.emailEnabled}
                >
                  Send Test Email
                </Button>
              </Group>
              
              <Stack gap="md">
                <Grid>
                  <Grid.Col span={6}>
                    <TextInput
                      label="From Email"
                      placeholder="<EMAIL>"
                      value={notificationSettings.fromEmail}
                      onChange={(e) => setNotificationSettings(prev => ({ 
                        ...prev, 
                        fromEmail: e.target.value 
                      }))}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <TextInput
                      label="From Name"
                      placeholder="Carvio Car Rental"
                      value={notificationSettings.fromName}
                      onChange={(e) => setNotificationSettings(prev => ({ 
                        ...prev, 
                        fromName: e.target.value 
                      }))}
                    />
                  </Grid.Col>
                </Grid>

                <Divider label="SMTP Configuration" />

                <Grid>
                  <Grid.Col span={6}>
                    <TextInput
                      label="SMTP Host"
                      placeholder="smtp.gmail.com"
                      value={notificationSettings.smtpHost}
                      onChange={(e) => setNotificationSettings(prev => ({ 
                        ...prev, 
                        smtpHost: e.target.value 
                      }))}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <NumberInput
                      label="SMTP Port"
                      placeholder="587"
                      value={notificationSettings.smtpPort}
                      onChange={(value) => setNotificationSettings(prev => ({ 
                        ...prev, 
                        smtpPort: value || 587 
                      }))}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <TextInput
                      label="Username"
                      placeholder="<EMAIL>"
                      value={notificationSettings.smtpUsername}
                      onChange={(e) => setNotificationSettings(prev => ({ 
                        ...prev, 
                        smtpUsername: e.target.value 
                      }))}
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <TextInput
                      label="Password"
                      type="password"
                      placeholder="Your app password"
                      value={notificationSettings.smtpPassword}
                      onChange={(e) => setNotificationSettings(prev => ({ 
                        ...prev, 
                        smtpPassword: e.target.value 
                      }))}
                    />
                  </Grid.Col>
                </Grid>

                <Switch
                  label="Use Secure Connection (TLS)"
                  checked={notificationSettings.smtpSecure}
                  onChange={(event) => setNotificationSettings(prev => ({ 
                    ...prev, 
                    smtpSecure: event.currentTarget.checked 
                  }))}
                />
              </Stack>
            </Card>
          </Tabs.Panel>

          {/* SMS Configuration Tab */}
          <Tabs.Panel value="sms">
            <Card withBorder mt="md">
              <Group justify="space-between" mb="md">
                <Title order={4}>SMS Configuration</Title>
                <Button
                  variant="light"
                  onClick={handleTestSMS}
                  disabled={!notificationSettings.smsEnabled}
                >
                  Send Test SMS
                </Button>
              </Group>

              <Stack gap="md">
                <Select
                  label="SMS Provider"
                  value={notificationSettings.smsProvider}
                  onChange={(value) => setNotificationSettings(prev => ({
                    ...prev,
                    smsProvider: value || 'twilio'
                  }))}
                  data={[
                    { value: 'twilio', label: 'Twilio' },
                    { value: 'aws-sns', label: 'AWS SNS' },
                    { value: 'nexmo', label: 'Vonage (Nexmo)' },
                    { value: 'messagebird', label: 'MessageBird' }
                  ]}
                />

                {notificationSettings.smsProvider === 'twilio' && (
                  <Grid>
                    <Grid.Col span={6}>
                      <TextInput
                        label="Account SID"
                        placeholder="Your Twilio Account SID"
                        value={notificationSettings.twilioAccountSid}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          twilioAccountSid: e.target.value
                        }))}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <TextInput
                        label="Auth Token"
                        type="password"
                        placeholder="Your Twilio Auth Token"
                        value={notificationSettings.twilioAuthToken}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          twilioAuthToken: e.target.value
                        }))}
                      />
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <TextInput
                        label="Phone Number"
                        placeholder="+**********"
                        value={notificationSettings.twilioPhoneNumber}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          twilioPhoneNumber: e.target.value
                        }))}
                      />
                    </Grid.Col>
                  </Grid>
                )}

                <Alert color="blue" title="SMS Configuration">
                  <Text size="sm">
                    Configure your SMS provider credentials to enable SMS notifications.
                    Make sure to verify your phone numbers and comply with local regulations.
                  </Text>
                </Alert>
              </Stack>
            </Card>
          </Tabs.Panel>

          {/* Event Notifications Tab */}
          <Tabs.Panel value="events">
            <Card withBorder mt="md">
              <Title order={4} mb="md">Event Notification Settings</Title>
              <Text size="sm" c="dimmed" mb="md">
                Configure which events trigger notifications and through which channels
              </Text>

              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Event</Table.Th>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>SMS</Table.Th>
                    <Table.Th>Push</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {Object.entries(notificationSettings.events).map(([event, settings]) => (
                    <Table.Tr key={event}>
                      <Table.Td>
                        <Text fw={500}>
                          {event.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Switch
                          checked={settings.email}
                          onChange={(e) => handleEventToggle(event, 'email', e.currentTarget.checked)}
                          disabled={!notificationSettings.emailEnabled}
                        />
                      </Table.Td>
                      <Table.Td>
                        <Switch
                          checked={settings.sms}
                          onChange={(e) => handleEventToggle(event, 'sms', e.currentTarget.checked)}
                          disabled={!notificationSettings.smsEnabled}
                        />
                      </Table.Td>
                      <Table.Td>
                        <Switch
                          checked={settings.push}
                          onChange={(e) => handleEventToggle(event, 'push', e.currentTarget.checked)}
                          disabled={!notificationSettings.pushEnabled}
                        />
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Card>
          </Tabs.Panel>

          {/* Templates Tab */}
          <Tabs.Panel value="templates">
            <Card withBorder mt="md">
              <Group justify="space-between" mb="md">
                <Title order={4}>Notification Templates</Title>
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={handleAddTemplate}
                >
                  Add Template
                </Button>
              </Group>

              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Template Name</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Event</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {templates.map((template) => (
                    <Table.Tr key={template.id}>
                      <Table.Td>
                        <Text fw={500}>{template.name}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          variant="light"
                          color={
                            template.type === 'email' ? 'blue' :
                            template.type === 'sms' ? 'green' : 'orange'
                          }
                        >
                          {template.type.toUpperCase()}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{template.event}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          variant="light"
                          color={template.isActive ? 'green' : 'red'}
                          style={{ cursor: 'pointer' }}
                          onClick={() => handleToggleTemplateStatus(template.id)}
                        >
                          {template.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => handleEditTemplate(template)}
                          >
                            <IconEdit size={14} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="red"
                            onClick={() => handleDeleteTemplate(template.id)}
                          >
                            <IconTrash size={14} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              <Alert color="yellow" title="Template Variables" mt="md">
                <Text size="sm">
                  Use these variables in your templates: <Text component="span" ff="monospace">
                    {'{{customer_name}}'}, {'{{reservation_number}}'}, {'{{vehicle_name}}'}, {'{{pickup_date}}'}, {'{{return_date}}'}, {'{{amount}}'}, {'{{due_date}}'}
                  </Text>
                </Text>
              </Alert>
            </Card>
          </Tabs.Panel>
        </Tabs>
      </Stack>

      {/* Template Modal */}
      <Modal
        opened={isModalOpen}
        onClose={handleCloseModal}
        title={isAddingTemplate ? 'Add New Template' : 'Edit Template'}
        size="lg"
      >
        <Stack gap="md">
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="Template Name"
                placeholder="Enter template name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Type"
                value={formData.type}
                onChange={(value) => setFormData(prev => ({ ...prev, type: value as 'email' | 'sms' | 'push' }))}
                data={[
                  { value: 'email', label: 'Email' },
                  { value: 'sms', label: 'SMS' },
                  { value: 'push', label: 'Push Notification' }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Select
            label="Event"
            value={formData.event}
            onChange={(value) => setFormData(prev => ({ ...prev, event: value || 'newReservation' }))}
            data={[
              { value: 'newReservation', label: 'New Reservation' },
              { value: 'reservationConfirmed', label: 'Reservation Confirmed' },
              { value: 'reservationCancelled', label: 'Reservation Cancelled' },
              { value: 'paymentReceived', label: 'Payment Received' },
              { value: 'paymentDue', label: 'Payment Due' },
              { value: 'paymentFailed', label: 'Payment Failed' },
              { value: 'vehicleReturned', label: 'Vehicle Returned' },
              { value: 'returnReminder', label: 'Return Reminder' },
              { value: 'maintenanceDue', label: 'Maintenance Due' },
              { value: 'contractExpiring', label: 'Contract Expiring' },
              { value: 'overdueReturn', label: 'Overdue Return' }
            ]}
            required
          />

          {formData.type === 'email' && (
            <TextInput
              label="Subject"
              placeholder="Enter email subject"
              value={formData.subject}
              onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
            />
          )}

          <Textarea
            label="Content"
            placeholder="Enter template content"
            value={formData.content}
            onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
            minRows={4}
            required
          />

          <Switch
            label="Active"
            description="Enable this template"
            checked={formData.isActive}
            onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.currentTarget.checked }))}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={handleCloseModal}>
              Cancel
            </Button>
            <Button onClick={handleSaveTemplate}>
              {isAddingTemplate ? 'Add Template' : 'Update Template'}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
