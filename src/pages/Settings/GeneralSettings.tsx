import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Button,
  Stack,
  Grid,
  Switch,
  Select,
  NumberInput,
  Slider,
  ColorInput,
  Divider,
  <PERSON><PERSON>,
  Badge
} from '@mantine/core'
import {
  IconSettings,
  IconDeviceFloppy,
  IconPalette,
  IconBell,
  IconShield,
  IconDatabase,
  IconRefresh,
  IconCheck
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'
import { useAppStore } from '../../store/useAppStore'

export function GeneralSettings() {
  const { t } = useTranslation()
  const { settings, updateSettings } = useAppStore()

  console.log('Current settings from store:', settings) // Debug log
  
  const [formData, setFormData] = useState({
    // Application Preferences
    theme: settings?.theme || 'dark',
    language: settings?.language || 'en',
    dateFormat: settings?.dateFormat || 'DD/MM/YYYY',
    timeFormat: settings?.timeFormat || '24h',
    currency: settings?.currency || 'AED',
    
    // UI Customization
    sidebarCollapsed: settings?.sidebarCollapsed || false,
    showWelcomeMessage: settings?.showWelcomeMessage !== false,
    enableAnimations: settings?.enableAnimations !== false,
    compactMode: settings?.compactMode || false,
    primaryColor: settings?.primaryColor || '#1e5c7a',
    
    // Performance Settings
    autoSave: settings?.autoSave !== false,
    autoSaveInterval: settings?.autoSaveInterval || 30,
    cacheEnabled: settings?.cacheEnabled !== false,
    maxRecordsPerPage: settings?.maxRecordsPerPage || 50,
    
    // Security Options
    sessionTimeout: settings?.sessionTimeout || 60,
    requirePasswordChange: settings?.requirePasswordChange || false,
    enableAuditLog: settings?.enableAuditLog !== false,
    
    // Notifications
    desktopNotifications: settings?.desktopNotifications !== false,
    soundEnabled: settings?.soundEnabled !== false,
    emailNotifications: settings?.emailNotifications !== false,
    
    // System Behavior
    confirmDeletions: settings?.confirmDeletions !== false,
    autoBackup: settings?.autoBackup !== false,
    backupFrequency: settings?.backupFrequency || 'daily',
    debugMode: settings?.debugMode || false
  })

  // Update form data when settings change in store
  useEffect(() => {
    if (settings) {
      setFormData({
        theme: settings.theme || 'dark',
        language: settings.language || 'en',
        dateFormat: settings.dateFormat || 'DD/MM/YYYY',
        timeFormat: settings.timeFormat || '24h',
        currency: settings.currency || 'AED',
        sidebarCollapsed: settings.sidebarCollapsed || false,
        showWelcomeMessage: settings.showWelcomeMessage !== false,
        enableAnimations: settings.enableAnimations !== false,
        compactMode: settings.compactMode || false,
        primaryColor: settings.primaryColor || '#1e5c7a',
        autoSave: settings.autoSave !== false,
        autoSaveInterval: settings.autoSaveInterval || 30,
        cacheEnabled: settings.cacheEnabled !== false,
        maxRecordsPerPage: settings.maxRecordsPerPage || 50,
        sessionTimeout: settings.sessionTimeout || 60,
        requirePasswordChange: settings.requirePasswordChange || false,
        enableAuditLog: settings.enableAuditLog !== false,
        desktopNotifications: settings.desktopNotifications !== false,
        soundEnabled: settings.soundEnabled !== false,
        emailNotifications: settings.emailNotifications !== false,
        confirmDeletions: settings.confirmDeletions !== false,
        autoBackup: settings.autoBackup !== false,
        backupFrequency: settings.backupFrequency || 'daily',
        debugMode: settings.debugMode || false
      })
    }
  }, [settings])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    // Ensure proper type casting for the settings
    const settingsToSave = {
      theme: formData.theme as 'light' | 'dark' | 'auto',
      language: formData.language,
      dateFormat: formData.dateFormat,
      timeFormat: formData.timeFormat as '12h' | '24h',
      currency: formData.currency,
      sidebarCollapsed: formData.sidebarCollapsed,
      showWelcomeMessage: formData.showWelcomeMessage,
      enableAnimations: formData.enableAnimations,
      compactMode: formData.compactMode,
      primaryColor: formData.primaryColor,
      autoSave: formData.autoSave,
      autoSaveInterval: formData.autoSaveInterval,
      cacheEnabled: formData.cacheEnabled,
      maxRecordsPerPage: formData.maxRecordsPerPage,
      sessionTimeout: formData.sessionTimeout,
      requirePasswordChange: formData.requirePasswordChange,
      enableAuditLog: formData.enableAuditLog,
      desktopNotifications: formData.desktopNotifications,
      soundEnabled: formData.soundEnabled,
      emailNotifications: formData.emailNotifications,
      confirmDeletions: formData.confirmDeletions,
      autoBackup: formData.autoBackup,
      backupFrequency: formData.backupFrequency as 'hourly' | 'daily' | 'weekly' | 'monthly',
      debugMode: formData.debugMode
    }

    console.log('Saving settings:', settingsToSave) // Debug log
    updateSettings(settingsToSave)

    // Verify settings were saved by checking store after a brief delay
    setTimeout(() => {
      console.log('Settings after save:', useAppStore.getState().settings)
    }, 100)

    notifications.show({
      title: 'Success',
      message: 'General settings have been saved successfully!',
      color: 'green',
      icon: <IconCheck size={16} />
    })
  }

  const handleReset = () => {
    setFormData({
      theme: 'dark',
      language: 'en',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      currency: 'AED',
      sidebarCollapsed: false,
      showWelcomeMessage: true,
      enableAnimations: true,
      compactMode: false,
      primaryColor: '#1e5c7a',
      autoSave: true,
      autoSaveInterval: 30,
      cacheEnabled: true,
      maxRecordsPerPage: 50,
      sessionTimeout: 60,
      requirePasswordChange: false,
      enableAuditLog: true,
      desktopNotifications: true,
      soundEnabled: true,
      emailNotifications: true,
      confirmDeletions: true,
      autoBackup: true,
      backupFrequency: 'daily',
      debugMode: false
    })
    notifications.show({
      title: 'Settings Reset',
      message: 'All settings have been reset to default values',
      color: 'blue'
    })
  }

  return (
    <Container size="lg" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Group gap="sm">
              <IconSettings size={24} />
              <div>
                <Title order={2}>General Settings</Title>
                <Text c="dimmed">Configure application preferences and behavior</Text>
              </div>
            </Group>
          </div>
          <Group>
            <Button variant="light" onClick={handleReset} leftSection={<IconRefresh size={16} />}>
              Reset to Defaults
            </Button>
            <Button onClick={handleSave} leftSection={<IconDeviceFloppy size={16} />}>
              Save Changes
            </Button>
          </Group>
        </Group>

        {/* Application Preferences */}
        <Card withBorder>
          <Group gap="sm" mb="md">
            <IconSettings size={20} />
            <Title order={4}>Application Preferences</Title>
          </Group>
          <Grid>
            <Grid.Col span={6}>
              <Select
                label="Theme"
                description="Choose your preferred theme"
                value={formData.theme}
                onChange={(value) => handleInputChange('theme', value)}
                data={[
                  { value: 'light', label: 'Light Theme' },
                  { value: 'dark', label: 'Dark Theme' },
                  { value: 'auto', label: 'Auto (System)' }
                ]}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Language"
                description="Default application language"
                value={formData.language}
                onChange={(value) => handleInputChange('language', value)}
                data={[
                  { value: 'en', label: 'English' },
                  { value: 'ar', label: 'العربية (Arabic)' },
                  { value: 'fr', label: 'Français (French)' },
                  { value: 'es', label: 'Español (Spanish)' }
                ]}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Select
                label="Date Format"
                value={formData.dateFormat}
                onChange={(value) => handleInputChange('dateFormat', value)}
                data={[
                  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
                  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
                  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' }
                ]}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Select
                label="Time Format"
                value={formData.timeFormat}
                onChange={(value) => handleInputChange('timeFormat', value)}
                data={[
                  { value: '24h', label: '24 Hour (14:30)' },
                  { value: '12h', label: '12 Hour (2:30 PM)' }
                ]}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Select
                label="Default Currency"
                value={formData.currency}
                onChange={(value) => handleInputChange('currency', value)}
                data={[
                  { value: 'AED', label: 'AED - UAE Dirham' },
                  { value: 'USD', label: 'USD - US Dollar' },
                  { value: 'EUR', label: 'EUR - Euro' },
                  { value: 'GBP', label: 'GBP - British Pound' }
                ]}
              />
            </Grid.Col>
          </Grid>
        </Card>

        {/* UI Customization */}
        <Card withBorder>
          <Group gap="sm" mb="md">
            <IconPalette size={20} />
            <Title order={4}>UI Customization</Title>
          </Group>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <ColorInput
                  label="Primary Color"
                  description="Main brand color for the application"
                  value={formData.primaryColor}
                  onChange={(value) => handleInputChange('primaryColor', value)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Records Per Page"
                  description="Default number of records to show in tables"
                  value={formData.maxRecordsPerPage}
                  onChange={(value) => handleInputChange('maxRecordsPerPage', value)}
                  min={10}
                  max={200}
                  step={10}
                />
              </Grid.Col>
            </Grid>
            <Divider />
            <Grid>
              <Grid.Col span={6}>
                <Switch
                  label="Collapse Sidebar by Default"
                  description="Start with sidebar collapsed"
                  checked={formData.sidebarCollapsed}
                  onChange={(event) => handleInputChange('sidebarCollapsed', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Show Welcome Message"
                  description="Display welcome message on dashboard"
                  checked={formData.showWelcomeMessage}
                  onChange={(event) => handleInputChange('showWelcomeMessage', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Enable Animations"
                  description="Use smooth animations and transitions"
                  checked={formData.enableAnimations}
                  onChange={(event) => handleInputChange('enableAnimations', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Compact Mode"
                  description="Use smaller spacing and components"
                  checked={formData.compactMode}
                  onChange={(event) => handleInputChange('compactMode', event.currentTarget.checked)}
                />
              </Grid.Col>
            </Grid>
          </Stack>
        </Card>

        {/* Performance Settings */}
        <Card withBorder>
          <Group gap="sm" mb="md">
            <IconDatabase size={20} />
            <Title order={4}>Performance Settings</Title>
          </Group>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <Switch
                  label="Auto Save"
                  description="Automatically save changes"
                  checked={formData.autoSave}
                  onChange={(event) => handleInputChange('autoSave', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Enable Cache"
                  description="Cache data for better performance"
                  checked={formData.cacheEnabled}
                  onChange={(event) => handleInputChange('cacheEnabled', event.currentTarget.checked)}
                />
              </Grid.Col>
            </Grid>
            {formData.autoSave && (
              <div>
                <Text size="sm" fw={500} mb="xs">Auto Save Interval (seconds)</Text>
                <Slider
                  value={formData.autoSaveInterval}
                  onChange={(value) => handleInputChange('autoSaveInterval', value)}
                  min={10}
                  max={300}
                  step={10}
                  marks={[
                    { value: 30, label: '30s' },
                    { value: 60, label: '1m' },
                    { value: 120, label: '2m' },
                    { value: 300, label: '5m' }
                  ]}
                />
              </div>
            )}
          </Stack>
        </Card>

        {/* Security Options */}
        <Card withBorder>
          <Group gap="sm" mb="md">
            <IconShield size={20} />
            <Title order={4}>Security Options</Title>
          </Group>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Session Timeout (minutes)"
                  description="Auto logout after inactivity"
                  value={formData.sessionTimeout}
                  onChange={(value) => handleInputChange('sessionTimeout', value)}
                  min={5}
                  max={480}
                  step={5}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Require Password Change"
                  description="Force password change on first login"
                  checked={formData.requirePasswordChange}
                  onChange={(event) => handleInputChange('requirePasswordChange', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Enable Audit Log"
                  description="Track all user actions"
                  checked={formData.enableAuditLog}
                  onChange={(event) => handleInputChange('enableAuditLog', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Confirm Deletions"
                  description="Show confirmation dialog for deletions"
                  checked={formData.confirmDeletions}
                  onChange={(event) => handleInputChange('confirmDeletions', event.currentTarget.checked)}
                />
              </Grid.Col>
            </Grid>
          </Stack>
        </Card>

        {/* Notification Settings */}
        <Card withBorder>
          <Group gap="sm" mb="md">
            <IconBell size={20} />
            <Title order={4}>Notification Settings</Title>
          </Group>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <Switch
                  label="Desktop Notifications"
                  description="Show browser notifications"
                  checked={formData.desktopNotifications}
                  onChange={(event) => handleInputChange('desktopNotifications', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Sound Notifications"
                  description="Play sound for notifications"
                  checked={formData.soundEnabled}
                  onChange={(event) => handleInputChange('soundEnabled', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Email Notifications"
                  description="Send email notifications"
                  checked={formData.emailNotifications}
                  onChange={(event) => handleInputChange('emailNotifications', event.currentTarget.checked)}
                />
              </Grid.Col>
            </Grid>
          </Stack>
        </Card>

        {/* System Behavior */}
        <Card withBorder>
          <Group gap="sm" mb="md">
            <IconSettings size={20} />
            <Title order={4}>System Behavior</Title>
          </Group>
          <Stack gap="md">
            <Grid>
              <Grid.Col span={6}>
                <Switch
                  label="Auto Backup"
                  description="Automatically backup data"
                  checked={formData.autoBackup}
                  onChange={(event) => handleInputChange('autoBackup', event.currentTarget.checked)}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Backup Frequency"
                  value={formData.backupFrequency}
                  onChange={(value) => handleInputChange('backupFrequency', value)}
                  data={[
                    { value: 'hourly', label: 'Every Hour' },
                    { value: 'daily', label: 'Daily' },
                    { value: 'weekly', label: 'Weekly' },
                    { value: 'monthly', label: 'Monthly' }
                  ]}
                  disabled={!formData.autoBackup}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label="Debug Mode"
                  description="Enable detailed logging (for developers)"
                  checked={formData.debugMode}
                  onChange={(event) => handleInputChange('debugMode', event.currentTarget.checked)}
                />
              </Grid.Col>
            </Grid>
          </Stack>
        </Card>

        {/* Current Settings Summary */}
        <Alert color="blue" title="Current Configuration">
          <Text size="sm">
            Theme: <Badge variant="light">{formData.theme}</Badge> •
            Language: <Badge variant="light">{formData.language}</Badge> •
            Currency: <Badge variant="light">{formData.currency}</Badge> •
            Auto Save: <Badge variant="light" color={formData.autoSave ? 'green' : 'red'}>
              {formData.autoSave ? 'Enabled' : 'Disabled'}
            </Badge>
          </Text>
        </Alert>

        {/* Save Actions */}
        <Group justify="flex-end">
          <Button variant="light" onClick={handleReset}>
            Reset to Defaults
          </Button>
          <Button onClick={handleSave} leftSection={<IconDeviceFloppy size={16} />}>
            Save All Settings
          </Button>
        </Group>
      </Stack>
    </Container>
  )
}
