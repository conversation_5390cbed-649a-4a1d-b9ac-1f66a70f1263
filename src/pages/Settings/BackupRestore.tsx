import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Button,
  Stack,
  Grid,
  Switch,
  Select,
  Progress,
  Alert,
  Badge,
  Table,
  ActionIcon,
  Modal,
  FileInput,
  Divider,
  List,
  ThemeIcon
} from '@mantine/core'
import {
  IconDatabase,
  IconDownload,
  IconUpload,
  IconDeviceFloppy,
  IconRefresh,
  IconTrash,
  IconCheck,
  IconX,
  IconClock,
  IconAlertTriangle,
  IconCloudUpload,
  IconFile,
  IconFileExport,
  IconRestore
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'
import { useAppStore } from '../../store/useAppStore'

interface BackupRecord {
  id: string
  name: string
  type: 'manual' | 'automatic' | 'scheduled'
  size: string
  date: string
  status: 'completed' | 'in_progress' | 'failed'
  includes: string[]
  location: 'local' | 'cloud'
}

export function BackupRestore() {
  const { t } = useTranslation()
  const { settings, updateSettings, vehicles, customers, reservations, contracts } = useAppStore()

  const [backupSettings, setBackupSettings] = useState({
    autoBackup: settings?.autoBackup !== false,
    backupFrequency: settings?.backupFrequency || 'daily',
    backupLocation: 'local',
    cloudProvider: 'google_drive',
    includeVehicles: true,
    includeCustomers: true,
    includeReservations: true,
    includeContracts: true,
    includeSettings: true,
    includeReports: false,
    retentionDays: 30,
    compressionEnabled: true
  })

  const [backupRecords, setBackupRecords] = useState<BackupRecord[]>([
    {
      id: '1',
      name: 'Daily Backup - 2024-01-15',
      type: 'automatic',
      size: '2.4 MB',
      date: '2024-01-15 02:00:00',
      status: 'completed',
      includes: ['Vehicles', 'Customers', 'Reservations', 'Settings'],
      location: 'local'
    },
    {
      id: '2',
      name: 'Manual Backup - Before Update',
      type: 'manual',
      size: '2.6 MB',
      date: '2024-01-14 14:30:00',
      status: 'completed',
      includes: ['All Data'],
      location: 'cloud'
    },
    {
      id: '3',
      name: 'Weekly Backup - 2024-01-08',
      type: 'scheduled',
      size: '2.2 MB',
      date: '2024-01-08 01:00:00',
      status: 'completed',
      includes: ['Vehicles', 'Customers', 'Reservations'],
      location: 'local'
    }
  ])

  const [restoreModalOpen, setRestoreModalOpen] = useState(false)
  const [selectedBackup, setSelectedBackup] = useState<BackupRecord | null>(null)
  const [restoreFile, setRestoreFile] = useState<File | null>(null)
  const [backupProgress, setBackupProgress] = useState(0)
  const [isBackingUp, setIsBackingUp] = useState(false)

  const handleSaveSettings = () => {
    updateSettings({
      autoBackup: backupSettings.autoBackup,
      backupFrequency: backupSettings.backupFrequency as any
    })

    notifications.show({
      title: 'Success',
      message: 'Backup settings have been saved successfully!',
      color: 'green',
      icon: <IconCheck size={16} />
    })
  }

  const handleCreateBackup = async () => {
    setIsBackingUp(true)
    setBackupProgress(0)

    // Simulate backup progress
    const interval = setInterval(() => {
      setBackupProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsBackingUp(false)
          
          // Add new backup record
          const newBackup: BackupRecord = {
            id: `backup-${Date.now()}`,
            name: `Manual Backup - ${new Date().toLocaleDateString()}`,
            type: 'manual',
            size: '2.5 MB',
            date: new Date().toISOString(),
            status: 'completed',
            includes: getIncludedData(),
            location: backupSettings.backupLocation as 'local' | 'cloud'
          }
          
          setBackupRecords(prev => [newBackup, ...prev])
          
          notifications.show({
            title: 'Backup Complete',
            message: 'Your data has been backed up successfully!',
            color: 'green',
            icon: <IconCheck size={16} />
          })
          
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  const getIncludedData = () => {
    const included = []
    if (backupSettings.includeVehicles) included.push('Vehicles')
    if (backupSettings.includeCustomers) included.push('Customers')
    if (backupSettings.includeReservations) included.push('Reservations')
    if (backupSettings.includeContracts) included.push('Contracts')
    if (backupSettings.includeSettings) included.push('Settings')
    if (backupSettings.includeReports) included.push('Reports')
    return included
  }

  const handleDownloadBackup = (backup: BackupRecord) => {
    // Create backup data
    const backupData = {
      metadata: {
        name: backup.name,
        date: backup.date,
        version: '1.0.0',
        includes: backup.includes
      },
      data: {
        ...(backupSettings.includeVehicles && { vehicles }),
        ...(backupSettings.includeCustomers && { customers }),
        ...(backupSettings.includeReservations && { reservations }),
        ...(backupSettings.includeContracts && { contracts }),
        ...(backupSettings.includeSettings && { settings })
      }
    }

    // Create and download file
    const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `carvio-backup-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    notifications.show({
      title: 'Download Started',
      message: 'Backup file download has started',
      color: 'blue'
    })
  }

  const handleRestoreBackup = () => {
    if (!selectedBackup && !restoreFile) {
      notifications.show({
        title: 'Error',
        message: 'Please select a backup to restore',
        color: 'red'
      })
      return
    }

    notifications.show({
      title: 'Restore Started',
      message: 'Data restoration is in progress...',
      color: 'blue'
    })

    // Simulate restore process
    setTimeout(() => {
      notifications.show({
        title: 'Restore Complete',
        message: 'Your data has been restored successfully!',
        color: 'green',
        icon: <IconCheck size={16} />
      })
      setRestoreModalOpen(false)
      setSelectedBackup(null)
      setRestoreFile(null)
    }, 2000)
  }

  const handleDeleteBackup = (backup: BackupRecord) => {
    setBackupRecords(prev => prev.filter(b => b.id !== backup.id))
    notifications.show({
      title: 'Backup Deleted',
      message: `${backup.name} has been deleted`,
      color: 'green'
    })
  }

  const getDataSummary = () => {
    return {
      vehicles: vehicles.length,
      customers: customers.length,
      reservations: reservations.length,
      contracts: contracts.length
    }
  }

  const dataSummary = getDataSummary()

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Group gap="sm">
              <IconDatabase size={24} />
              <div>
                <Title order={2}>Backup & Restore</Title>
                <Text c="dimmed">Manage data backups and restore operations</Text>
              </div>
            </Group>
          </div>
          <Group>
            <Button
              variant="light"
              onClick={handleSaveSettings}
              leftSection={<IconDeviceFloppy size={16} />}
            >
              Save Settings
            </Button>
            <Button
              onClick={handleCreateBackup}
              loading={isBackingUp}
              leftSection={<IconDownload size={16} />}
            >
              Create Backup
            </Button>
          </Group>
        </Group>

        {/* Data Summary */}
        <Card withBorder>
          <Title order={4} mb="md">Current Data Summary</Title>
          <Grid>
            <Grid.Col span={3}>
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">Vehicles</Text>
                <Text fw={700} size="xl">{dataSummary.vehicles}</Text>
              </div>
            </Grid.Col>
            <Grid.Col span={3}>
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">Customers</Text>
                <Text fw={700} size="xl">{dataSummary.customers}</Text>
              </div>
            </Grid.Col>
            <Grid.Col span={3}>
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">Reservations</Text>
                <Text fw={700} size="xl">{dataSummary.reservations}</Text>
              </div>
            </Grid.Col>
            <Grid.Col span={3}>
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">Contracts</Text>
                <Text fw={700} size="xl">{dataSummary.contracts}</Text>
              </div>
            </Grid.Col>
          </Grid>
        </Card>

        {/* Backup Progress */}
        {isBackingUp && (
          <Card withBorder>
            <Group justify="space-between" mb="xs">
              <Text fw={500}>Creating Backup...</Text>
              <Text size="sm">{backupProgress}%</Text>
            </Group>
            <Progress value={backupProgress} animated />
          </Card>
        )}

        {/* Backup Settings */}
        <Card withBorder>
          <Title order={4} mb="md">Backup Settings</Title>
          <Grid>
            <Grid.Col span={6}>
              <Switch
                label="Automatic Backup"
                description="Enable automatic backups"
                checked={backupSettings.autoBackup}
                onChange={(event) => setBackupSettings(prev => ({
                  ...prev,
                  autoBackup: event.currentTarget.checked
                }))}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Backup Frequency"
                value={backupSettings.backupFrequency}
                onChange={(value) => setBackupSettings(prev => ({
                  ...prev,
                  backupFrequency: value || 'daily'
                }))}
                data={[
                  { value: 'hourly', label: 'Every Hour' },
                  { value: 'daily', label: 'Daily' },
                  { value: 'weekly', label: 'Weekly' },
                  { value: 'monthly', label: 'Monthly' }
                ]}
                disabled={!backupSettings.autoBackup}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Backup Location"
                value={backupSettings.backupLocation}
                onChange={(value) => setBackupSettings(prev => ({
                  ...prev,
                  backupLocation: value || 'local'
                }))}
                data={[
                  { value: 'local', label: 'Local Storage' },
                  { value: 'cloud', label: 'Cloud Storage' }
                ]}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Retention Period"
                value={backupSettings.retentionDays.toString()}
                onChange={(value) => setBackupSettings(prev => ({
                  ...prev,
                  retentionDays: parseInt(value || '30')
                }))}
                data={[
                  { value: '7', label: '7 Days' },
                  { value: '14', label: '14 Days' },
                  { value: '30', label: '30 Days' },
                  { value: '60', label: '60 Days' },
                  { value: '90', label: '90 Days' }
                ]}
              />
            </Grid.Col>
          </Grid>

          <Divider my="md" label="Data to Include" />

          <Grid>
            <Grid.Col span={4}>
              <Switch
                label="Vehicles"
                checked={backupSettings.includeVehicles}
                onChange={(event) => setBackupSettings(prev => ({
                  ...prev,
                  includeVehicles: event.currentTarget.checked
                }))}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Switch
                label="Customers"
                checked={backupSettings.includeCustomers}
                onChange={(event) => setBackupSettings(prev => ({
                  ...prev,
                  includeCustomers: event.currentTarget.checked
                }))}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Switch
                label="Reservations"
                checked={backupSettings.includeReservations}
                onChange={(event) => setBackupSettings(prev => ({
                  ...prev,
                  includeReservations: event.currentTarget.checked
                }))}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Switch
                label="Contracts"
                checked={backupSettings.includeContracts}
                onChange={(event) => setBackupSettings(prev => ({
                  ...prev,
                  includeContracts: event.currentTarget.checked
                }))}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Switch
                label="Settings"
                checked={backupSettings.includeSettings}
                onChange={(event) => setBackupSettings(prev => ({
                  ...prev,
                  includeSettings: event.currentTarget.checked
                }))}
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Switch
                label="Reports"
                checked={backupSettings.includeReports}
                onChange={(event) => setBackupSettings(prev => ({
                  ...prev,
                  includeReports: event.currentTarget.checked
                }))}
              />
            </Grid.Col>
          </Grid>
        </Card>

        {/* Backup History */}
        <Card withBorder>
          <Group justify="space-between" mb="md">
            <Title order={4}>Backup History</Title>
            <Button
              variant="light"
              onClick={() => setRestoreModalOpen(true)}
              leftSection={<IconRestore size={16} />}
            >
              Restore from File
            </Button>
          </Group>

          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Backup Name</Table.Th>
                <Table.Th>Type</Table.Th>
                <Table.Th>Size</Table.Th>
                <Table.Th>Date</Table.Th>
                <Table.Th>Status</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {backupRecords.map((backup) => (
                <Table.Tr key={backup.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{backup.name}</Text>
                      <Text size="xs" c="dimmed">
                        Includes: {backup.includes.join(', ')}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      variant="light"
                      color={
                        backup.type === 'manual' ? 'blue' :
                        backup.type === 'automatic' ? 'green' : 'orange'
                      }
                    >
                      {backup.type}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{backup.size}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">
                      {new Date(backup.date).toLocaleDateString()} {new Date(backup.date).toLocaleTimeString()}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      variant="light"
                      color={
                        backup.status === 'completed' ? 'green' :
                        backup.status === 'in_progress' ? 'blue' : 'red'
                      }
                    >
                      {backup.status}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon
                        variant="light"
                        size="sm"
                        onClick={() => handleDownloadBackup(backup)}
                        title="Download"
                      >
                        <IconDownload size={14} />
                      </ActionIcon>
                      <ActionIcon
                        variant="light"
                        size="sm"
                        color="blue"
                        onClick={() => {
                          setSelectedBackup(backup)
                          setRestoreModalOpen(true)
                        }}
                        title="Restore"
                      >
                        <IconRestore size={14} />
                      </ActionIcon>
                      <ActionIcon
                        variant="light"
                        size="sm"
                        color="red"
                        onClick={() => handleDeleteBackup(backup)}
                        title="Delete"
                      >
                        <IconTrash size={14} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>

        {/* Restore Modal */}
        <Modal
          opened={restoreModalOpen}
          onClose={() => setRestoreModalOpen(false)}
          title="Restore Data"
          size="md"
          withCloseButton
        >
          <Stack>
            <Alert color="yellow" title="Warning" icon={<IconAlertTriangle size={16} />}>
              Restoring data will overwrite your current data. This action cannot be undone.
              Please make sure you have a recent backup before proceeding.
            </Alert>

            {selectedBackup ? (
              <Card withBorder>
                <Text fw={500} mb="xs">Selected Backup:</Text>
                <Text size="sm">{selectedBackup.name}</Text>
                <Text size="xs" c="dimmed">
                  Created: {new Date(selectedBackup.date).toLocaleString()}
                </Text>
                <Text size="xs" c="dimmed">
                  Includes: {selectedBackup.includes.join(', ')}
                </Text>
              </Card>
            ) : (
              <FileInput
                label="Upload Backup File"
                placeholder="Select backup file"
                accept=".json"
                value={restoreFile}
                onChange={setRestoreFile}
                leftSection={<IconUpload size={16} />}
              />
            )}

            <List
              spacing="xs"
              size="sm"
              center
              icon={
                <ThemeIcon color="red" size={16} radius="xl">
                  <IconAlertTriangle size={12} />
                </ThemeIcon>
              }
            >
              <List.Item>All current data will be replaced</List.Item>
              <List.Item>This action cannot be undone</List.Item>
              <List.Item>The application will restart after restore</List.Item>
            </List>

            <Group justify="flex-end">
              <Button
                variant="light"
                onClick={() => {
                  setRestoreModalOpen(false)
                  setSelectedBackup(null)
                  setRestoreFile(null)
                }}
              >
                Cancel
              </Button>
              <Button
                color="red"
                onClick={handleRestoreBackup}
                leftSection={<IconRestore size={16} />}
              >
                Restore Data
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}
