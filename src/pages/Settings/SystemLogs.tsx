import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Card,
  Group,
  Button,
  Stack,
  Grid,
  Select,
  TextInput,
  Table,
  Badge,
  ActionIcon,
  Modal,
  ScrollArea,
  Code,
  Tabs,
  Alert,
  Pagination,
  Checkbox
} from '@mantine/core'
import {
  IconFileText,
  IconDownload,
  IconRefresh,
  IconTrash,
  IconEye,
  IconSearch,
  IconFilter,
  IconAlertTriangle,
  IconInfoCircle,
  IconBug,
  IconShield,
  IconActivity,
  IconClock
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'
import { useAppStore } from '../../store/useAppStore'

interface LogEntry {
  id: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'debug' | 'security'
  category: 'system' | 'user' | 'api' | 'database' | 'security' | 'backup'
  message: string
  details?: string
  user?: string
  ip?: string
  userAgent?: string
  module: string
}

export function SystemLogs() {
  const { t } = useTranslation()
  const { currentUser } = useAppStore()

  const [logs, setLogs] = useState<LogEntry[]>([
    {
      id: '1',
      timestamp: '2024-01-15 14:30:25',
      level: 'info',
      category: 'user',
      message: 'User logged in successfully',
      user: '<EMAIL>',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
      module: 'Authentication'
    },
    {
      id: '2',
      timestamp: '2024-01-15 14:25:12',
      level: 'info',
      category: 'system',
      message: 'Vehicle added to fleet',
      details: 'Vehicle ID: VEH-001, Make: Toyota, Model: Camry',
      user: '<EMAIL>',
      module: 'Fleet Management'
    },
    {
      id: '3',
      timestamp: '2024-01-15 14:20:45',
      level: 'warning',
      category: 'system',
      message: 'Low fuel level detected',
      details: 'Vehicle ABC-123 fuel level: 15%',
      module: 'Vehicle Monitoring'
    },
    {
      id: '4',
      timestamp: '2024-01-15 14:15:33',
      level: 'error',
      category: 'api',
      message: 'Payment processing failed',
      details: 'Error: Connection timeout to payment gateway',
      user: '<EMAIL>',
      module: 'Payment Processing'
    },
    {
      id: '5',
      timestamp: '2024-01-15 14:10:18',
      level: 'security',
      category: 'security',
      message: 'Failed login attempt',
      details: 'Multiple failed login attempts detected',
      ip: '*************',
      userAgent: 'Unknown',
      module: 'Security'
    },
    {
      id: '6',
      timestamp: '2024-01-15 14:05:07',
      level: 'info',
      category: 'backup',
      message: 'Automatic backup completed',
      details: 'Backup size: 2.4MB, Duration: 45 seconds',
      module: 'Backup System'
    },
    {
      id: '7',
      timestamp: '2024-01-15 14:00:00',
      level: 'debug',
      category: 'database',
      message: 'Database query executed',
      details: 'SELECT * FROM vehicles WHERE status = "available"',
      module: 'Database'
    }
  ])

  const [filters, setFilters] = useState({
    level: '',
    category: '',
    module: '',
    search: '',
    dateFrom: '',
    dateTo: ''
  })

  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null)
  const [logModalOpen, setLogModalOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedLogs, setSelectedLogs] = useState<string[]>([])
  const itemsPerPage = 10

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'red'
      case 'warning': return 'yellow'
      case 'security': return 'purple'
      case 'debug': return 'gray'
      default: return 'blue'
    }
  }

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return <IconBug size={14} />
      case 'warning': return <IconAlertTriangle size={14} />
      case 'security': return <IconShield size={14} />
      case 'debug': return <IconInfoCircle size={14} />
      default: return <IconInfoCircle size={14} />
    }
  }

  const filteredLogs = logs.filter(log => {
    if (filters.level && log.level !== filters.level) return false
    if (filters.category && log.category !== filters.category) return false
    if (filters.module && log.module !== filters.module) return false
    if (filters.search && !log.message.toLowerCase().includes(filters.search.toLowerCase())) return false
    return true
  })

  const paginatedLogs = filteredLogs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const handleViewLog = (log: LogEntry) => {
    setSelectedLog(log)
    setLogModalOpen(true)
  }

  const handleExportLogs = () => {
    const exportData = filteredLogs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      category: log.category,
      module: log.module,
      message: log.message,
      details: log.details || '',
      user: log.user || '',
      ip: log.ip || ''
    }))

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `carvio-logs-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    notifications.show({
      title: 'Export Complete',
      message: 'System logs have been exported successfully',
      color: 'green'
    })
  }

  const handleClearLogs = () => {
    if (selectedLogs.length > 0) {
      setLogs(prev => prev.filter(log => !selectedLogs.includes(log.id)))
      setSelectedLogs([])
      notifications.show({
        title: 'Logs Cleared',
        message: `${selectedLogs.length} log entries have been deleted`,
        color: 'green'
      })
    } else {
      setLogs([])
      notifications.show({
        title: 'All Logs Cleared',
        message: 'All log entries have been deleted',
        color: 'green'
      })
    }
  }

  const handleRefreshLogs = () => {
    // Simulate adding new log entry
    const newLog: LogEntry = {
      id: `log-${Date.now()}`,
      timestamp: new Date().toLocaleString(),
      level: 'info',
      category: 'system',
      message: 'System logs refreshed',
      user: currentUser?.email || 'system',
      module: 'System Logs'
    }
    
    setLogs(prev => [newLog, ...prev])
    notifications.show({
      title: 'Logs Refreshed',
      message: 'System logs have been updated',
      color: 'blue'
    })
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedLogs(paginatedLogs.map(log => log.id))
    } else {
      setSelectedLogs([])
    }
  }

  const handleSelectLog = (logId: string, checked: boolean) => {
    if (checked) {
      setSelectedLogs(prev => [...prev, logId])
    } else {
      setSelectedLogs(prev => prev.filter(id => id !== logId))
    }
  }

  const getLogStats = () => {
    const stats = {
      total: logs.length,
      errors: logs.filter(l => l.level === 'error').length,
      warnings: logs.filter(l => l.level === 'warning').length,
      security: logs.filter(l => l.level === 'security').length,
      today: logs.filter(l => l.timestamp.startsWith(new Date().toISOString().split('T')[0])).length
    }
    return stats
  }

  const stats = getLogStats()

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Group gap="sm">
              <IconFileText size={24} />
              <div>
                <Title order={2}>System Logs</Title>
                <Text c="dimmed">View system activity, errors, and audit trails</Text>
              </div>
            </Group>
          </div>
          <Group>
            <Button
              variant="light"
              onClick={handleRefreshLogs}
              leftSection={<IconRefresh size={16} />}
            >
              Refresh
            </Button>
            <Button
              variant="light"
              onClick={handleExportLogs}
              leftSection={<IconDownload size={16} />}
            >
              Export
            </Button>
            <Button
              color="red"
              variant="light"
              onClick={handleClearLogs}
              leftSection={<IconTrash size={16} />}
            >
              Clear {selectedLogs.length > 0 ? `(${selectedLogs.length})` : 'All'}
            </Button>
          </Group>
        </Group>

        {/* Statistics */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 2.4 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Total Logs</Text>
              <Text fw={700} size="xl">{stats.total}</Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 2.4 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Errors</Text>
              <Text fw={700} size="xl" c="red">{stats.errors}</Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 2.4 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Warnings</Text>
              <Text fw={700} size="xl" c="yellow">{stats.warnings}</Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 2.4 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Security</Text>
              <Text fw={700} size="xl" c="purple">{stats.security}</Text>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 2.4 }}>
            <Card withBorder>
              <Text size="xs" tt="uppercase" fw={700} c="dimmed">Today</Text>
              <Text fw={700} size="xl">{stats.today}</Text>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Filters */}
        <Card withBorder>
          <Group gap="md" mb="md">
            <IconFilter size={20} />
            <Title order={4}>Filters</Title>
          </Group>
          <Grid>
            <Grid.Col span={3}>
              <Select
                label="Level"
                placeholder="All levels"
                value={filters.level}
                onChange={(value) => setFilters(prev => ({ ...prev, level: value || '' }))}
                data={[
                  { value: '', label: 'All Levels' },
                  { value: 'info', label: 'Info' },
                  { value: 'warning', label: 'Warning' },
                  { value: 'error', label: 'Error' },
                  { value: 'debug', label: 'Debug' },
                  { value: 'security', label: 'Security' }
                ]}
                clearable
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Category"
                placeholder="All categories"
                value={filters.category}
                onChange={(value) => setFilters(prev => ({ ...prev, category: value || '' }))}
                data={[
                  { value: '', label: 'All Categories' },
                  { value: 'system', label: 'System' },
                  { value: 'user', label: 'User' },
                  { value: 'api', label: 'API' },
                  { value: 'database', label: 'Database' },
                  { value: 'security', label: 'Security' },
                  { value: 'backup', label: 'Backup' }
                ]}
                clearable
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <Select
                label="Module"
                placeholder="All modules"
                value={filters.module}
                onChange={(value) => setFilters(prev => ({ ...prev, module: value || '' }))}
                data={[
                  { value: '', label: 'All Modules' },
                  { value: 'Authentication', label: 'Authentication' },
                  { value: 'Fleet Management', label: 'Fleet Management' },
                  { value: 'Payment Processing', label: 'Payment Processing' },
                  { value: 'Vehicle Monitoring', label: 'Vehicle Monitoring' },
                  { value: 'Security', label: 'Security' },
                  { value: 'Backup System', label: 'Backup System' }
                ]}
                clearable
              />
            </Grid.Col>
            <Grid.Col span={3}>
              <TextInput
                label="Search"
                placeholder="Search messages..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                leftSection={<IconSearch size={16} />}
              />
            </Grid.Col>
          </Grid>
        </Card>

        {/* Logs Table */}
        <Card withBorder>
          <Group justify="space-between" mb="md">
            <Title order={4}>Log Entries ({filteredLogs.length})</Title>
            <Checkbox
              label="Select All"
              checked={selectedLogs.length === paginatedLogs.length && paginatedLogs.length > 0}
              indeterminate={selectedLogs.length > 0 && selectedLogs.length < paginatedLogs.length}
              onChange={(event) => handleSelectAll(event.currentTarget.checked)}
            />
          </Group>

          <ScrollArea>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Select</Table.Th>
                  <Table.Th>Timestamp</Table.Th>
                  <Table.Th>Level</Table.Th>
                  <Table.Th>Category</Table.Th>
                  <Table.Th>Module</Table.Th>
                  <Table.Th>Message</Table.Th>
                  <Table.Th>User</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {paginatedLogs.map((log) => (
                  <Table.Tr key={log.id}>
                    <Table.Td>
                      <Checkbox
                        checked={selectedLogs.includes(log.id)}
                        onChange={(event) => handleSelectLog(log.id, event.currentTarget.checked)}
                      />
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <IconClock size={12} />
                        <Text size="xs" style={{ fontFamily: 'monospace' }}>
                          {log.timestamp}
                        </Text>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Badge
                        variant="light"
                        color={getLevelColor(log.level)}
                        leftSection={getLevelIcon(log.level)}
                      >
                        {log.level.toUpperCase()}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="outline" size="sm">
                        {log.category}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{log.module}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" lineClamp={2}>
                        {log.message}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="xs" c="dimmed">
                        {log.user || 'System'}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <ActionIcon
                        variant="light"
                        size="sm"
                        onClick={() => handleViewLog(log)}
                      >
                        <IconEye size={14} />
                      </ActionIcon>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </ScrollArea>

          {filteredLogs.length > itemsPerPage && (
            <Group justify="center" mt="md">
              <Pagination
                value={currentPage}
                onChange={setCurrentPage}
                total={Math.ceil(filteredLogs.length / itemsPerPage)}
                size="sm"
              />
            </Group>
          )}
        </Card>

        {/* Log Detail Modal */}
        <Modal
          opened={logModalOpen}
          onClose={() => setLogModalOpen(false)}
          title="Log Entry Details"
          size="lg"
          withCloseButton
        >
          {selectedLog && (
            <Stack>
              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" fw={500}>Timestamp:</Text>
                  <Code>{selectedLog.timestamp}</Code>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" fw={500}>Level:</Text>
                  <Badge
                    variant="light"
                    color={getLevelColor(selectedLog.level)}
                    leftSection={getLevelIcon(selectedLog.level)}
                  >
                    {selectedLog.level.toUpperCase()}
                  </Badge>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" fw={500}>Category:</Text>
                  <Badge variant="outline">{selectedLog.category}</Badge>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" fw={500}>Module:</Text>
                  <Text size="sm">{selectedLog.module}</Text>
                </Grid.Col>
              </Grid>

              <div>
                <Text size="sm" fw={500} mb="xs">Message:</Text>
                <Code block>{selectedLog.message}</Code>
              </div>

              {selectedLog.details && (
                <div>
                  <Text size="sm" fw={500} mb="xs">Details:</Text>
                  <Code block>{selectedLog.details}</Code>
                </div>
              )}

              {selectedLog.user && (
                <div>
                  <Text size="sm" fw={500} mb="xs">User:</Text>
                  <Text size="sm">{selectedLog.user}</Text>
                </div>
              )}

              {selectedLog.ip && (
                <div>
                  <Text size="sm" fw={500} mb="xs">IP Address:</Text>
                  <Code>{selectedLog.ip}</Code>
                </div>
              )}

              {selectedLog.userAgent && (
                <div>
                  <Text size="sm" fw={500} mb="xs">User Agent:</Text>
                  <Code block>{selectedLog.userAgent}</Code>
                </div>
              )}
            </Stack>
          )}
        </Modal>
      </Stack>
    </Container>
  )
}
