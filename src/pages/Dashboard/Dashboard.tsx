import {
  Grid,
  Card,
  Text,
  Group,
  Stack,
  Button,
  Badge,
  SimpleGrid,
  Title,
  ActionIcon,
  Table,
  Avatar,
  Divider,
  Box,
  Progress
} from '@mantine/core'
import {
  IconCurrencyDollar,
  IconLogin,
  IconChartBar,
  IconTriangle,
  IconCar,
  IconUsers,
  IconCalendar,
  IconTool,
  IconRoad,
  IconCircleCheck,
  IconAlertTriangle
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useAppStore } from '../../store/useAppStore'
import { useEffect, useMemo } from 'react'


export function Dashboard() {
  const { t } = useTranslation()
  const navigate = useNavigate()

  // Get real data from store
  const {
    vehicles,
    reservations,
    contracts,
    categories,
    locations,
    setVehicles,
    setReservations,
    setContracts,
    setCategories,
    setLocations,
    updateStats
  } = useAppStore()

  // Initialize with sample data if empty (for demo purposes)
  useEffect(() => {
    if (vehicles.length === 0) {
      const sampleVehicles = [
        {
          id: '1',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          plate_number: 'ABC-123',
          vin: '1HGBH41JXMN109186',
          category_id: '1',
          color: 'White',
          transmission: 'automatic' as const,
          fuel_type: 'gasoline' as const,
          engine_size: 2.5,
          seating_capacity: 5,
          current_mileage: 15000,
          fuel_level: 85,
          status: 'available' as const,
          daily_rate: 150,
          weekly_rate: 900,
          monthly_rate: 3500,
          security_deposit: 500,
          location_id: '1',
          features: ['GPS', 'Bluetooth', 'AC'],
          notes: 'Well maintained vehicle',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          make: 'BMW',
          model: 'X5',
          year: 2022,
          plate_number: 'XYZ-456',
          vin: '2HGBH41JXMN109187',
          category_id: '2',
          color: 'Black',
          transmission: 'automatic' as const,
          fuel_type: 'gasoline' as const,
          engine_size: 3.0,
          seating_capacity: 7,
          current_mileage: 25000,
          fuel_level: 20, // Low fuel for alert
          status: 'rented' as const,
          daily_rate: 250,
          weekly_rate: 1500,
          monthly_rate: 6000,
          security_deposit: 800,
          location_id: '1',
          features: ['GPS', 'Bluetooth', 'Leather', 'Sunroof'],
          notes: 'Premium SUV',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '3',
          make: 'Mercedes',
          model: 'C-Class',
          year: 2023,
          plate_number: 'MER-789',
          vin: '3HGBH41JXMN109188',
          category_id: '1',
          color: 'Silver',
          transmission: 'automatic' as const,
          fuel_type: 'gasoline' as const,
          engine_size: 2.0,
          seating_capacity: 5,
          current_mileage: 8000,
          fuel_level: 90,
          status: 'maintenance' as const,
          daily_rate: 200,
          weekly_rate: 1200,
          monthly_rate: 4800,
          security_deposit: 600,
          location_id: '1',
          features: ['GPS', 'Bluetooth', 'Premium Audio'],
          notes: 'Scheduled maintenance',
          next_service_due: new Date('2024-02-15'),
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]
      setVehicles(sampleVehicles)
    }

    if (reservations.length === 0) {
      const sampleReservations = [
        {
          id: '1',
          reservation_number: 'RES-2024-001',
          customer_id: 'cust-001',
          vehicle_id: '1',
          pickup_date: new Date('2024-01-20'),
          return_date: new Date('2024-01-25'),
          status: 'confirmed' as const,
          daily_rate: 150,
          total_days: 5,
          subtotal: 750,
          tax_amount: 37.5,
          total_amount: 787.5,
          security_deposit: 500,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          reservation_number: 'RES-2024-002',
          customer_id: 'cust-002',
          vehicle_id: '3',
          pickup_date: new Date('2024-01-22'),
          return_date: new Date('2024-01-27'),
          status: 'pending' as const,
          daily_rate: 200,
          total_days: 5,
          subtotal: 1000,
          tax_amount: 50,
          total_amount: 1050,
          security_deposit: 600,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]
      setReservations(sampleReservations)
    }

    if (contracts.length === 0) {
      const sampleContracts = [
        {
          id: '1',
          contract_number: 'CON-2024-001',
          customer_id: 'cust-003',
          vehicle_id: '2',
          pickup_date: new Date('2024-01-15'),
          return_date: new Date('2024-01-20'),
          status: 'active' as const,
          daily_rate: 250,
          total_days: 5,
          subtotal: 1250,
          tax_amount: 62.5,
          total_amount: 1312.5,
          security_deposit: 800,
          additional_charges: 0,
          refund_amount: 0,
          damage_charges: 0,
          fuel_charges: 0,
          late_return_charges: 0,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]
      setContracts(sampleContracts)
    }

    // Initialize categories if empty
    if (categories.length === 0) {
      const sampleCategories = [
        {
          id: '1',
          name: 'Sedan',
          description: 'Comfortable sedan vehicles',
          daily_rate_base: 120,
          weekly_rate_base: 700,
          monthly_rate_base: 2800,
          security_deposit: 400,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          name: 'SUV',
          description: 'Sport Utility Vehicles',
          daily_rate_base: 250,
          weekly_rate_base: 1500,
          monthly_rate_base: 6000,
          security_deposit: 800,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '3',
          name: 'Luxury',
          description: 'Premium luxury vehicles',
          daily_rate_base: 300,
          weekly_rate_base: 1800,
          monthly_rate_base: 7200,
          security_deposit: 1000,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]
      setCategories(sampleCategories)
    }

    // Initialize locations if empty
    if (locations.length === 0) {
      const sampleLocations = [
        {
          id: '1',
          name: 'Main Branch',
          address: 'Sheikh Zayed Road, Dubai',
          city: 'Dubai',
          country: 'UAE',
          phone: '+971-4-123-4567',
          email: '<EMAIL>',
          is_main_branch: true,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          name: 'Abu Dhabi Branch',
          address: 'Corniche Road, Abu Dhabi',
          city: 'Abu Dhabi',
          country: 'UAE',
          phone: '+971-2-123-4567',
          email: '<EMAIL>',
          is_main_branch: false,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]
      setLocations(sampleLocations)
    }
  }, [vehicles.length, reservations.length, contracts.length, categories.length, locations.length, setVehicles, setReservations, setContracts, setCategories, setLocations])

  // Calculate real statistics from actual data
  const realStats = useMemo(() => {
    const totalVehicles = vehicles.length
    const availableVehicles = vehicles.filter(v => v.status === 'available').length
    const rentedVehicles = vehicles.filter(v => v.status === 'rented').length
    const maintenanceVehicles = vehicles.filter(v => v.status === 'maintenance').length
    const outOfServiceVehicles = vehicles.filter(v => !v.is_active).length

    const activeRentals = contracts.filter(c => c.status === 'active').length
    const pendingReservations = reservations.filter(r => r.status === 'pending').length
    const confirmedReservations = reservations.filter(r => r.status === 'confirmed').length
    const completedContractsList = contracts.filter(c => c.status === 'completed')
    const completedContracts = completedContractsList.length

    // Calculate monthly revenue from active contracts
    const monthlyRevenue = contracts
      .filter(c => c.status === 'active')
      .reduce((sum, contract) => sum + (contract.total_amount || 0), 0)

    // Calculate total revenue from all completed contracts
    const totalRevenue = completedContractsList
      .reduce((sum, contract) => sum + (contract.total_amount || 0), 0)

    // Calculate fleet utilization (rented / total available)
    const fleetUtilization = totalVehicles > 0 ? Math.round((rentedVehicles / totalVehicles) * 100) : 0

    // Calculate average rental duration from completed contracts
    const avgRentalDuration = completedContracts > 0
      ? completedContractsList.reduce((sum, contract) => sum + (contract.total_days || 0), 0) / completedContracts
      : 0

    // Calculate return rate (completed vs total contracts)
    const totalContracts = contracts.length
    const returnRate = totalContracts > 0 ? Math.round((completedContracts / totalContracts) * 100) : 100

    // Calculate revenue target progress (current vs target of $50,000)
    const revenueTarget = 50000
    const revenueProgress = Math.round((monthlyRevenue / revenueTarget) * 100)

    // Calculate customer satisfaction based on return rate and contract completion
    const customerSatisfaction = Math.min(95, Math.round((returnRate + fleetUtilization) / 2))

    return {
      totalVehicles,
      availableVehicles,
      rentedVehicles,
      maintenanceVehicles,
      outOfServiceVehicles,
      activeRentals,
      pendingReservations: pendingReservations + confirmedReservations,
      monthlyRevenue,
      totalRevenue,
      fleetUtilization,
      avgRentalDuration,
      returnRate,
      revenueProgress,
      completedContracts,
      customerSatisfaction
    }
  }, [vehicles, reservations, contracts])

  // Update store stats when calculated stats change
  useEffect(() => {
    updateStats({
      totalVehicles: realStats.totalVehicles,
      availableVehicles: realStats.availableVehicles,
      activeRentals: realStats.activeRentals,
      monthlyRevenue: realStats.monthlyRevenue,
      pendingReservations: realStats.pendingReservations,
      overdueReturns: 0, // TODO: Calculate from contracts
      maintenanceDue: realStats.maintenanceVehicles
    })
  }, [realStats, updateStats])

  // Calculate real change percentages (simplified calculation)
  const calculateChange = (current: number, baseline: number) => {
    if (baseline === 0) return { change: '+0%', type: 'neutral' }
    const percentage = Math.round(((current - baseline) / baseline) * 100)
    return {
      change: `${percentage >= 0 ? '+' : ''}${percentage}%`,
      type: percentage >= 0 ? 'positive' : 'negative'
    }
  }

  // Dashboard stats with real data and calculated changes
  const stats = [
    {
      title: t('totalVehicles'),
      value: realStats.totalVehicles.toString(),
      icon: IconCar,
      color: 'blue',
      ...calculateChange(realStats.totalVehicles, 10) // Baseline of 10 vehicles
    },
    {
      title: t('activeRentalsCount'),
      value: realStats.activeRentals.toString(),
      icon: IconUsers,
      color: 'green',
      ...calculateChange(realStats.activeRentals, 5) // Baseline of 5 rentals
    },
    {
      title: t('monthlyRevenue'),
      value: `$${realStats.monthlyRevenue.toLocaleString()}`,
      icon: IconCurrencyDollar,
      color: 'yellow',
      ...calculateChange(realStats.monthlyRevenue, 30000) // Baseline of $30k
    },
    {
      title: t('pendingReservations'),
      value: realStats.pendingReservations.toString(),
      icon: IconCalendar,
      color: 'orange',
      ...calculateChange(realStats.pendingReservations, 8) // Baseline of 8 reservations
    }
  ]

  const quickActions = [
    {
      title: t('manageVehicles'),
      icon: IconCar,
      color: 'blue',
      path: '/fleet/vehicles'
    },
    {
      title: t('createReservation'),
      icon: IconCalendar,
      color: 'green',
      path: '/reservations/new'
    },
    {
      title: t('checkInVehicle'),
      icon: IconLogin,
      color: 'orange',
      path: '/operations/check-in'
    },
    {
      title: t('generateReport'),
      icon: IconChartBar,
      color: 'purple',
      path: '/reports/dashboard'
    }
  ]

  // Generate recent activity from real data
  const recentActivity = useMemo(() => {
    const activities = []

    // Add recent reservations
    const recentReservations = reservations
      .filter(r => r.status === 'confirmed' || r.status === 'pending')
      .slice(0, 2)
      .map(r => {
        // Find vehicle info
        const vehicle = vehicles.find(v => v.id === r.vehicle_id)
        const location = locations.find(l => l.id === vehicle?.location_id) || locations[0]
        return {
          id: r.id,
          type: 'reservation',
          customer: `Customer ${r.customer_id.slice(0, 8)}...`,
          vehicle: vehicle ? `${vehicle.make} ${vehicle.model}` : 'Unknown Vehicle',
          plateNumber: vehicle?.plate_number || 'N/A',
          action: `Reservation ${r.status}`,
          time: new Date(r.created_at).toLocaleDateString(),
          timeDetail: new Date(r.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          status: r.status,
          amount: r.total_amount || 0,
          location: location?.name || 'Unknown Location'
        }
      })

    // Add recent contracts
    const recentContracts = contracts
      .filter(c => c.status === 'active')
      .slice(0, 2)
      .map(c => {
        // Find vehicle info
        const vehicle = vehicles.find(v => v.id === c.vehicle_id)
        const location = locations.find(l => l.id === vehicle?.location_id) || locations[0]
        return {
          id: c.id,
          type: 'rental',
          customer: `Customer ${c.customer_id.slice(0, 8)}...`,
          vehicle: vehicle ? `${vehicle.make} ${vehicle.model}` : 'Unknown Vehicle',
          plateNumber: vehicle?.plate_number || 'N/A',
          action: 'Vehicle checked out',
          time: new Date(c.created_at).toLocaleDateString(),
          timeDetail: new Date(c.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          status: 'active',
          amount: c.total_amount || 0,
          location: location?.name || 'Unknown Location'
        }
      })

    activities.push(...recentReservations, ...recentContracts)
    return activities.slice(0, 5) // Show 5 most recent activities
  }, [reservations, contracts, vehicles, locations])

  // Generate upcoming maintenance from real data
  const upcomingMaintenance = useMemo(() => {
    return vehicles
      .filter(v => v.next_service_due)
      .slice(0, 2)
      .map(v => ({
        vehicle: `${v.make} ${v.model} ${v.year}`,
        type: 'Scheduled Service',
        dueDate: v.next_service_due ? new Date(v.next_service_due).toLocaleDateString() : 'TBD',
        mileage: `${v.current_mileage.toLocaleString()} km`
      }))
  }, [vehicles])

  // Generate real alerts from data
  const alerts = useMemo(() => {
    const alertList = []

    // Low fuel alerts
    const lowFuelVehicles = vehicles.filter(v => v.fuel_level < 25).length
    if (lowFuelVehicles > 0) {
      alertList.push({
        type: 'fuel',
        message: `${lowFuelVehicles} vehicles with low fuel`,
        severity: lowFuelVehicles > 5 ? 'high' : 'medium'
      })
    }

    // Maintenance due alerts
    const maintenanceDue = vehicles.filter(v => v.status === 'maintenance').length
    if (maintenanceDue > 0) {
      alertList.push({
        type: 'maintenance',
        message: `${maintenanceDue} vehicles due for maintenance`,
        severity: maintenanceDue > 3 ? 'high' : 'low'
      })
    }

    // Overdue returns (contracts past return date)
    const overdueContracts = contracts.filter(c =>
      c.status === 'active' &&
      c.return_date &&
      new Date(c.return_date) < new Date()
    ).length

    if (overdueContracts > 0) {
      alertList.push({
        type: 'overdue',
        message: `${overdueContracts} vehicles overdue for return`,
        severity: 'high'
      })
    }

    return alertList.slice(0, 3) // Show max 3 alerts
  }, [vehicles, contracts])

  return (
    <Stack gap="lg">


      {/* Stats Cards */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md">
        {stats.map((stat, index) => (
          <Card
            key={index}
            padding="md"
            radius="lg"
            withBorder
            className="card-hover"
            style={{
              minHeight: '110px',
              border: '1px solid var(--mantine-color-gray-3)'
            }}
          >
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <Text c="dimmed" size="sm" mb="xs">
                {stat.title}
              </Text>
              <Text fw={700} size="2xl" mb="xs">
                {stat.value}
              </Text>
              <Text size="sm" c={stat.type === 'positive' ? 'green' : stat.type === 'negative' ? 'red' : 'gray'}>
                {stat.change}
              </Text>
            </div>
          </Card>
        ))}
      </SimpleGrid>

      <Grid>
        {/* Quick Actions */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card padding="lg" radius="md" withBorder h="100%">
            <Title order={4} mb="md">{t('quickActions')}</Title>
            <SimpleGrid cols={2} spacing="md">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="light"
                  color={action.color}
                  leftSection={<action.icon size={18} />}
                  onClick={() => {
                    try {
                      navigate(action.path)
                    } catch (error) {
                      console.error('Navigation error:', error)
                    }
                  }}
                  fullWidth
                  h={60}
                >
                  {action.title}
                </Button>
              ))}
            </SimpleGrid>
          </Card>
        </Grid.Col>

        {/* System Alerts */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card padding="lg" radius="md" withBorder h="100%">
            <Title order={4} mb="md">{t('systemAlerts')}</Title>
            <Stack gap="sm">
              {alerts.map((alert, index) => (
                <Group key={index} justify="space-between" p="sm" style={{ 
                  backgroundColor: 'var(--mantine-color-gray-0)',
                  borderRadius: '8px'
                }}>
                  <Group>
                    <IconTriangle 
                      size={16} 
                      color={
                        alert.severity === 'high' ? 'red' : 
                        alert.severity === 'medium' ? 'orange' : 'blue'
                      } 
                    />
                    <Text size="sm">{alert.message}</Text>
                  </Group>
                  <Badge 
                    size="xs" 
                    color={
                      alert.severity === 'high' ? 'red' : 
                      alert.severity === 'medium' ? 'orange' : 'blue'
                    }
                  >
                    {alert.severity}
                  </Badge>
                </Group>
              ))}
            </Stack>
          </Card>
        </Grid.Col>

        {/* Expanded Recent Activity */}
        <Grid.Col span={12}>
          <Card padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="md">
              <Title order={4}>{t('recentActivity')}</Title>
              <Button variant="light" size="xs">View All</Button>
            </Group>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Customer</Table.Th>
                  <Table.Th>Vehicle</Table.Th>
                  <Table.Th>Action</Table.Th>
                  <Table.Th>Date & Time</Table.Th>
                  <Table.Th>Status</Table.Th>
                  <Table.Th>Amount</Table.Th>
                  <Table.Th>Location</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {recentActivity.map((activity) => (
                  <Table.Tr key={activity.id}>
                    <Table.Td>
                      <Group gap="sm">
                        <Avatar size="sm" color="blue">
                          {activity.customer.charAt(0)}
                        </Avatar>
                        <div>
                          <Text size="sm" fw={500}>{activity.customer}</Text>
                          <Text size="xs" c="dimmed">ID: {activity.id.slice(0, 8)}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm" fw={500}>{activity.vehicle}</Text>
                        <Text size="xs" c="dimmed">Plate: {activity.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <IconCar size={14} color="blue" />
                        <Text size="sm">{activity.action}</Text>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{activity.time}</Text>
                        <Text size="xs" c="dimmed">{activity.timeDetail}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge
                        size="sm"
                        color={
                          activity.status === 'active' ? 'green' :
                          activity.status === 'completed' ? 'blue' : 'orange'
                        }
                      >
                        {activity.status}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" fw={500}>${activity.amount.toLocaleString()}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" c="dimmed">{activity.location}</Text>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Second Row - Three Sections */}
      <Grid>
        {/* Upcoming Maintenance */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card padding="lg" radius="md" withBorder h="100%">
            <Group justify="space-between" mb="md">
              <Title order={4}>{t('upcomingMaintenance')}</Title>
              <ActionIcon variant="light" size="sm">
                <IconTool size={16} />
              </ActionIcon>
            </Group>
            <Stack gap="md">
              {upcomingMaintenance.map((maintenance, index) => (
                <Box key={index}>
                  <Group justify="space-between" mb="xs">
                    <Text size="sm" fw={500}>{maintenance.vehicle}</Text>
                    <Badge size="xs" color="orange">{maintenance.type}</Badge>
                  </Group>
                  <Group gap="xs" mb="xs">
                    <IconCalendar size={12} color="gray" />
                    <Text size="xs" c="dimmed">Due: {maintenance.dueDate}</Text>
                  </Group>
                  <Group gap="xs">
                    <IconRoad size={12} color="gray" />
                    <Text size="xs" c="dimmed">{maintenance.mileage}</Text>
                  </Group>
                  {index < upcomingMaintenance.length - 1 && <Divider mt="md" />}
                </Box>
              ))}
              <Button variant="light" size="xs" fullWidth mt="sm">
                Schedule Maintenance
              </Button>
            </Stack>
          </Card>
        </Grid.Col>

        {/* Fleet Status Overview */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card padding="lg" radius="md" withBorder h="100%">
            <Group justify="space-between" mb="md">
              <Title order={4}>Fleet Status</Title>
              <ActionIcon variant="light" size="sm">
                <IconCar size={16} />
              </ActionIcon>
            </Group>
            <Stack gap="md">
              <Group justify="space-between" p="sm" style={{ backgroundColor: 'var(--mantine-color-green-0)', borderRadius: '8px' }}>
                <Group gap="xs">
                  <IconCircleCheck size={16} color="green" />
                  <Text size="sm" fw={500}>Available</Text>
                </Group>
                <Text size="sm" fw={600}>{realStats.availableVehicles}</Text>
              </Group>
              <Group justify="space-between" p="sm" style={{ backgroundColor: 'var(--mantine-color-blue-0)', borderRadius: '8px' }}>
                <Group gap="xs">
                  <IconCar size={16} color="blue" />
                  <Text size="sm" fw={500}>Rented</Text>
                </Group>
                <Text size="sm" fw={600}>{realStats.rentedVehicles}</Text>
              </Group>
              <Group justify="space-between" p="sm" style={{ backgroundColor: 'var(--mantine-color-orange-0)', borderRadius: '8px' }}>
                <Group gap="xs">
                  <IconTool size={16} color="orange" />
                  <Text size="sm" fw={500}>Maintenance</Text>
                </Group>
                <Text size="sm" fw={600}>{realStats.maintenanceVehicles}</Text>
              </Group>
              <Group justify="space-between" p="sm" style={{ backgroundColor: 'var(--mantine-color-red-0)', borderRadius: '8px' }}>
                <Group gap="xs">
                  <IconAlertTriangle size={16} color="red" />
                  <Text size="sm" fw={500}>Out of Service</Text>
                </Group>
                <Text size="sm" fw={600}>{realStats.outOfServiceVehicles}</Text>
              </Group>
            </Stack>
          </Card>
        </Grid.Col>

        {/* Performance Metrics */}
        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card padding="lg" radius="md" withBorder h="100%">
            <Group justify="space-between" mb="md">
              <Title order={4}>Performance Metrics</Title>
              <ActionIcon variant="light" size="sm">
                <IconChartBar size={16} />
              </ActionIcon>
            </Group>
            <Stack gap="md">
              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm">Fleet Utilization</Text>
                  <Text size="sm" fw={600}>{realStats.fleetUtilization}%</Text>
                </Group>
                <Progress value={realStats.fleetUtilization} color="blue" size="sm" />
              </div>
              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm">Revenue Target</Text>
                  <Text size="sm" fw={600}>{realStats.revenueProgress}%</Text>
                </Group>
                <Progress value={realStats.revenueProgress} color="green" size="sm" />
              </div>
              <div>
                <Group justify="space-between" mb="xs">
                  <Text size="sm">Customer Satisfaction</Text>
                  <Text size="sm" fw={600}>{realStats.customerSatisfaction}%</Text>
                </Group>
                <Progress value={realStats.customerSatisfaction} color="yellow" size="sm" />
              </div>
              <Divider />
              <Group justify="space-between">
                <Text size="xs" c="dimmed">Avg. Rental Duration</Text>
                <Text size="xs" fw={500}>{realStats.avgRentalDuration.toFixed(1)} days</Text>
              </Group>
              <Group justify="space-between">
                <Text size="xs" c="dimmed">Return Rate</Text>
                <Text size="xs" fw={500}>{realStats.returnRate}%</Text>
              </Group>
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>
    </Stack>
  )
}
