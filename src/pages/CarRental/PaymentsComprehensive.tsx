import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Center,
  Progress,
  Alert,
  RingProgress
} from '@mantine/core'
import {
  IconCalendar,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconTrash,
  IconCreditCard,
  IconCurrencyDollar,
  IconReceipt,
  IconAlertTriangle,
  IconBan,
  IconRefresh,
  IconCalendarEvent,
  IconTrendingUp,
  IconWallet
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface PaymentTableData {
  id: string
  payment_number: string
  reservation_id: string
  reservation_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  vehicle_name: string
  plate_number: string
  payment_type: 'deposit' | 'partial' | 'full' | 'refund' | 'penalty'
  payment_method: 'cash' | 'card' | 'bank_transfer' | 'online' | 'cheque'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded'
  amount: number
  currency: string
  transaction_id?: string
  gateway_reference?: string
  payment_date: string
  due_date?: string
  description: string
  notes: string
  processed_by: string
  created_at: string
  updated_at: string
}

export function PaymentsComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [payments, setPayments] = useState<PaymentTableData[]>([])
  const [activeTab, setActiveTab] = useState('all-payments')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [methodFilter, setMethodFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [refundModalOpen, setRefundModalOpen] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<PaymentTableData | null>(null)

  // Mock data for development - comprehensive payment structure
  const mockPayments: PaymentTableData[] = [
    {
      id: '1',
      payment_number: 'PAY-2024-001',
      reservation_id: '1',
      reservation_number: 'RES-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971501234567',
      vehicle_name: 'Toyota Camry 2023',
      plate_number: 'ABC-123',
      payment_type: 'deposit',
      payment_method: 'card',
      status: 'completed',
      amount: 200,
      currency: 'AED',
      transaction_id: 'TXN-001-2024',
      gateway_reference: 'GTW-REF-12345',
      payment_date: '2024-01-20T10:30:00Z',
      due_date: '2024-01-20T23:59:59Z',
      description: 'Security deposit for Toyota Camry rental',
      notes: 'Payment processed successfully via Visa card',
      processed_by: 'admin',
      created_at: '2024-01-20T10:00:00Z',
      updated_at: '2024-01-20T10:30:00Z'
    },
    {
      id: '2',
      payment_number: 'PAY-2024-002',
      reservation_id: '2',
      reservation_number: 'RES-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+************',
      vehicle_name: 'BMW X5 2022',
      plate_number: 'XYZ-456',
      payment_type: 'full',
      payment_method: 'bank_transfer',
      status: 'completed',
      amount: 1050,
      currency: 'AED',
      transaction_id: 'TXN-002-2024',
      payment_date: '2024-01-19T14:15:00Z',
      description: 'Full payment for BMW X5 rental',
      notes: 'Bank transfer received and verified',
      processed_by: 'admin',
      created_at: '2024-01-19T09:00:00Z',
      updated_at: '2024-01-19T14:15:00Z'
    },
    {
      id: '3',
      payment_number: 'PAY-2024-003',
      reservation_id: '3',
      reservation_number: 'RES-2024-003',
      customer_id: '3',
      customer_name: 'Mohammed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+************',
      vehicle_name: 'Mercedes C-Class 2023',
      plate_number: 'DEF-789',
      payment_type: 'partial',
      payment_method: 'online',
      status: 'pending',
      amount: 300,
      currency: 'AED',
      payment_date: '2024-01-22T16:00:00Z',
      due_date: '2024-01-25T23:59:59Z',
      description: 'Partial payment for Mercedes C-Class rental',
      notes: 'Awaiting online payment confirmation',
      processed_by: 'system',
      created_at: '2024-01-22T16:00:00Z',
      updated_at: '2024-01-22T16:00:00Z'
    },
    {
      id: '4',
      payment_number: 'PAY-2024-004',
      reservation_id: '4',
      reservation_number: 'RES-2024-004',
      customer_id: '4',
      customer_name: 'Fatima Al-Zahra',
      customer_email: '<EMAIL>',
      customer_phone: '+971502345678',
      vehicle_name: 'Nissan Altima 2023',
      plate_number: 'GHI-012',
      payment_type: 'refund',
      payment_method: 'card',
      status: 'processing',
      amount: 100,
      currency: 'AED',
      transaction_id: 'TXN-004-2024',
      payment_date: '2024-01-16T11:00:00Z',
      description: 'Refund for early return - Nissan Altima',
      notes: 'Refund processing to original payment method',
      processed_by: 'admin',
      created_at: '2024-01-16T10:30:00Z',
      updated_at: '2024-01-16T11:00:00Z'
    },
    {
      id: '5',
      payment_number: 'PAY-2024-005',
      reservation_id: '5',
      reservation_number: 'RES-2024-005',
      customer_id: '5',
      customer_name: 'Omar Al-Mansoori',
      customer_email: '<EMAIL>',
      customer_phone: '+971503456789',
      vehicle_name: 'Audi A4 2023',
      plate_number: 'JKL-345',
      payment_type: 'penalty',
      payment_method: 'cash',
      status: 'failed',
      amount: 150,
      currency: 'AED',
      payment_date: '2024-01-18T09:00:00Z',
      due_date: '2024-01-20T23:59:59Z',
      description: 'Late return penalty for Audi A4',
      notes: 'Payment failed - insufficient funds',
      processed_by: 'admin',
      created_at: '2024-01-18T09:00:00Z',
      updated_at: '2024-01-18T09:30:00Z'
    }
  ]

  useEffect(() => {
    loadPayments()
  }, [])

  const loadPayments = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await paymentService.getAllPayments()
      setPayments(mockPayments)
    } catch (error) {
      console.error('Error loading payments:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredPayments = payments.filter(payment => {
    const matchesSearch = !searchQuery || 
      payment.payment_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.reservation_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.vehicle_name.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || payment.status === statusFilter
    const matchesMethod = !methodFilter || payment.payment_method === methodFilter
    const matchesType = !typeFilter || payment.payment_type === typeFilter
    
    return matchesSearch && matchesStatus && matchesMethod && matchesType
  })

  // Pagination
  const totalItems = filteredPayments.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedPayments = filteredPayments.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const totalRevenue = payments.filter(p => p.status === 'completed' && p.payment_type !== 'refund').reduce((sum, p) => sum + p.amount, 0)
  const pendingAmount = payments.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0)
  const refundAmount = payments.filter(p => p.payment_type === 'refund' && p.status === 'completed').reduce((sum, p) => sum + p.amount, 0)
  
  const stats = [
    {
      label: t('totalPayments'),
      value: payments.length.toString(),
      color: 'blue',
      icon: IconReceipt
    },
    {
      label: t('totalRevenue'),
      value: `$${totalRevenue.toFixed(2)}`,
      color: 'green',
      icon: IconTrendingUp
    },
    {
      label: t('pendingPayments'),
      value: `$${pendingAmount.toFixed(2)}`,
      color: 'orange',
      icon: IconClock
    },
    {
      label: t('successRate'),
      value: `${Math.round((payments.filter(p => p.status === 'completed').length / payments.length) * 100)}%`,
      color: 'teal',
      icon: IconCheck
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'processing': return 'blue'
      case 'completed': return 'green'
      case 'failed': return 'red'
      case 'cancelled': return 'gray'
      case 'refunded': return 'cyan'
      default: return 'gray'
    }
  }

  const getPaymentTypeColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'blue'
      case 'partial': return 'yellow'
      case 'full': return 'green'
      case 'refund': return 'cyan'
      case 'penalty': return 'red'
      default: return 'gray'
    }
  }

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'card': return IconCreditCard
      case 'cash': return IconWallet
      case 'bank_transfer': return IconReceipt
      case 'online': return IconCurrencyDollar
      case 'cheque': return IconReceipt
      default: return IconCurrencyDollar
    }
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('paymentsManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageAllPaymentsAndTransactions')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setAddModalOpen(true)}
              >
                {t('recordPayment')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-payments">{t('allPayments')}</Tabs.Tab>
            <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-payments">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder={t('searchPayments')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'pending', label: t('pending') },
                      { value: 'processing', label: t('processing') },
                      { value: 'completed', label: t('completed') },
                      { value: 'failed', label: t('failed') },
                      { value: 'cancelled', label: t('cancelled') },
                      { value: 'refunded', label: t('refunded') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('method')}
                    leftSection={<IconCreditCard size={16} />}
                    value={methodFilter}
                    onChange={(value) => setMethodFilter(value || '')}
                    data={[
                      { value: '', label: t('allMethods') },
                      { value: 'cash', label: t('cash') },
                      { value: 'card', label: t('card') },
                      { value: 'bank_transfer', label: t('bankTransfer') },
                      { value: 'online', label: t('online') },
                      { value: 'cheque', label: t('cheque') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('type')}
                    leftSection={<IconReceipt size={16} />}
                    value={typeFilter}
                    onChange={(value) => setTypeFilter(value || '')}
                    data={[
                      { value: '', label: t('allTypes') },
                      { value: 'deposit', label: t('deposit') },
                      { value: 'partial', label: t('partial') },
                      { value: 'full', label: t('full') },
                      { value: 'refund', label: t('refund') },
                      { value: 'penalty', label: t('penalty') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setMethodFilter('')
                      setTypeFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Payments Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('payment')}</Table.Th>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('reservation')}</Table.Th>
                    <Table.Th>{t('type')}</Table.Th>
                    <Table.Th>{t('method')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('amount')}</Table.Th>
                    <Table.Th>{t('date')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedPayments.map((payment) => {
                    const MethodIcon = getMethodIcon(payment.payment_method)
                    return (
                      <Table.Tr key={payment.id}>
                        <Table.Td>
                          <div>
                            <Text fw={700} size="sm">{payment.payment_number}</Text>
                            <Text size="xs" c="dimmed">
                              {payment.transaction_id || 'No transaction ID'}
                            </Text>
                          </div>
                        </Table.Td>
                        <Table.Td>
                          <Group gap="sm">
                            <Avatar size="sm" color="blue">
                              {payment.customer_name.charAt(0)}
                            </Avatar>
                            <div>
                              <Text fw={500} size="sm">{payment.customer_name}</Text>
                              <Text size="xs" c="dimmed">{payment.customer_email}</Text>
                            </div>
                          </Group>
                        </Table.Td>
                        <Table.Td>
                          <div>
                            <Text fw={500} size="sm">{payment.reservation_number}</Text>
                            <Text size="xs" c="dimmed">{payment.vehicle_name}</Text>
                          </div>
                        </Table.Td>
                        <Table.Td>
                          <Badge color={getPaymentTypeColor(payment.payment_type)} size="sm">
                            {t(payment.payment_type)}
                          </Badge>
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <MethodIcon size={16} color="var(--mantine-color-gray-6)" />
                            <Text size="sm">{t(payment.payment_method)}</Text>
                          </Group>
                        </Table.Td>
                        <Table.Td>
                          <Badge color={getStatusColor(payment.status)} size="sm">
                            {t(payment.status)}
                          </Badge>
                        </Table.Td>
                        <Table.Td>
                          <div>
                            <Text fw={700} size="sm">
                              {payment.currency} {payment.amount.toFixed(2)}
                            </Text>
                            {payment.due_date && payment.status === 'pending' && (
                              <Text size="xs" c={new Date(payment.due_date) < new Date() ? 'red' : 'dimmed'}>
                                {t('due')}: {new Date(payment.due_date).toLocaleDateString()}
                              </Text>
                            )}
                          </div>
                        </Table.Td>
                        <Table.Td>
                          <div>
                            <Text size="sm">
                              {new Date(payment.payment_date).toLocaleDateString()}
                            </Text>
                            <Text size="xs" c="dimmed">
                              {new Date(payment.payment_date).toLocaleTimeString()}
                            </Text>
                          </div>
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => {
                                setSelectedPayment(payment)
                                setViewModalOpen(true)
                              }}
                            >
                              <IconEye size={16} />
                            </ActionIcon>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              color="blue"
                              onClick={() => {
                                setSelectedPayment(payment)
                                setEditModalOpen(true)
                              }}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                            {payment.status === 'completed' && payment.payment_type !== 'refund' && (
                              <ActionIcon
                                variant="light"
                                size="sm"
                                color="cyan"
                                onClick={() => {
                                  setSelectedPayment(payment)
                                  setRefundModalOpen(true)
                                }}
                              >
                                <IconRefresh size={16} />
                              </ActionIcon>
                            )}
                            <ActionIcon
                              variant="light"
                              size="sm"
                              color="red"
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Group>
                        </Table.Td>
                      </Table.Tr>
                    )
                  })}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('payments')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="analytics">
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('paymentMethodDistribution')}</Title>
                  <Center>
                    <RingProgress
                      size={200}
                      thickness={20}
                      sections={[
                        { value: 40, color: 'blue', tooltip: `${t('card')}: 40%` },
                        { value: 25, color: 'green', tooltip: `${t('bankTransfer')}: 25%` },
                        { value: 20, color: 'orange', tooltip: `${t('cash')}: 20%` },
                        { value: 15, color: 'cyan', tooltip: `${t('online')}: 15%` }
                      ]}
                      label={
                        <Text size="xs" ta="center">
                          {t('paymentMethods')}
                        </Text>
                      }
                    />
                  </Center>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('revenueOverview')}</Title>
                  <Stack>
                    <Group justify="space-between">
                      <Text size="sm">{t('totalRevenue')}</Text>
                      <Text fw={700} size="lg" c="green">AED {totalRevenue.toFixed(2)}</Text>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm">{t('pendingAmount')}</Text>
                      <Text fw={700} c="orange">AED {pendingAmount.toFixed(2)}</Text>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm">{t('refundAmount')}</Text>
                      <Text fw={700} c="cyan">AED {refundAmount.toFixed(2)}</Text>
                    </Group>
                    <Divider />
                    <Group justify="space-between">
                      <Text size="sm">{t('netRevenue')}</Text>
                      <Text fw={700} size="xl" c="blue">
                        AED {(totalRevenue - refundAmount).toFixed(2)}
                      </Text>
                    </Group>
                  </Stack>
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* Record Payment Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('recordPayment')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('reservation')}
                  placeholder={t('selectReservation')}
                  data={[
                    { value: '1', label: 'RES-2024-001 - Ahmed Al-Rashid' },
                    { value: '2', label: 'RES-2024-002 - Sarah Johnson' },
                    { value: '3', label: 'RES-2024-003 - Mohammed Hassan' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('paymentType')}
                  placeholder={t('selectPaymentType')}
                  data={[
                    { value: 'deposit', label: t('deposit') },
                    { value: 'partial', label: t('partial') },
                    { value: 'full', label: t('full') },
                    { value: 'penalty', label: t('penalty') }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('paymentMethod')}
                  placeholder={t('selectPaymentMethod')}
                  data={[
                    { value: 'cash', label: t('cash') },
                    { value: 'card', label: t('card') },
                    { value: 'bank_transfer', label: t('bankTransfer') },
                    { value: 'online', label: t('online') },
                    { value: 'cheque', label: t('cheque') }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('amount')}
                  placeholder="0.00"
                  min={0}
                  prefix="AED "
                  decimalScale={2}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('transactionId')}
                  placeholder={t('enterTransactionId')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('gatewayReference')}
                  placeholder={t('enterGatewayReference')}
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('description')}
                  placeholder={t('enterPaymentDescription')}
                  rows={3}
                  required
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('notes')}
                  placeholder={t('enterPaymentNotes')}
                  rows={2}
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconCheck size={16} />} onClick={() => setAddModalOpen(false)}>
                {t('recordPayment')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* View Payment Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedPayment ? selectedPayment.payment_number : t('paymentDetails')}
          size="lg"
        >
          {selectedPayment && (
            <Stack>
              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('customer')}</Text>
                  <Text fw={500}>{selectedPayment.customer_name}</Text>
                  <Text size="xs" c="dimmed">{selectedPayment.customer_email}</Text>
                  <Text size="xs" c="dimmed">{selectedPayment.customer_phone}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('reservation')}</Text>
                  <Text fw={500}>{selectedPayment.reservation_number}</Text>
                  <Text size="xs" c="dimmed">{selectedPayment.vehicle_name}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('paymentType')}</Text>
                  <Badge color={getPaymentTypeColor(selectedPayment.payment_type)}>
                    {t(selectedPayment.payment_type)}
                  </Badge>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('paymentMethod')}</Text>
                  <Group gap="xs">
                    {(() => {
                      const MethodIcon = getMethodIcon(selectedPayment.payment_method)
                      return <MethodIcon size={16} color="var(--mantine-color-gray-6)" />
                    })()}
                    <Text>{t(selectedPayment.payment_method)}</Text>
                  </Group>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('status')}</Text>
                  <Badge color={getStatusColor(selectedPayment.status)}>
                    {t(selectedPayment.status)}
                  </Badge>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('amount')}</Text>
                  <Text fw={700} size="xl">
                    {selectedPayment.currency} {selectedPayment.amount.toFixed(2)}
                  </Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('paymentDate')}</Text>
                  <Text fw={500}>{new Date(selectedPayment.payment_date).toLocaleDateString()}</Text>
                  <Text size="xs" c="dimmed">{new Date(selectedPayment.payment_date).toLocaleTimeString()}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('processedBy')}</Text>
                  <Text fw={500}>{selectedPayment.processed_by}</Text>
                </Grid.Col>
                {selectedPayment.transaction_id && (
                  <Grid.Col span={6}>
                    <Text size="sm" c="dimmed">{t('transactionId')}</Text>
                    <Text fw={500}>{selectedPayment.transaction_id}</Text>
                  </Grid.Col>
                )}
                {selectedPayment.gateway_reference && (
                  <Grid.Col span={6}>
                    <Text size="sm" c="dimmed">{t('gatewayReference')}</Text>
                    <Text fw={500}>{selectedPayment.gateway_reference}</Text>
                  </Grid.Col>
                )}
                <Grid.Col span={12}>
                  <Divider />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Text size="sm" c="dimmed">{t('description')}</Text>
                  <Text>{selectedPayment.description}</Text>
                </Grid.Col>
                {selectedPayment.notes && (
                  <Grid.Col span={12}>
                    <Text size="sm" c="dimmed">{t('notes')}</Text>
                    <Text>{selectedPayment.notes}</Text>
                  </Grid.Col>
                )}
                {selectedPayment.due_date && selectedPayment.status === 'pending' && (
                  <Grid.Col span={12}>
                    <Alert
                      color={new Date(selectedPayment.due_date) < new Date() ? 'red' : 'orange'}
                      icon={<IconAlertTriangle size={16} />}
                    >
                      <Text fw={500}>
                        {new Date(selectedPayment.due_date) < new Date() ? t('paymentOverdue') : t('paymentDue')}
                      </Text>
                      <Text size="sm">
                        {t('dueDate')}: {new Date(selectedPayment.due_date).toLocaleDateString()}
                      </Text>
                    </Alert>
                  </Grid.Col>
                )}
              </Grid>
            </Stack>
          )}
        </Modal>

        {/* Refund Modal */}
        <Modal
          opened={refundModalOpen}
          onClose={() => setRefundModalOpen(false)}
          title={t('processRefund')}
          size="md"
        >
          {selectedPayment && (
            <Stack>
              <Alert color="cyan" icon={<IconRefresh size={16} />}>
                <Text fw={500}>{t('refundPayment')}</Text>
                <Text size="sm">
                  {t('originalPayment')}: {selectedPayment.payment_number} - {selectedPayment.currency} {selectedPayment.amount.toFixed(2)}
                </Text>
              </Alert>

              <NumberInput
                label={t('refundAmount')}
                placeholder="0.00"
                min={0}
                max={selectedPayment.amount}
                prefix={`${selectedPayment.currency} `}
                decimalScale={2}
                defaultValue={selectedPayment.amount}
                required
              />

              <Select
                label={t('refundReason')}
                placeholder={t('selectRefundReason')}
                data={[
                  { value: 'early_return', label: t('earlyReturn') },
                  { value: 'cancellation', label: t('cancellation') },
                  { value: 'damage_deposit', label: t('damageDepositReturn') },
                  { value: 'overpayment', label: t('overpayment') },
                  { value: 'other', label: t('other') }
                ]}
                required
              />

              <Textarea
                label={t('refundNotes')}
                placeholder={t('enterRefundNotes')}
                rows={3}
              />

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setRefundModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button
                  leftSection={<IconRefresh size={16} />}
                  color="cyan"
                  onClick={() => setRefundModalOpen(false)}
                >
                  {t('processRefund')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
