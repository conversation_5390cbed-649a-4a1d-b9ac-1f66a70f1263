import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Tabs,
  Checkbox,
  Menu,
  Avatar
} from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import {
  IconCar,
  IconCopy,
  IconCurrencyDollar,
  IconDots,
  IconDownload,
  IconEdit,
  IconEye,
  IconMail,
  IconPlus,
  IconPrinter,
  IconSearch,
  IconSend,
  IconTrash
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface Quote {
  id: string
  quoteNumber: string
  customerId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  vehicleCategory: string
  vehiclePreference?: string
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'declined' | 'expired'
  validUntil: string
  pickupDate: string
  returnDate: string
  totalDays: number
  pickupLocation: string
  returnLocation: string
  baseRate: number
  discountAmount: number
  taxAmount: number
  totalAmount: number
  createdDate: string
  sentDate?: string
  viewedDate?: string
  respondedDate?: string
  notes: string
  additionalServices: string[]
  terms: string
}

export function Quotes() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [dateFilter, setDateFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Mock data
  const quotes: Quote[] = [
    {
      id: '1',
      quoteNumber: 'QUO-2024-001',
      customerId: '1',
      customerName: 'Ahmed Al-Rashid',
      customerEmail: '<EMAIL>',
      customerPhone: '+971501234567',
      vehicleCategory: 'Luxury',
      vehiclePreference: 'Mercedes C-Class or similar',
      status: 'sent',
      validUntil: '2024-01-25',
      pickupDate: '2024-01-20',
      returnDate: '2024-01-25',
      totalDays: 5,
      pickupLocation: 'Dubai Airport',
      returnLocation: 'Dubai Airport',
      baseRate: 900,
      discountAmount: 50,
      taxAmount: 42.5,
      totalAmount: 892.5,
      createdDate: '2024-01-15',
      sentDate: '2024-01-15',
      notes: 'Corporate client - apply 5% discount',
      additionalServices: ['GPS Navigation', 'Full Insurance'],
      terms: 'Standard rental terms and conditions apply'
    },
    {
      id: '2',
      quoteNumber: 'QUO-2024-002',
      customerId: '2',
      customerName: 'Sarah Johnson',
      customerEmail: '<EMAIL>',
      customerPhone: '+971507654321',
      vehicleCategory: 'SUV',
      status: 'viewed',
      validUntil: '2024-01-22',
      pickupDate: '2024-01-18',
      returnDate: '2024-01-22',
      totalDays: 4,
      pickupLocation: 'Downtown Dubai',
      returnLocation: 'Downtown Dubai',
      baseRate: 800,
      discountAmount: 0,
      taxAmount: 40,
      totalAmount: 840,
      createdDate: '2024-01-10',
      sentDate: '2024-01-10',
      viewedDate: '2024-01-12',
      notes: 'Customer interested in BMW X5',
      additionalServices: ['Additional Driver'],
      terms: 'Standard rental terms and conditions apply'
    },
    {
      id: '3',
      quoteNumber: 'QUO-2024-003',
      customerId: '3',
      customerName: 'Mohammed Hassan',
      customerEmail: '<EMAIL>',
      customerPhone: '+971509876543',
      vehicleCategory: 'Economy',
      status: 'accepted',
      validUntil: '2024-01-30',
      pickupDate: '2024-01-25',
      returnDate: '2024-01-30',
      totalDays: 5,
      pickupLocation: 'Abu Dhabi',
      returnLocation: 'Abu Dhabi',
      baseRate: 600,
      discountAmount: 30,
      taxAmount: 28.5,
      totalAmount: 598.5,
      createdDate: '2024-01-16',
      sentDate: '2024-01-16',
      viewedDate: '2024-01-17',
      respondedDate: '2024-01-17',
      notes: 'First time customer - welcome discount applied',
      additionalServices: ['Basic Insurance'],
      terms: 'Standard rental terms and conditions apply'
    }
  ]

  const stats = [
    { label: t('totalQuotes'), value: '89', color: 'blue', icon: IconCurrencyDollar },
    { label: t('pendingResponse'), value: '23', color: 'orange', icon: IconCar},
    { label: t('acceptedQuotes'), value: '45', color: 'green', icon: IconCar},
    { label: t('conversionRate'), value: '68%', color: 'purple', icon: IconCurrencyDollar }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'green'
      case 'sent': return 'blue'
      case 'viewed': return 'cyan'
      case 'draft': return 'gray'
      case 'declined': return 'red'
      case 'expired': return 'dark'
      default: return 'gray'
    }
  }

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.quoteNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.vehicleCategory.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || quote.status === statusFilter
    const matchesDate = !dateFilter || quote.createdDate.includes(dateFilter)
    
    return matchesSearch && matchesStatus && matchesDate
  })

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('quotesManagement')}</Title>
          <Text c="dimmed" size="sm">{t('createAndManageRentalQuotes')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportQuotes')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
            {t('createQuote')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="quotes">{t('allQuotes')}</Tabs.Tab>
          <Tabs.Tab value="templates">{t('templates')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Recent Quotes */}
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('recentQuotes')}</Title>
                  <Button variant="light" size="sm">
                    {t('viewAll')}
                  </Button>
                </Group>
                
                <Stack gap="sm">
                  {quotes.slice(0, 3).map((quote) => (
                    <Paper key={quote.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Group gap="sm">
                            <Avatar size="sm" color="blue">
                              {quote.customerName.charAt(0)}
                            </Avatar>
                            <div>
                              <Text fw={500} size="sm">{quote.quoteNumber}</Text>
                              <Text size="xs" c="dimmed">{quote.customerName} • {quote.vehicleCategory}</Text>
                            </div>
                          </Group>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(quote.status)} size="sm">
                            {t(quote.status)}
                          </Badge>
                          <Text size="xs" c="dimmed">${quote.totalAmount}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>

            {/* Quick Actions */}
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('quickActions')}</Title>
                
                <Stack gap="sm">
                  <Button variant="light" fullWidth leftSection={<IconPlus size={16} />}>
                    {t('createQuote')}
                  </Button>
                  
                  <Button variant="light" fullWidth leftSection={<IconCopy size={16} />}>
                    {t('duplicateLastQuote')}
                  </Button>
                  
                  <Button variant="light" fullWidth leftSection={<IconMail size={16} />}>
                    {t('sendReminders')}
                  </Button>
                  
                  <Button variant="light" fullWidth leftSection={<IconDownload size={16} />}>
                    {t('exportReport')}
                  </Button>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="quotes" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchQuotes')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'draft', label: t('draft') },
                    { value: 'sent', label: t('sent') },
                    { value: 'viewed', label: t('viewed') },
                    { value: 'accepted', label: t('accepted') },
                    { value: 'declined', label: t('declined') },
                    { value: 'expired', label: t('expired') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allDates')}
                  data={[
                    { value: '', label: t('allDates') },
                    { value: '2024-01', label: t('thisMonth') },
                    { value: '2024-01-20', label: t('thisWeek') }
                  ]}
                  value={dateFilter}
                  onChange={(value) => setDateFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Quotes Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('quote')}</Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('dates')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('validUntil')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredQuotes.map((quote) => (
                  <Table.Tr key={quote.id}>
                    <Table.Td>
                      <div>
                        <Text fw={700} size="sm">{quote.quoteNumber}</Text>
                        <Text size="xs" c="dimmed">{quote.createdDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="sm">
                        <Avatar size="sm" color="blue">
                          {quote.customerName.charAt(0)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{quote.customerName}</Text>
                          <Text size="xs" c="dimmed">{quote.customerEmail}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{quote.vehicleCategory}</Text>
                        {quote.vehiclePreference && (
                          <Text size="xs" c="dimmed">{quote.vehiclePreference}</Text>
                        )}
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{quote.pickupDate} - {quote.returnDate}</Text>
                        <Text size="xs" c="dimmed">{quote.totalDays} {t('days')}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(quote.status)}>
                        {t(quote.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" c={new Date(quote.validUntil) < new Date() ? 'red' : undefined}>
                        {quote.validUntil}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${quote.totalAmount}</Text>
                      {quote.discountAmount > 0 && (
                        <Text size="xs" c="green">-${quote.discountAmount}</Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <Menu>
                          <Menu.Target>
                            <ActionIcon variant="light" size="sm">
                              <IconDots size={14} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item leftSection={<IconSend size={14} />}>
                              {t('sendQuote')}
                            </Menu.Item>
                            <Menu.Item leftSection={<IconCopy size={14} />}>
                              {t('duplicate')}
                            </Menu.Item>
                            <Menu.Item leftSection={<IconPrinter size={14} />}>
                              {t('print')}
                            </Menu.Item>
                            <Menu.Item leftSection={<IconDownload size={14} />}>
                              {t('downloadPDF')}
                            </Menu.Item>
                            <Menu.Divider />
                            <Menu.Item leftSection={<IconTrash size={14} />} color="red">
                              {t('delete')}
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="templates" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('quoteTemplates')}</Title>
            <Text c="dimmed">{t('quoteTemplatesWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('conversionAnalytics')}</Title>
                <Text c="dimmed">{t('conversionAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
            
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('quotePerformance')}</Title>
                <Text c="dimmed">{t('performanceAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>

      {/* Create Quote Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('createNewQuote')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectOrCreateCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid (<EMAIL>)' },
              { value: '2', label: 'Sarah Johnson (<EMAIL>)' },
              { value: '3', label: 'Mohammed Hassan (<EMAIL>)' }
            ]}
            searchable
            required
          />

          <Select
            label={t('vehicleCategory')}
            placeholder={t('selectVehicleCategory')}
            data={[
              { value: 'economy', label: t('economy') },
              { value: 'sedan', label: t('sedan') },
              { value: 'suv', label: t('suv') },
              { value: 'luxury', label: t('luxury') }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <DatePickerInput
                label={t('pickupDate')}
                placeholder={t('selectPickupDate')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <DatePickerInput
                label={t('returnDate')}
                placeholder={t('selectReturnDate')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <NumberInput
                label={t('baseRate')}
                placeholder={t('enterBaseRate')}
                prefix="$"
                min={0}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <NumberInput
                label={t('discountAmount')}
                placeholder={t('enterDiscount')}
                prefix="$"
                min={0}
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('notes')}
            placeholder={t('enterQuoteNotes')}
            rows={3}
          />

          <Checkbox.Group label={t('additionalServices')}>
            <Stack mt="xs">
              <Checkbox value="gps" label={t('gpsNavigation')} />
              <Checkbox value="insurance" label={t('fullInsurance')} />
              <Checkbox value="driver" label={t('additionalDriver')} />
              <Checkbox value="seat" label={t('childSeat')} />
            </Stack>
          </Checkbox.Group>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('saveDraft')}
            </Button>
            <Button onClick={() => setAddModalOpen(false)}>
              {t('createAndSend')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
