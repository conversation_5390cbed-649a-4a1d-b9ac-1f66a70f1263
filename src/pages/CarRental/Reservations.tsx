import { useState, useMemo } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Timeline,
  Avatar,
  Menu
} from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconEye,IconCalendar,IconCar,
  IconCurrencyDollar,
  IconClock,
  IconCheck,
  IconX,
  IconDots,
  IconPhone,
  IconMail,
  IconDownload,
  IconPrinter,
  IconAlertTriangle
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { useAppStore } from '../../store/useAppStore'

interface Reservation {
  id: string
  reservationNumber: string
  customerId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  vehicleId: string
  vehicleName: string
  plateNumber: string
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'no-show'
  pickupDate: string
  returnDate: string
  pickupLocation: string
  returnLocation: string
  totalDays: number
  dailyRate: number
  totalAmount: number
  paidAmount: number
  paymentStatus: 'pending' | 'partial' | 'paid' | 'refunded'
  createdDate: string
  notes: string
  specialRequests: string[]
}

export function Reservations() {
  const { t } = useTranslation()

  // Get real data from store
  const { reservations: storeReservations, vehicles, customers } = useAppStore()

  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [dateFilter, setDateFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Transform store reservations to display format
  const reservations: Reservation[] = useMemo(() => {
    return storeReservations.map(reservation => {
      const vehicle = vehicles.find(v => v.id === reservation.vehicle_id)
      const customer = customers.find(c => c.id === reservation.customer_id)

      return {
        id: reservation.id,
        reservationNumber: reservation.reservation_number,
        customerId: reservation.customer_id,
        customerName: customer?.first_name && customer?.last_name
          ? `${customer.first_name} ${customer.last_name}`
          : `Customer ${reservation.customer_id.slice(0, 8)}...`,
        customerEmail: customer?.email || 'N/A',
        customerPhone: customer?.phone || 'N/A',
        vehicleId: reservation.vehicle_id || '',
        vehicleName: vehicle ? `${vehicle.make} ${vehicle.model} ${vehicle.year}` : 'Unknown Vehicle',
        plateNumber: vehicle?.plate_number || 'N/A',
        status: reservation.status,
        pickupDate: new Date(reservation.pickup_date).toLocaleDateString(),
        returnDate: new Date(reservation.return_date).toLocaleDateString(),
        pickupLocation: 'Main Branch', // TODO: Get from location data
        returnLocation: 'Main Branch', // TODO: Get from location data
        totalDays: reservation.total_days || 1,
        dailyRate: reservation.daily_rate || 0,
        totalAmount: reservation.total_amount || 0,
        paidAmount: 0, // TODO: Calculate from payments
        paymentStatus: 'pending' as const, // TODO: Calculate from payments
        createdDate: new Date(reservation.created_at).toLocaleDateString(),
        notes: reservation.notes || '',
        specialRequests: reservation.special_requests ? [reservation.special_requests] : []
      }
    })
  }, [storeReservations, vehicles, customers])

  // Calculate real stats from store data
  const stats = useMemo(() => {
    const total = storeReservations.length
    const active = storeReservations.filter(r => r.status === 'active').length
    const pending = storeReservations.filter(r => r.status === 'pending').length
    const confirmed = storeReservations.filter(r => r.status === 'confirmed').length
    const totalRevenue = storeReservations.reduce((sum, r) => sum + (r.total_amount || 0), 0)

    return [
      { label: t('totalReservations'), value: total.toString(), color: 'blue', icon: IconCalendar },
      { label: t('activeRentals'), value: active.toString(), color: 'green', icon: IconCar },
      { label: t('pendingConfirmation'), value: (pending + confirmed).toString(), color: 'orange', icon: IconClock },
      { label: t('monthlyRevenue'), value: `$${totalRevenue.toLocaleString()}`, color: 'red', icon: IconCurrencyDollar }
    ]
  }, [storeReservations, t])

  // Generate today's activity from real data
  const todayActivity = useMemo(() => {
    const today = new Date().toDateString()
    const todayReservations = storeReservations.filter(r =>
      new Date(r.pickup_date).toDateString() === today ||
      new Date(r.return_date).toDateString() === today
    ).slice(0, 3)

    return todayReservations.map(reservation => {
      const vehicle = vehicles.find(v => v.id === reservation.vehicle_id)
      const customer = customers.find(c => c.id === reservation.customer_id)
      const isPickup = new Date(reservation.pickup_date).toDateString() === today

      return {
        type: isPickup ? 'pickup' : 'return',
        customer: customer?.first_name && customer?.last_name
          ? `${customer.first_name} ${customer.last_name}`
          : `Customer ${reservation.customer_id.slice(0, 8)}...`,
        vehicle: vehicle ? `${vehicle.make} ${vehicle.model}` : 'Unknown Vehicle',
        time: isPickup ? '09:00 AM' : '02:00 PM', // TODO: Get real time from reservation
        status: reservation.status === 'confirmed' ? 'confirmed' : 'pending'
      }
    })
  }, [storeReservations, vehicles, customers])

  // Real-time data - no more mock data needed!

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'blue'
      case 'active': return 'green'
      case 'completed': return 'gray'
      case 'pending': return 'orange'
      case 'cancelled': return 'red'
      case 'no-show': return 'dark'
      default: return 'gray'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'green'
      case 'partial': return 'yellow'
      case 'pending': return 'orange'
      case 'refunded': return 'blue'
      default: return 'gray'
    }
  }

  const filteredReservations = reservations.filter(reservation => {
    const matchesSearch = reservation.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reservation.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reservation.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reservation.plateNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || reservation.status === statusFilter
    const matchesDate = !dateFilter || reservation.pickupDate.includes(dateFilter)
    
    return matchesSearch && matchesStatus && matchesDate
  })

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('reservationsManagement')}</Title>
          <Text c="dimmed" size="sm">{t('manageVehicleReservationsAndBookings')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconCalendar size={16} />}>
            {t('reservationCalendar')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
            {t('newReservation')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="reservations">{t('allReservations')}</Tabs.Tab>
          <Tabs.Tab value="calendar">{t('calendar')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Today's Activity */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('todaysActivity')}</Title>
                  <Button variant="light" size="sm">
                    {t('viewAll')}
                  </Button>
                </Group>
                
                <Timeline active={1} bulletSize={24} lineWidth={2}>
                  {todayActivity.map((activity, index) => (
                    <Timeline.Item
                      key={index}
                      bullet={activity.type === 'pickup' ? <IconCar size={12} /> : <IconCheck size={12} />}
                      title={activity.type === 'pickup' ? t('vehiclePickup') : t('vehicleReturn')}
                    >
                      <Text size="sm" fw={500}>{activity.customer}</Text>
                      <Text size="xs" c="dimmed">{activity.vehicle} - {activity.time}</Text>
                      <Badge size="xs" color={activity.status === 'confirmed' ? 'green' : 'orange'}>
                        {t(activity.status)}
                      </Badge>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Card>
            </Grid.Col>

            {/* Pending Actions */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('pendingActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('pendingConfirmations')}</Text>
                    <Text size="xs">8 {t('reservationsAwaitingConfirmation')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCurrencyDollar size={16} />} color="red">
                    <Text fw={500} size="sm">{t('pendingPayments')}</Text>
                    <Text size="xs">5 {t('reservationsWithPendingPayments')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('upcomingPickups')}</Text>
                    <Text size="xs">12 {t('pickupsScheduledForTomorrow')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="reservations" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchReservations')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'pending', label: t('pending') },
                    { value: 'confirmed', label: t('confirmed') },
                    { value: 'active', label: t('active') },
                    { value: 'completed', label: t('completed') },
                    { value: 'cancelled', label: t('cancelled') },
                    { value: 'no-show', label: t('noShow') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allDates')}
                  data={[
                    { value: '', label: t('allDates') },
                    { value: '2024-01', label: t('thisMonth') },
                    { value: '2024-01-20', label: t('today') },
                    { value: '2024-01-21', label: t('tomorrow') }
                  ]}
                  value={dateFilter}
                  onChange={(value) => setDateFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Reservations Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('reservation')}</Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('dates')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('payment')}</Table.Th>
                  <Table.Th>{t('total')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredReservations.map((reservation) => (
                  <Table.Tr key={reservation.id}>
                    <Table.Td>
                      <div>
                        <Text fw={700} size="sm">{reservation.reservationNumber}</Text>
                        <Text size="xs" c="dimmed">{reservation.createdDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="sm">
                        <Avatar size="sm" color="blue">
                          {reservation.customerName.charAt(0)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{reservation.customerName}</Text>
                          <Text size="xs" c="dimmed">{reservation.customerEmail}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{reservation.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{reservation.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{reservation.pickupDate} - {reservation.returnDate}</Text>
                        <Text size="xs" c="dimmed">{reservation.totalDays} {t('days')}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(reservation.status)}>
                        {t(reservation.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getPaymentStatusColor(reservation.paymentStatus)} variant="light">
                        {t(reservation.paymentStatus)}
                      </Badge>
                      <Text size="xs" c="dimmed">
                        ${reservation.paidAmount} / ${reservation.totalAmount}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${reservation.totalAmount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <Menu>
                          <Menu.Target>
                            <ActionIcon variant="light" size="sm">
                              <IconDots size={14} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item leftSection={<IconPhone size={14} />}>
                              {t('callCustomer')}
                            </Menu.Item>
                            <Menu.Item leftSection={<IconMail size={14} />}>
                              {t('emailCustomer')}
                            </Menu.Item>
                            <Menu.Item leftSection={<IconPrinter size={14} />}>
                              {t('printVoucher')}
                            </Menu.Item>
                            <Menu.Divider />
                            <Menu.Item leftSection={<IconX size={14} />} color="red">
                              {t('cancelReservation')}
                            </Menu.Item>
                          </Menu.Dropdown>
                        </Menu>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="calendar" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('reservationCalendar')}</Title>
            <Text c="dimmed">{t('calendarViewWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('reservationTrends')}</Title>
                <Text c="dimmed">{t('trendsAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
            
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('customerAnalytics')}</Title>
                <Text c="dimmed">{t('customerAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>

      {/* New Reservation Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('createNewReservation')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectOrCreateCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid (<EMAIL>)' },
              { value: '2', label: 'Sarah Johnson (<EMAIL>)' },
              { value: '3', label: 'Mohammed Hassan (<EMAIL>)' }
            ]}
            searchable
            required
          />

          <Select
            label={t('vehicle')}
            placeholder={t('selectAvailableVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023 (ABC-123) - $120/day' },
              { value: '2', label: 'BMW X5 2022 (XYZ-456) - $200/day' },
              { value: '3', label: 'Mercedes C-Class 2023 (DEF-789) - $180/day' }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <DatePickerInput
                label={t('pickupDate')}
                placeholder={t('selectPickupDate')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <DatePickerInput
                label={t('returnDate')}
                placeholder={t('selectReturnDate')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('pickupLocation')}
                placeholder={t('selectPickupLocation')}
                data={[
                  { value: 'dubai-airport', label: 'Dubai Airport' },
                  { value: 'downtown-dubai', label: 'Downtown Dubai' },
                  { value: 'abu-dhabi', label: 'Abu Dhabi' }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('returnLocation')}
                placeholder={t('selectReturnLocation')}
                data={[
                  { value: 'dubai-airport', label: 'Dubai Airport' },
                  { value: 'downtown-dubai', label: 'Downtown Dubai' },
                  { value: 'abu-dhabi', label: 'Abu Dhabi' }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={() => setAddModalOpen(false)}>
              {t('createReservation')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
