import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,Alert,
  Tabs
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCalendar,
  IconCar,
  IconClock,
  IconCurrencyDollar,
  IconDownload,
  IconEdit,
  IconEye,
  IconPlus,
  IconSearch,
  IconTrash
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface SimpleReservation {
  id: string
  reservationNumber: string
  customerName: string
  customerEmail: string
  vehicleName: string
  plateNumber: string
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled'
  pickupDate: string
  returnDate: string
  totalDays: number
  totalAmount: number
  paymentStatus: 'pending' | 'partial' | 'paid'
}

export function ReservationsSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Simple mock data
  const reservations: SimpleReservation[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerEmail: '<EMAIL>',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      status: 'confirmed',
      pickupDate: '2024-01-20',
      returnDate: '2024-01-25',
      totalDays: 5,
      totalAmount: 600,
      paymentStatus: 'partial'
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      customerEmail: '<EMAIL>',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      status: 'active',
      pickupDate: '2024-01-18',
      returnDate: '2024-01-22',
      totalDays: 4,
      totalAmount: 800,
      paymentStatus: 'paid'
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      customerEmail: '<EMAIL>',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      status: 'pending',
      pickupDate: '2024-01-25',
      returnDate: '2024-01-30',
      totalDays: 5,
      totalAmount: 900,
      paymentStatus: 'pending'
    }
  ]

  const stats = [
    { label: t('totalReservations'), value: '156', color: 'blue', icon: IconCalendar },
    { label: t('activeRentals'), value: '23', color: 'green', icon: IconCar},
    { label: t('pendingConfirmation'), value: '8', color: 'orange', icon: IconClock },
    { label: t('monthlyRevenue'), value: '$45,230', color: 'red', icon: IconCurrencyDollar }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'blue'
      case 'active': return 'green'
      case 'completed': return 'gray'
      case 'pending': return 'orange'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'green'
      case 'partial': return 'yellow'
      case 'pending': return 'orange'
      default: return 'gray'
    }
  }

  const filteredReservations = reservations.filter(reservation => {
    const matchesSearch = reservation.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reservation.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         reservation.vehicleName.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || reservation.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('reservationsManagement')}
        description={t('manageVehicleReservationsAndBookings')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconCalendar size={16} />}>
              {t('reservationCalendar')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('newReservation')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="reservations">{t('allReservations')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Pending Actions */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('pendingActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('pendingConfirmations')}</Text>
                    <Text size="xs">8 {t('reservationsAwaitingConfirmation')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCurrencyDollar size={16} />} color="red">
                    <Text fw={500} size="sm">{t('pendingPayments')}</Text>
                    <Text size="xs">5 {t('reservationsWithPendingPayments')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('upcomingPickups')}</Text>
                    <Text size="xs">12 {t('pickupsScheduledForTomorrow')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Reservations */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentReservations')}</Title>
                
                <Stack gap="sm">
                  {reservations.slice(0, 3).map((reservation) => (
                    <Paper key={reservation.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text fw={500} size="sm">{reservation.customerName}</Text>
                          <Text size="xs" c="dimmed">{reservation.vehicleName}</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(reservation.status)} size="sm">
                            {t(reservation.status)}
                          </Badge>
                          <Text size="xs" c="dimmed">${reservation.totalAmount}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="reservations" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchReservations')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'pending', label: t('pending') },
                    { value: 'confirmed', label: t('confirmed') },
                    { value: 'active', label: t('active') },
                    { value: 'completed', label: t('completed') },
                    { value: 'cancelled', label: t('cancelled') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Reservations Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('reservation')}</Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('dates')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('payment')}</Table.Th>
                  <Table.Th>{t('total')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredReservations.map((reservation) => (
                  <Table.Tr key={reservation.id}>
                    <Table.Td>
                      <Text fw={700} size="sm">{reservation.reservationNumber}</Text>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{reservation.customerName}</Text>
                        <Text size="xs" c="dimmed">{reservation.customerEmail}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{reservation.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{reservation.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{reservation.pickupDate} - {reservation.returnDate}</Text>
                        <Text size="xs" c="dimmed">{reservation.totalDays} {t('days')}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(reservation.status)}>
                        {t(reservation.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getPaymentStatusColor(reservation.paymentStatus)} variant="light">
                        {t(reservation.paymentStatus)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${reservation.totalAmount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('reservationAnalytics')}</Title>
            <Text c="dimmed">{t('analyticsWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* New Reservation Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('createNewReservation')}
        size="md"
      >
        <Text>{t('reservationFormWillBeImplementedHere')}</Text>
      </Modal>
    </Stack>
  )
}
