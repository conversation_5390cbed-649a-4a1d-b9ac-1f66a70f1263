import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Badge,
  ActionIcon,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,Menu,ScrollArea
} from '@mantine/core'
import {
  IconCalendar,
  IconChevronLeft,
  IconChevronRight,
  IconPlus,
  IconFilter,
  IconEye,
  IconEdit,
  IconCar,IconClock,IconDots,
  IconRefresh,
  IconDownload,
  IconSettings
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface CalendarEvent {
  id: string
  type: 'pickup' | 'return' | 'maintenance' | 'inspection' | 'blocked'
  title: string
  description: string
  date: string
  time: string
  duration: number
  vehicleId?: string
  vehicleName?: string
  plateNumber?: string
  customerId?: string
  customerName?: string
  reservationId?: string
  location: string
  status: 'confirmed' | 'pending' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  color: string
}

export function Calendar() {
  const { t } = useTranslation()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month')
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [eventModalOpen, setEventModalOpen] = useState(false)
  const [filterType, setFilterType] = useState<string>('')
  const [filterVehicle, setFilterVehicle] = useState<string>('')

  // Mock calendar events
  const events: CalendarEvent[] = [
    {
      id: '1',
      type: 'pickup',
      title: 'Vehicle Pickup',
      description: 'Ahmed Al-Rashid - Toyota Camry pickup',
      date: '2024-01-20',
      time: '09:00',
      duration: 30,
      vehicleId: '1',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      customerId: '1',
      customerName: 'Ahmed Al-Rashid',
      reservationId: 'RES-2024-001',
      location: 'Dubai Airport',
      status: 'confirmed',
      priority: 'medium',
      color: 'blue'
    },
    {
      id: '2',
      type: 'return',
      title: 'Vehicle Return',
      description: 'Sarah Johnson - BMW X5 return',
      date: '2024-01-22',
      time: '14:00',
      duration: 30,
      vehicleId: '2',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      customerId: '2',
      customerName: 'Sarah Johnson',
      reservationId: 'RES-2024-002',
      location: 'Downtown Dubai',
      status: 'confirmed',
      priority: 'medium',
      color: 'green'
    },
    {
      id: '3',
      type: 'maintenance',
      title: 'Scheduled Maintenance',
      description: 'Mercedes C-Class - Oil change and inspection',
      date: '2024-01-25',
      time: '10:00',
      duration: 120,
      vehicleId: '3',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      location: 'Service',
      status: 'pending',
      priority: 'high',
      color: 'orange'
    },
    {
      id: '4',
      type: 'pickup',
      title: 'Vehicle Pickup',
      description: 'Mohammed Hassan - Mercedes C-Class pickup',
      date: '2024-01-25',
      time: '16:00',
      duration: 30,
      vehicleId: '3',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      customerId: '3',
      customerName: 'Mohammed Hassan',
      reservationId: 'RES-2024-003',
      location: 'Abu Dhabi',
      status: 'confirmed',
      priority: 'medium',
      color: 'blue'
    },
    {
      id: '5',
      type: 'blocked',
      title: 'Vehicle Unavailable',
      description: 'Honda Civic - Repair work in progress',
      date: '2024-01-23',
      time: '00:00',
      duration: 1440, // All day
      vehicleId: '4',
      vehicleName: 'Honda Civic 2023',
      plateNumber: 'GHI-012',
      location: 'Repair Shop',
      status: 'confirmed',
      priority: 'high',
      color: 'red'
    }
  ]

  const todayEvents = events.filter(event => event.date === new Date().toISOString().split('T')[0])
  const upcomingEvents = events.filter(event => new Date(event.date) > new Date()).slice(0, 5)

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'pickup': return <IconCar size={14} />
      case 'return': return <IconCar size={14} />
      case 'maintenance': return <IconSettings size={14} />
      case 'inspection': return <IconEye size={14} />
      case 'blocked': return <IconClock size={14} />
      default: return <IconCalendar size={14} />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'green'
      case 'pending': return 'orange'
      case 'completed': return 'blue'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red'
      case 'medium': return 'yellow'
      case 'low': return 'green'
      default: return 'gray'
    }
  }

  // Filtered events for future calendar implementation
  // const filteredEvents = events.filter(event => {
  //   const matchesType = !filterType || event.type === filterType
  //   const matchesVehicle = !filterVehicle || event.vehicleId === filterVehicle
  //   return matchesType && matchesVehicle
  // })

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    })
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('rentalCalendar')}</Title>
          <Text c="dimmed" size="sm">{t('manageSchedulesAndAvailability')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportCalendar')}
          </Button>
          <Button variant="light" leftSection={<IconRefresh size={16} />}>
            {t('refresh')}
          </Button>
          <Button leftSection={<IconPlus size={16} />}>
            {t('addEvent')}
          </Button>
        </Group>
      </Group>

      <Grid>
        {/* Calendar Controls */}
        <Grid.Col span={12}>
          <Card withBorder mb="lg">
            <Group justify="space-between">
              <Group>
                <ActionIcon variant="light" onClick={() => navigateMonth('prev')}>
                  <IconChevronLeft size={16} />
                </ActionIcon>
                <Title order={3}>{formatDate(currentDate)}</Title>
                <ActionIcon variant="light" onClick={() => navigateMonth('next')}>
                  <IconChevronRight size={16} />
                </ActionIcon>
              </Group>
              
              <Group>
                <Select
                  placeholder={t('allTypes')}
                  data={[
                    { value: '', label: t('allTypes') },
                    { value: 'pickup', label: t('pickups') },
                    { value: 'return', label: t('returns') },
                    { value: 'maintenance', label: t('maintenance') },
                    { value: 'inspection', label: t('inspections') },
                    { value: 'blocked', label: t('blocked') }
                  ]}
                  value={filterType}
                  onChange={(value) => setFilterType(value || '')}
                  leftSection={<IconFilter size={14} />}
                />
                
                <Select
                  placeholder={t('allVehicles')}
                  data={[
                    { value: '', label: t('allVehicles') },
                    { value: '1', label: 'Toyota Camry (ABC-123)' },
                    { value: '2', label: 'BMW X5 (XYZ-456)' },
                    { value: '3', label: 'Mercedes C-Class (DEF-789)' }
                  ]}
                  value={filterVehicle}
                  onChange={(value) => setFilterVehicle(value || '')}
                />
                
                <Select
                  value={viewMode}
                  onChange={(value) => setViewMode(value as 'month' | 'week' | 'day')}
                  data={[
                    { value: 'month', label: t('month') },
                    { value: 'week', label: t('week') },
                    { value: 'day', label: t('day') }
                  ]}
                />
              </Group>
            </Group>
          </Card>
        </Grid.Col>

        {/* Main Calendar View */}
        <Grid.Col span={{ base: 12, lg: 8 }}>
          <Card withBorder>
            <Title order={4} mb="md">{t('calendarView')}</Title>
            
            {/* Calendar Grid Placeholder */}
            <div style={{ minHeight: '500px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Stack align="center">
                <IconCalendar size={64} color="var(--mantine-color-gray-5)" />
                <Text c="dimmed" size="lg">{t('interactiveCalendarWillBeImplementedHere')}</Text>
                <Text c="dimmed" size="sm">{t('thisWillShowFullCalendarWithDragDrop')}</Text>
              </Stack>
            </div>
          </Card>
        </Grid.Col>

        {/* Sidebar */}
        <Grid.Col span={{ base: 12, lg: 4 }}>
          <Stack>
            {/* Today's Events */}
            <Card withBorder>
              <Group justify="space-between" mb="md">
                <Title order={4}>{t('todaysEvents')}</Title>
                <Badge color="blue">{todayEvents.length}</Badge>
              </Group>
              
              <ScrollArea h={200}>
                <Stack gap="sm">
                  {todayEvents.length > 0 ? (
                    todayEvents.map((event) => (
                      <Paper key={event.id} p="sm" withBorder>
                        <Group justify="space-between" align="flex-start">
                          <Group gap="xs">
                            <ActionIcon size="sm" color={event.color} variant="light">
                              {getEventTypeIcon(event.type)}
                            </ActionIcon>
                            <div>
                              <Text fw={500} size="sm">{event.title}</Text>
                              <Text size="xs" c="dimmed">{event.time}</Text>
                              {event.customerName && (
                                <Text size="xs" c="dimmed">{event.customerName}</Text>
                              )}
                            </div>
                          </Group>
                          <Badge color={getStatusColor(event.status)} size="xs">
                            {t(event.status)}
                          </Badge>
                        </Group>
                      </Paper>
                    ))
                  ) : (
                    <Text c="dimmed" size="sm" ta="center">{t('noEventsToday')}</Text>
                  )}
                </Stack>
              </ScrollArea>
            </Card>

            {/* Upcoming Events */}
            <Card withBorder>
              <Group justify="space-between" mb="md">
                <Title order={4}>{t('upcomingEvents')}</Title>
                <Badge color="orange">{upcomingEvents.length}</Badge>
              </Group>
              
              <ScrollArea h={250}>
                <Stack gap="sm">
                  {upcomingEvents.map((event) => (
                    <Paper key={event.id} p="sm" withBorder>
                      <Group justify="space-between" align="flex-start">
                        <Group gap="xs">
                          <ActionIcon size="sm" color={event.color} variant="light">
                            {getEventTypeIcon(event.type)}
                          </ActionIcon>
                          <div>
                            <Text fw={500} size="sm">{event.title}</Text>
                            <Text size="xs" c="dimmed">{event.date} at {event.time}</Text>
                            {event.customerName && (
                              <Text size="xs" c="dimmed">{event.customerName}</Text>
                            )}
                          </div>
                        </Group>
                        <Group gap="xs">
                          <Badge color={getPriorityColor(event.priority)} size="xs">
                            {t(event.priority)}
                          </Badge>
                          <Menu>
                            <Menu.Target>
                              <ActionIcon variant="light" size="sm">
                                <IconDots size={12} />
                              </ActionIcon>
                            </Menu.Target>
                            <Menu.Dropdown>
                              <Menu.Item 
                                leftSection={<IconEye size={14} />}
                                onClick={() => {
                                  setSelectedEvent(event)
                                  setEventModalOpen(true)
                                }}
                              >
                                {t('viewDetails')}
                              </Menu.Item>
                              <Menu.Item leftSection={<IconEdit size={14} />}>
                                {t('edit')}
                              </Menu.Item>
                            </Menu.Dropdown>
                          </Menu>
                        </Group>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </ScrollArea>
            </Card>

            {/* Quick Stats */}
            <Card withBorder>
              <Title order={4} mb="md">{t('quickStats')}</Title>
              
              <Stack gap="sm">
                <Group justify="space-between">
                  <Text size="sm">{t('pickupsToday')}</Text>
                  <Badge color="blue">{todayEvents.filter(e => e.type === 'pickup').length}</Badge>
                </Group>
                
                <Group justify="space-between">
                  <Text size="sm">{t('returnsToday')}</Text>
                  <Badge color="green">{todayEvents.filter(e => e.type === 'return').length}</Badge>
                </Group>
                
                <Group justify="space-between">
                  <Text size="sm">{t('maintenanceScheduled')}</Text>
                  <Badge color="orange">{events.filter(e => e.type === 'maintenance').length}</Badge>
                </Group>
                
                <Group justify="space-between">
                  <Text size="sm">{t('vehiclesBlocked')}</Text>
                  <Badge color="red">{events.filter(e => e.type === 'blocked').length}</Badge>
                </Group>
              </Stack>
            </Card>
          </Stack>
        </Grid.Col>
      </Grid>

      {/* Event Details Modal */}
      <Modal
        opened={eventModalOpen}
        onClose={() => setEventModalOpen(false)}
        title={t('eventDetails')}
        size="md"
      >
        {selectedEvent && (
          <Stack>
            <Group>
              <ActionIcon size="lg" color={selectedEvent.color} variant="light">
                {getEventTypeIcon(selectedEvent.type)}
              </ActionIcon>
              <div>
                <Title order={4}>{selectedEvent.title}</Title>
                <Text c="dimmed" size="sm">{selectedEvent.description}</Text>
              </div>
            </Group>

            <Divider />

            <Grid>
              <Grid.Col span={6}>
                <Text fw={500} size="sm">{t('date')}</Text>
                <Text size="sm">{selectedEvent.date}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text fw={500} size="sm">{t('time')}</Text>
                <Text size="sm">{selectedEvent.time}</Text>
              </Grid.Col>
            </Grid>

            {selectedEvent.vehicleName && (
              <div>
                <Text fw={500} size="sm">{t('vehicle')}</Text>
                <Text size="sm">{selectedEvent.vehicleName} ({selectedEvent.plateNumber})</Text>
              </div>
            )}

            {selectedEvent.customerName && (
              <div>
                <Text fw={500} size="sm">{t('customer')}</Text>
                <Text size="sm">{selectedEvent.customerName}</Text>
              </div>
            )}

            <div>
              <Text fw={500} size="sm">{t('location')}</Text>
              <Text size="sm">{selectedEvent.location}</Text>
            </div>

            <Group>
              <div>
                <Text fw={500} size="sm">{t('status')}</Text>
                <Badge color={getStatusColor(selectedEvent.status)}>
                  {t(selectedEvent.status)}
                </Badge>
              </div>
              <div>
                <Text fw={500} size="sm">{t('priority')}</Text>
                <Badge color={getPriorityColor(selectedEvent.priority)}>
                  {t(selectedEvent.priority)}
                </Badge>
              </div>
            </Group>

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setEventModalOpen(false)}>
                {t('close')}
              </Button>
              <Button variant="light">
                {t('edit')}
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Container>
  )
}
