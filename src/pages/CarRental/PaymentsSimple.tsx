import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  NumberInput,
  Progress
} from '@mantine/core'
import {
  IconPlus,
  IconSearch,IconEye,IconCreditCard,IconCurrencyDollar,
  IconClock,
  IconCheck,
  IconX,
  IconDownload,
  IconPrinter,
  IconAlertTriangle,
  IconRefresh
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface SimplePayment {
  id: string
  paymentNumber: string
  reservationNumber: string
  customerName: string
  vehicleName: string
  paymentMethod: 'cash' | 'card' | 'bank-transfer' | 'online'
  paymentType: 'deposit' | 'full-payment' | 'balance' | 'refund'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded'
  amount: number
  totalAmount: number
  paidAmount: number
  remainingAmount: number
  paymentDate: string
  notes: string
}

export function PaymentsSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [methodFilter, setMethodFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Simple mock data
  const payments: SimplePayment[] = [
    {
      id: '1',
      paymentNumber: 'PAY-2024-001',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      vehicleName: 'Toyota Camry 2023',
      paymentMethod: 'card',
      paymentType: 'deposit',
      status: 'completed',
      amount: 200,
      totalAmount: 600,
      paidAmount: 200,
      remainingAmount: 400,
      paymentDate: '2024-01-15',
      notes: 'Deposit payment for reservation'
    },
    {
      id: '2',
      paymentNumber: 'PAY-2024-002',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      vehicleName: 'BMW X5 2022',
      paymentMethod: 'bank-transfer',
      paymentType: 'full-payment',
      status: 'completed',
      amount: 800,
      totalAmount: 800,
      paidAmount: 800,
      remainingAmount: 0,
      paymentDate: '2024-01-10',
      notes: 'Full payment via bank transfer'
    },
    {
      id: '3',
      paymentNumber: 'PAY-2024-003',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      vehicleName: 'Mercedes C-Class 2023',
      paymentMethod: 'online',
      paymentType: 'deposit',
      status: 'pending',
      amount: 300,
      totalAmount: 900,
      paidAmount: 0,
      remainingAmount: 900,
      paymentDate: '2024-01-16',
      notes: 'Online payment pending confirmation'
    }
  ]

  const stats = [
    { label: t('totalPayments'), value: '$45,230', color: 'green', icon: IconCurrencyDollar },
    { label: t('pendingPayments'), value: '$3,450', color: 'orange', icon: IconClock },
    { label: t('completedToday'), value: '12', color: 'blue', icon: IconCheck },
    { label: t('failedPayments'), value: '2', color: 'red', icon: IconX }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'processing': return 'blue'
      case 'pending': return 'orange'
      case 'failed': return 'red'
      case 'refunded': return 'gray'
      default: return 'gray'
    }
  }

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'cash': return 'green'
      case 'card': return 'blue'
      case 'bank-transfer': return 'cyan'
      case 'online': return 'purple'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'yellow'
      case 'full-payment': return 'green'
      case 'balance': return 'blue'
      case 'refund': return 'red'
      default: return 'gray'
    }
  }

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.paymentNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || payment.status === statusFilter
    const matchesMethod = !methodFilter || payment.paymentMethod === methodFilter
    
    return matchesSearch && matchesStatus && matchesMethod
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('paymentsManagement')}
        description={t('trackAndManageAllPayments')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconRefresh size={16} />}>
              {t('syncPayments')}
            </Button>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportPayments')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('recordPayment')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="payments">{t('allPayments')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Payment Alerts */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('paymentAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('failedPayments')}</Text>
                    <Text size="xs">2 {t('paymentsRequireAttention')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('pendingPayments')}</Text>
                    <Text size="xs">5 {t('paymentsAwaitingProcessing')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCurrencyDollar size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('overduePayments')}</Text>
                    <Text size="xs">3 {t('paymentsOverdue')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Payment Methods */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('paymentMethods')}</Title>
                
                <Stack gap="sm">
                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconCreditCard size={16} color="var(--mantine-color-blue-6)" />
                      <Text size="sm">{t('creditCard')}</Text>
                    </Group>
                    <Text fw={700}>65%</Text>
                  </Group>
                  <Progress value={65} color="blue" size="sm" />

                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconCurrencyDollar size={16} color="var(--mantine-color-green-6)" />
                      <Text size="sm">{t('cash')}</Text>
                    </Group>
                    <Text fw={700}>20%</Text>
                  </Group>
                  <Progress value={20} color="green" size="sm" />

                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconCreditCard size={16} color="var(--mantine-color-cyan-6)" />
                      <Text size="sm">{t('bankTransfer')}</Text>
                    </Group>
                    <Text fw={700}>15%</Text>
                  </Group>
                  <Progress value={15} color="cyan" size="sm" />
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="payments" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <TextInput
                  placeholder={t('searchPayments')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'pending', label: t('pending') },
                    { value: 'processing', label: t('processing') },
                    { value: 'completed', label: t('completed') },
                    { value: 'failed', label: t('failed') },
                    { value: 'refunded', label: t('refunded') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allMethods')}
                  data={[
                    { value: '', label: t('allMethods') },
                    { value: 'cash', label: t('cash') },
                    { value: 'card', label: t('creditCard') },
                    { value: 'bank-transfer', label: t('bankTransfer') },
                    { value: 'online', label: t('onlinePayment') }
                  ]}
                  value={methodFilter}
                  onChange={(value) => setMethodFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Payments Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('payment')}</Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('reservation')}</Table.Th>
                  <Table.Th>{t('method')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredPayments.map((payment) => (
                  <Table.Tr key={payment.id}>
                    <Table.Td>
                      <div>
                        <Text fw={700} size="sm">{payment.paymentNumber}</Text>
                        <Text size="xs" c="dimmed">{payment.paymentDate}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{payment.customerName}</Text>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{payment.reservationNumber}</Text>
                        <Text size="xs" c="dimmed">{payment.vehicleName}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getMethodColor(payment.paymentMethod)} variant="light">
                        {t(payment.paymentMethod)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(payment.paymentType)} variant="light">
                        {t(payment.paymentType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(payment.status)}>
                        {t(payment.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={700}>${payment.amount}</Text>
                        <Text size="xs" c="dimmed">
                          ${payment.paidAmount} / ${payment.totalAmount}
                        </Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPrinter size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconRefresh size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconX size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('paymentAnalytics')}</Title>
            <Text c="dimmed">{t('analyticsWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Record Payment Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('recordPayment')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('reservation')}
            placeholder={t('selectReservation')}
            data={[
              { value: '1', label: 'RES-2024-001 - Ahmed Al-Rashid' },
              { value: '2', label: 'RES-2024-002 - Sarah Johnson' },
              { value: '3', label: 'RES-2024-003 - Mohammed Hassan' }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('paymentMethod')}
                placeholder={t('selectMethod')}
                data={[
                  { value: 'cash', label: t('cash') },
                  { value: 'card', label: t('creditCard') },
                  { value: 'bank-transfer', label: t('bankTransfer') },
                  { value: 'online', label: t('onlinePayment') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('paymentType')}
                placeholder={t('selectType')}
                data={[
                  { value: 'deposit', label: t('deposit') },
                  { value: 'full-payment', label: t('fullPayment') },
                  { value: 'balance', label: t('balance') },
                  { value: 'refund', label: t('refund') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <NumberInput
            label={t('amount')}
            placeholder={t('enterAmount')}
            prefix="$"
            min={0}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setAddModalOpen(false)}>
              {t('recordPayment')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
