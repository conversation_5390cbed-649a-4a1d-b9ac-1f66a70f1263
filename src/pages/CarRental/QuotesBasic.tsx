import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconCurrencyDollar,
  IconDownload,
  IconEdit,
  IconEye,
  IconPlus,
  IconSearch,
  IconSend,
  IconTrash
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface BasicQuote {
  id: string
  quoteNumber: string
  customerName: string
  vehicleName: string
  status: 'draft' | 'sent' | 'accepted' | 'rejected'
  totalAmount: number
  validUntil: string
  createdDate: string
}

export function QuotesBasic() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Simple mock data
  const quotes: BasicQuote[] = [
    {
      id: '1',
      quoteNumber: 'QUO-2024-001',
      customerName: 'Ahmed Al-Rashid',
      vehicleName: 'Toyota Camry 2023',
      status: 'sent',
      totalAmount: 600,
      validUntil: '2024-01-25',
      createdDate: '2024-01-15'
    },
    {
      id: '2',
      quoteNumber: 'QUO-2024-002',
      customerName: 'Sarah Johnson',
      vehicleName: 'BMW X5 2022',
      status: 'accepted',
      totalAmount: 800,
      validUntil: '2024-01-22',
      createdDate: '2024-01-10'
    },
    {
      id: '3',
      quoteNumber: 'QUO-2024-003',
      customerName: 'Mohammed Hassan',
      vehicleName: 'Mercedes C-Class 2023',
      status: 'draft',
      totalAmount: 900,
      validUntil: '2024-01-30',
      createdDate: '2024-01-16'
    }
  ]

  const stats = [
    { label: t('totalQuotes'), value: '89', color: 'blue', icon: IconCar},
    { label: t('pendingQuotes'), value: '12', color: 'orange', icon: IconCar},
    { label: t('acceptedQuotes'), value: '34', color: 'green', icon: IconCheck },
    { label: t('quoteValue'), value: '$67,890', color: 'red', icon: IconCurrencyDollar }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'green'
      case 'sent': return 'blue'
      case 'draft': return 'gray'
      case 'rejected': return 'red'
      default: return 'gray'
    }
  }

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.quoteNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || quote.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('quotesManagement')}</Title>
          <Text c="dimmed" size="sm">{t('generateAndManageRentalQuotes')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportQuotes')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
            {t('createQuote')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="quotes">{t('allQuotes')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Pending Actions */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('pendingActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('expiringSoon')}</Text>
                    <Text size="xs">3 {t('quotesExpiringIn24Hours')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconSend size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('awaitingResponse')}</Text>
                    <Text size="xs">8 {t('quotesAwaitingCustomerResponse')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCheck size={16} />} color="green">
                    <Text fw={500} size="sm">{t('readyToConvert')}</Text>
                    <Text size="xs">5 {t('acceptedQuotesReadyForReservation')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Quotes */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentQuotes')}</Title>
                
                <Stack gap="sm">
                  {quotes.slice(0, 3).map((quote) => (
                    <Paper key={quote.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text fw={500} size="sm">{quote.customerName}</Text>
                          <Text size="xs" c="dimmed">{quote.vehicleName}</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(quote.status)} size="sm">
                            {t(quote.status)}
                          </Badge>
                          <Text size="xs" c="dimmed">${quote.totalAmount}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="quotes" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchQuotes')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'draft', label: t('draft') },
                    { value: 'sent', label: t('sent') },
                    { value: 'accepted', label: t('accepted') },
                    { value: 'rejected', label: t('rejected') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Quotes Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('quote')}</Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('validUntil')}</Table.Th>
                  <Table.Th>{t('total')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredQuotes.map((quote) => (
                  <Table.Tr key={quote.id}>
                    <Table.Td>
                      <Text fw={700} size="sm">{quote.quoteNumber}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{quote.customerName}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{quote.vehicleName}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(quote.status)}>
                        {t(quote.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{quote.validUntil}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${quote.totalAmount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconSend size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Create Quote Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('createNewQuote')}
        size="md"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid' },
              { value: '2', label: 'Sarah Johnson' },
              { value: '3', label: 'Mohammed Hassan' }
            ]}
            required
          />

          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023' },
              { value: '2', label: 'BMW X5 2022' },
              { value: '3', label: 'Mercedes C-Class 2023' }
            ]}
            required
          />

          <TextInput
            label={t('totalAmount')}
            placeholder={t('enterAmount')}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconSend size={16} />} onClick={() => setAddModalOpen(false)}>
              {t('createQuote')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
