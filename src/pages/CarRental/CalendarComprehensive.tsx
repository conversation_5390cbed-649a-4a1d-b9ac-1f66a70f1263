import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Center,
  Alert,
  SimpleGrid,
  Paper,
  ScrollArea,
  Tooltip
} from '@mantine/core'
import {
  IconCalendar,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconTrash,
  IconChevronLeft,
  IconChevronRight,
  IconCalendarEvent,
  IconUser,
  IconMapPin,
  IconAlertTriangle,
  IconRefresh
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface CalendarReservation {
  id: string
  reservation_number: string
  customer_id: string
  customer_name: string
  customer_phone: string
  vehicle_id: string
  vehicle_name: string
  plate_number: string
  status: 'confirmed' | 'active' | 'completed' | 'cancelled' | 'pending'
  pickup_date: string
  return_date: string
  pickup_location: string
  return_location: string
  total_amount: number
  notes: string
}

interface CalendarDay {
  date: Date
  isCurrentMonth: boolean
  isToday: boolean
  reservations: CalendarReservation[]
}

export function CalendarComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [reservations, setReservations] = useState<CalendarReservation[]>([])
  const [activeTab, setActiveTab] = useState('month-view')
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [viewFilter, setViewFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  
  // Modal states
  const [quickBookModalOpen, setQuickBookModalOpen] = useState(false)
  const [dayViewModalOpen, setDayViewModalOpen] = useState(false)
  const [selectedReservation, setSelectedReservation] = useState<CalendarReservation | null>(null)

  // Mock data for calendar reservations
  const mockReservations: CalendarReservation[] = [
    {
      id: '1',
      reservation_number: 'RES-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_phone: '+971501234567',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      plate_number: 'ABC-123',
      status: 'confirmed',
      pickup_date: '2024-01-25T09:00:00Z',
      return_date: '2024-01-30T18:00:00Z',
      pickup_location: 'Dubai Airport',
      return_location: 'Dubai Airport',
      total_amount: 630,
      notes: 'Customer prefers white vehicle'
    },
    {
      id: '2',
      reservation_number: 'RES-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_phone: '+971507654321',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      plate_number: 'XYZ-456',
      status: 'active',
      pickup_date: '2024-01-22T10:00:00Z',
      return_date: '2024-01-26T17:00:00Z',
      pickup_location: 'Downtown Dubai',
      return_location: 'Downtown Dubai',
      total_amount: 1050,
      notes: 'Business customer - priority service'
    },
    {
      id: '3',
      reservation_number: 'RES-2024-003',
      customer_id: '3',
      customer_name: 'Mohammed Hassan',
      customer_phone: '+971509876543',
      vehicle_id: '3',
      vehicle_name: 'Mercedes C-Class 2023',
      plate_number: 'DEF-789',
      status: 'pending',
      pickup_date: '2024-01-28T14:00:00Z',
      return_date: '2024-02-02T12:00:00Z',
      pickup_location: 'Abu Dhabi',
      return_location: 'Abu Dhabi',
      total_amount: 945,
      notes: 'First time customer - requires verification'
    },
    {
      id: '4',
      reservation_number: 'RES-2024-004',
      customer_id: '4',
      customer_name: 'Fatima Al-Zahra',
      customer_phone: '+971502345678',
      vehicle_id: '4',
      vehicle_name: 'Nissan Altima 2023',
      plate_number: 'GHI-012',
      status: 'completed',
      pickup_date: '2024-01-15T11:00:00Z',
      return_date: '2024-01-20T16:00:00Z',
      pickup_location: 'Sharjah',
      return_location: 'Sharjah',
      total_amount: 525,
      notes: 'Regular customer - excellent history'
    }
  ]

  useEffect(() => {
    loadCalendarData()
  }, [currentDate])

  const loadCalendarData = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await reservationService.getReservationsByMonth(currentDate)
      setReservations(mockReservations)
    } catch (error) {
      console.error('Error loading calendar data:', error)
    } finally {
      setLoading(false)
    }
  }

  // Generate calendar days for the current month
  const generateCalendarDays = (): CalendarDay[] => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())
    
    const days: CalendarDay[] = []
    const today = new Date()
    
    for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
      const date = new Date(startDate)
      date.setDate(startDate.getDate() + i)
      
      const dayReservations = reservations.filter(reservation => {
        const pickupDate = new Date(reservation.pickup_date)
        const returnDate = new Date(reservation.return_date)
        return date >= pickupDate && date <= returnDate
      })
      
      days.push({
        date: new Date(date),
        isCurrentMonth: date.getMonth() === month,
        isToday: date.toDateString() === today.toDateString(),
        reservations: dayReservations
      })
    }
    
    return days
  }

  const calendarDays = generateCalendarDays()

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
  }

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
  }

  const goToToday = () => {
    setCurrentDate(new Date())
  }

  // Status colors
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'confirmed': return 'blue'
      case 'active': return 'green'
      case 'completed': return 'teal'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  // Statistics
  const monthReservations = reservations.filter(r => {
    const pickupDate = new Date(r.pickup_date)
    return pickupDate.getMonth() === currentDate.getMonth() && 
           pickupDate.getFullYear() === currentDate.getFullYear()
  })

  const stats = [
    {
      label: t('monthlyReservations'),
      value: monthReservations.length.toString(),
      color: 'blue',
      icon: IconCalendarEvent
    },
    {
      label: t('activeRentals'),
      value: reservations.filter(r => r.status === 'active').length.toString(),
      color: 'green',
      icon: IconCar
    },
    {
      label: t('pendingConfirmation'),
      value: reservations.filter(r => r.status === 'pending').length.toString(),
      color: 'orange',
      icon: IconClock
    },
    {
      label: t('monthlyRevenue'),
      value: `$${monthReservations.reduce((sum, r) => sum + r.total_amount, 0).toFixed(0)}`,
      color: 'teal',
      icon: IconCheck
    }
  ]

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('calendarManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageReservationsAndAvailability')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setQuickBookModalOpen(true)}
              >
                {t('quickBook')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="month-view">{t('monthView')}</Tabs.Tab>
            <Tabs.Tab value="week-view">{t('weekView')}</Tabs.Tab>
            <Tabs.Tab value="day-view">{t('dayView')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="month-view">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Calendar Navigation */}
            <Card withBorder mb="lg">
              <Group justify="space-between">
                <Group>
                  <ActionIcon variant="light" onClick={goToPreviousMonth}>
                    <IconChevronLeft size={16} />
                  </ActionIcon>
                  <Title order={3}>
                    {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                  </Title>
                  <ActionIcon variant="light" onClick={goToNextMonth}>
                    <IconChevronRight size={16} />
                  </ActionIcon>
                </Group>
                <Group>
                  <Button variant="light" size="sm" onClick={goToToday}>
                    {t('today')}
                  </Button>
                  <Select
                    placeholder={t('filterByStatus')}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'pending', label: t('pending') },
                      { value: 'confirmed', label: t('confirmed') },
                      { value: 'active', label: t('active') },
                      { value: 'completed', label: t('completed') },
                      { value: 'cancelled', label: t('cancelled') }
                    ]}
                    size="sm"
                    w={150}
                    clearable
                  />
                  <Select
                    placeholder={t('filterByLocation')}
                    value={locationFilter}
                    onChange={(value) => setLocationFilter(value || '')}
                    data={[
                      { value: '', label: t('allLocations') },
                      { value: 'dubai-airport', label: 'Dubai Airport' },
                      { value: 'downtown-dubai', label: 'Downtown Dubai' },
                      { value: 'abu-dhabi', label: 'Abu Dhabi' },
                      { value: 'sharjah', label: 'Sharjah' }
                    ]}
                    size="sm"
                    w={150}
                    clearable
                  />
                </Group>
              </Group>
            </Card>

            {/* Calendar Grid */}
            <Card withBorder>
              {/* Calendar Header */}
              <Grid mb="sm">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                  <Grid.Col key={day} span={12/7}>
                    <Text fw={700} ta="center" size="sm" c="dimmed">
                      {t(day.toLowerCase())}
                    </Text>
                  </Grid.Col>
                ))}
              </Grid>

              {/* Calendar Days */}
              <SimpleGrid cols={7} spacing="xs">
                {calendarDays.map((day, index) => (
                  <Paper
                    key={index}
                    p="xs"
                    h={120}
                    withBorder
                    style={{
                      backgroundColor: day.isToday ? 'var(--mantine-color-blue-0)' :
                                     !day.isCurrentMonth ? 'var(--mantine-color-gray-0)' : 'white',
                      cursor: 'pointer',
                      opacity: day.isCurrentMonth ? 1 : 0.5
                    }}
                    onClick={() => {
                      setSelectedDate(day.date)
                      setDayViewModalOpen(true)
                    }}
                  >
                    <Stack gap="xs" h="100%">
                      <Group justify="space-between">
                        <Text
                          size="sm"
                          fw={day.isToday ? 700 : 500}
                          c={day.isToday ? 'blue' : day.isCurrentMonth ? 'dark' : 'dimmed'}
                        >
                          {day.date.getDate()}
                        </Text>
                        {day.reservations.length > 0 && (
                          <Badge size="xs" color="blue">
                            {day.reservations.length}
                          </Badge>
                        )}
                      </Group>

                      <ScrollArea h={80}>
                        <Stack gap={2}>
                          {day.reservations.slice(0, 3).map((reservation) => (
                            <Tooltip
                              key={reservation.id}
                              label={`${reservation.customer_name} - ${reservation.vehicle_name}`}
                              position="top"
                            >
                              <Paper
                                p={2}
                                style={{
                                  backgroundColor: `var(--mantine-color-${getStatusColor(reservation.status)}-1)`,
                                  borderLeft: `3px solid var(--mantine-color-${getStatusColor(reservation.status)}-6)`
                                }}
                              >
                                <Text size="xs" truncate>
                                  {reservation.customer_name}
                                </Text>
                                <Text size="xs" c="dimmed" truncate>
                                  {reservation.vehicle_name}
                                </Text>
                              </Paper>
                            </Tooltip>
                          ))}
                          {day.reservations.length > 3 && (
                            <Text size="xs" c="dimmed" ta="center">
                              +{day.reservations.length - 3} {t('more')}
                            </Text>
                          )}
                        </Stack>
                      </ScrollArea>
                    </Stack>
                  </Paper>
                ))}
              </SimpleGrid>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="week-view">
            <Card withBorder>
              <Center py="xl">
                <Stack align="center">
                  <IconCalendarEvent size={48} color="var(--mantine-color-gray-5)" />
                  <Title order={4} c="dimmed">{t('weekView')}</Title>
                  <Text c="dimmed" ta="center">
                    {t('weekViewWillBeImplementedHere')}
                  </Text>
                </Stack>
              </Center>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="day-view">
            <Card withBorder>
              <Center py="xl">
                <Stack align="center">
                  <IconCalendarEvent size={48} color="var(--mantine-color-gray-5)" />
                  <Title order={4} c="dimmed">{t('dayView')}</Title>
                  <Text c="dimmed" ta="center">
                    {t('dayViewWillBeImplementedHere')}
                  </Text>
                </Stack>
              </Center>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* Quick Book Modal */}
        <Modal
          opened={quickBookModalOpen}
          onClose={() => setQuickBookModalOpen(false)}
          title={t('quickBook')}
          size="lg"
        >
          <Stack>
            <Alert color="blue" icon={<IconCalendarEvent size={16} />}>
              <Text fw={500}>{t('quickBookingFromCalendar')}</Text>
              <Text size="sm">{t('createNewReservationQuickly')}</Text>
            </Alert>

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('customer')}
                  placeholder={t('selectCustomer')}
                  data={[
                    { value: '1', label: 'Ahmed Al-Rashid' },
                    { value: '2', label: 'Sarah Johnson' },
                    { value: '3', label: 'Mohammed Hassan' },
                    { value: '4', label: 'Fatima Al-Zahra' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('vehicle')}
                  placeholder={t('selectVehicle')}
                  data={[
                    { value: '1', label: 'Toyota Camry 2023 (ABC-123)' },
                    { value: '2', label: 'BMW X5 2022 (XYZ-456)' },
                    { value: '3', label: 'Mercedes C-Class 2023 (DEF-789)' },
                    { value: '4', label: 'Nissan Altima 2023 (GHI-012)' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('pickupDate')}
                  placeholder={t('selectPickupDate')}
                  type="datetime-local"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('returnDate')}
                  placeholder={t('selectReturnDate')}
                  type="datetime-local"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('pickupLocation')}
                  placeholder={t('selectPickupLocation')}
                  data={[
                    { value: 'dubai-airport', label: 'Dubai Airport' },
                    { value: 'downtown-dubai', label: 'Downtown Dubai' },
                    { value: 'abu-dhabi', label: 'Abu Dhabi' },
                    { value: 'sharjah', label: 'Sharjah' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('returnLocation')}
                  placeholder={t('selectReturnLocation')}
                  data={[
                    { value: 'dubai-airport', label: 'Dubai Airport' },
                    { value: 'downtown-dubai', label: 'Downtown Dubai' },
                    { value: 'abu-dhabi', label: 'Abu Dhabi' },
                    { value: 'sharjah', label: 'Sharjah' }
                  ]}
                  required
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setQuickBookModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconCheck size={16} />} onClick={() => setQuickBookModalOpen(false)}>
                {t('createReservation')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Day View Modal */}
        <Modal
          opened={dayViewModalOpen}
          onClose={() => setDayViewModalOpen(false)}
          title={selectedDate ? selectedDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }) : t('dayView')}
          size="lg"
        >
          {selectedDate && (
            <Stack>
              {(() => {
                const dayReservations = reservations.filter(reservation => {
                  const pickupDate = new Date(reservation.pickup_date)
                  const returnDate = new Date(reservation.return_date)
                  return selectedDate >= pickupDate && selectedDate <= returnDate
                })

                if (dayReservations.length === 0) {
                  return (
                    <Center py="xl">
                      <Stack align="center">
                        <IconCalendarEvent size={48} color="var(--mantine-color-gray-5)" />
                        <Text c="dimmed">{t('noReservationsForThisDay')}</Text>
                        <Button
                          leftSection={<IconPlus size={16} />}
                          onClick={() => {
                            setDayViewModalOpen(false)
                            setQuickBookModalOpen(true)
                          }}
                        >
                          {t('addReservation')}
                        </Button>
                      </Stack>
                    </Center>
                  )
                }

                return (
                  <Stack>
                    <Group justify="space-between">
                      <Text fw={500}>{dayReservations.length} {t('reservationsForThisDay')}</Text>
                      <Button
                        size="sm"
                        leftSection={<IconPlus size={16} />}
                        onClick={() => {
                          setDayViewModalOpen(false)
                          setQuickBookModalOpen(true)
                        }}
                      >
                        {t('addReservation')}
                      </Button>
                    </Group>

                    {dayReservations.map((reservation) => (
                      <Card key={reservation.id} withBorder p="md">
                        <Grid>
                          <Grid.Col span={8}>
                            <Group>
                              <Avatar size="sm" color="blue">
                                {reservation.customer_name.charAt(0)}
                              </Avatar>
                              <div>
                                <Text fw={500}>{reservation.customer_name}</Text>
                                <Text size="sm" c="dimmed">{reservation.customer_phone}</Text>
                              </div>
                            </Group>
                          </Grid.Col>
                          <Grid.Col span={4}>
                            <Badge color={getStatusColor(reservation.status)} size="sm">
                              {t(reservation.status)}
                            </Badge>
                          </Grid.Col>
                          <Grid.Col span={6}>
                            <Group gap="xs">
                              <IconCar size={16} color="var(--mantine-color-gray-6)" />
                              <div>
                                <Text size="sm" fw={500}>{reservation.vehicle_name}</Text>
                                <Text size="xs" c="dimmed">{reservation.plate_number}</Text>
                              </div>
                            </Group>
                          </Grid.Col>
                          <Grid.Col span={6}>
                            <Group gap="xs">
                              <IconMapPin size={16} color="var(--mantine-color-gray-6)" />
                              <div>
                                <Text size="sm">{reservation.pickup_location}</Text>
                                <Text size="xs" c="dimmed">{reservation.return_location}</Text>
                              </div>
                            </Group>
                          </Grid.Col>
                          <Grid.Col span={6}>
                            <Text size="xs" c="dimmed">{t('pickup')}</Text>
                            <Text size="sm">
                              {new Date(reservation.pickup_date).toLocaleDateString()} {new Date(reservation.pickup_date).toLocaleTimeString()}
                            </Text>
                          </Grid.Col>
                          <Grid.Col span={6}>
                            <Text size="xs" c="dimmed">{t('return')}</Text>
                            <Text size="sm">
                              {new Date(reservation.return_date).toLocaleDateString()} {new Date(reservation.return_date).toLocaleTimeString()}
                            </Text>
                          </Grid.Col>
                          <Grid.Col span={12}>
                            <Group justify="space-between">
                              <Text fw={700}>${reservation.total_amount}</Text>
                              <Group gap="xs">
                                <ActionIcon variant="light" size="sm">
                                  <IconEye size={16} />
                                </ActionIcon>
                                <ActionIcon variant="light" size="sm" color="blue">
                                  <IconEdit size={16} />
                                </ActionIcon>
                              </Group>
                            </Group>
                          </Grid.Col>
                          {reservation.notes && (
                            <Grid.Col span={12}>
                              <Text size="xs" c="dimmed">{t('notes')}: {reservation.notes}</Text>
                            </Grid.Col>
                          )}
                        </Grid>
                      </Card>
                    ))}
                  </Stack>
                )
              })()}
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
