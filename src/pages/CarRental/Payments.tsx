import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  NumberInput,
  Textarea,Divider,
  Alert,
  Tabs,
  Progress,
  Avatar,
  Menu} from '@mantine/core'
import {
  IconAlertTriangle,
  IconArrowDown,
  IconArrowUp,
  IconBan,
  IconCar,
  IconCurrencyDollar,
  IconDots,
  IconDownload,
  IconEye,
  IconPlus,
  IconPrinter,
  IconReceipt,
  IconRefresh,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface Payment {
  id: string
  paymentId: string
  reservationId: string
  reservationNumber: string
  customerId: string
  customerName: string
  paymentType: 'deposit' | 'full' | 'partial' | 'refund' | 'penalty'
  method: 'credit-card' | 'debit-card' | 'cash' | 'bank-transfer' | 'online'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded'
  amount: number
  currency: string
  transactionId?: string
  gatewayResponse?: string
  processedDate?: string
  dueDate?: string
  description: string
  notes: string
  createdDate: string
  refundAmount?: number
  fees: number
}

export function Payments() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [methodFilter, setMethodFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Mock data
  const payments: Payment[] = [
    {
      id: '1',
      paymentId: 'PAY-2024-001',
      reservationId: '1',
      reservationNumber: 'RES-2024-001',
      customerId: '1',
      customerName: 'Ahmed Al-Rashid',
      paymentType: 'deposit',
      method: 'credit-card',
      status: 'completed',
      amount: 200,
      currency: 'USD',
      transactionId: 'TXN-ABC123',
      gatewayResponse: 'Approved',
      processedDate: '2024-01-15',
      description: 'Reservation deposit payment',
      notes: 'Visa ending in 1234',
      createdDate: '2024-01-15',
      fees: 6
    },
    {
      id: '2',
      paymentId: 'PAY-2024-002',
      reservationId: '2',
      reservationNumber: 'RES-2024-002',
      customerId: '2',
      customerName: 'Sarah Johnson',
      paymentType: 'full',
      method: 'online',
      status: 'completed',
      amount: 800,
      currency: 'USD',
      transactionId: 'TXN-DEF456',
      gatewayResponse: 'Approved',
      processedDate: '2024-01-10',
      description: 'Full rental payment',
      notes: 'PayPal payment',
      createdDate: '2024-01-10',
      fees: 24
    },
    {
      id: '3',
      paymentId: 'PAY-2024-003',
      reservationId: '3',
      reservationNumber: 'RES-2024-003',
      customerId: '3',
      customerName: 'Mohammed Hassan',
      paymentType: 'partial',
      method: 'bank-transfer',
      status: 'pending',
      amount: 450,
      currency: 'USD',
      dueDate: '2024-01-20',
      description: 'Partial payment for reservation',
      notes: 'Bank transfer pending verification',
      createdDate: '2024-01-16',
      fees: 0
    },
    {
      id: '4',
      paymentId: 'PAY-2024-004',
      reservationId: '2',
      reservationNumber: 'RES-2024-002',
      customerId: '2',
      customerName: 'Sarah Johnson',
      paymentType: 'refund',
      method: 'credit-card',
      status: 'processing',
      amount: 100,
      currency: 'USD',
      transactionId: 'TXN-REF789',
      description: 'Partial refund for early return',
      notes: 'Refund to original payment method',
      createdDate: '2024-01-18',
      refundAmount: 100,
      fees: 3
    }
  ]

  const stats = [
    { label: t('totalPayments'), value: '$45,230', color: 'green', icon: IconCurrencyDollar },
    { label: t('pendingPayments'), value: '$2,450', color: 'orange', icon: IconCar},
    { label: t('completedToday'), value: '12', color: 'blue', icon: IconCar},
    { label: t('failedPayments'), value: '3', color: 'red', icon: IconCar}
  ]

  const recentActivity = [
    { type: 'payment', customer: 'Ahmed Al-Rashid', amount: '$200', status: 'completed', time: '10:30 AM' },
    { type: 'refund', customer: 'Sarah Johnson', amount: '$100', status: 'processing', time: '09:15 AM' },
    { type: 'payment', customer: 'Mohammed Hassan', amount: '$450', status: 'pending', time: '08:45 AM' }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'processing': return 'blue'
      case 'pending': return 'orange'
      case 'failed': return 'red'
      case 'cancelled': return 'gray'
      case 'refunded': return 'purple'
      default: return 'gray'
    }
  }

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'credit-card': return 'blue'
      case 'debit-card': return 'cyan'
      case 'cash': return 'green'
      case 'bank-transfer': return 'purple'
      case 'online': return 'orange'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'blue'
      case 'full': return 'green'
      case 'partial': return 'yellow'
      case 'refund': return 'purple'
      case 'penalty': return 'red'
      default: return 'gray'
    }
  }

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.paymentId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.transactionId?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || payment.status === statusFilter
    const matchesMethod = !methodFilter || payment.method === methodFilter
    const matchesType = !typeFilter || payment.paymentType === typeFilter
    
    return matchesSearch && matchesStatus && matchesMethod && matchesType
  })

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('paymentsManagement')}</Title>
          <Text c="dimmed" size="sm">{t('trackAndManageAllPayments')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportPayments')}
          </Button>
          <Button variant="light" leftSection={<IconRefresh size={16} />}>
            {t('syncPayments')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
            {t('recordPayment')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="payments">{t('allPayments')}</Tabs.Tab>
          <Tabs.Tab value="pending">{t('pendingPayments')}</Tabs.Tab>
          <Tabs.Tab value="refunds">{t('refunds')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Recent Activity */}
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('recentPaymentActivity')}</Title>
                  <Button variant="light" size="sm">
                    {t('viewAll')}
                  </Button>
                </Group>
                
                <Stack gap="sm">
                  {recentActivity.map((activity, index) => (
                    <Paper key={index} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group gap="sm">
                          <ActionIcon
                            variant="light"
                            color={activity.type === 'payment' ? 'green' : 'blue'}
                            size="sm"
                          >
                            {activity.type === 'payment' ? <IconArrowDown size={14} /> : <IconArrowUp size={14} />}
                          </ActionIcon>
                          <div>
                            <Text fw={500} size="sm">
                              {activity.type === 'payment' ? t('paymentReceived') : t('refundProcessed')}
                            </Text>
                            <Text size="xs" c="dimmed">{activity.customer}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Text fw={700} size="sm">{activity.amount}</Text>
                          <Badge color={getStatusColor(activity.status)} size="xs">
                            {t(activity.status)}
                          </Badge>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>

            {/* Payment Summary */}
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('paymentSummary')}</Title>
                
                <Stack gap="md">
                  <div>
                    <Group justify="space-between" mb="xs">
                      <Text size="sm">{t('totalReceived')}</Text>
                      <Text fw={700} c="green">$42,780</Text>
                    </Group>
                    <Progress value={85} color="green" size="sm" />
                  </div>
                  
                  <div>
                    <Group justify="space-between" mb="xs">
                      <Text size="sm">{t('pendingAmount')}</Text>
                      <Text fw={700} c="orange">$2,450</Text>
                    </Group>
                    <Progress value={15} color="orange" size="sm" />
                  </div>
                  
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('overduePayments')}</Text>
                    <Text size="xs">2 {t('paymentsOverdue')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="payments" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <TextInput
                  placeholder={t('searchPayments')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'pending', label: t('pending') },
                    { value: 'processing', label: t('processing') },
                    { value: 'completed', label: t('completed') },
                    { value: 'failed', label: t('failed') },
                    { value: 'cancelled', label: t('cancelled') },
                    { value: 'refunded', label: t('refunded') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Select
                  placeholder={t('allMethods')}
                  data={[
                    { value: '', label: t('allMethods') },
                    { value: 'credit-card', label: t('creditCard') },
                    { value: 'debit-card', label: t('debitCard') },
                    { value: 'cash', label: t('cash') },
                    { value: 'bank-transfer', label: t('bankTransfer') },
                    { value: 'online', label: t('online') }
                  ]}
                  value={methodFilter}
                  onChange={(value) => setMethodFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Select
                  placeholder={t('allTypes')}
                  data={[
                    { value: '', label: t('allTypes') },
                    { value: 'deposit', label: t('deposit') },
                    { value: 'full', label: t('fullPayment') },
                    { value: 'partial', label: t('partialPayment') },
                    { value: 'refund', label: t('refund') },
                    { value: 'penalty', label: t('penalty') }
                  ]}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Group>
                  <Button variant="light" size="sm" leftSection={<IconDownload size={14} />}>
                    {t('export')}
                  </Button>
                  <Button variant="light" size="sm" leftSection={<IconRefresh size={14} />}>
                    {t('refresh')}
                  </Button>
                </Group>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Payments Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('payment')}</Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('reservation')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('method')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('date')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredPayments.map((payment) => (
                  <Table.Tr key={payment.id}>
                    <Table.Td>
                      <div>
                        <Text fw={700} size="sm">{payment.paymentId}</Text>
                        {payment.transactionId && (
                          <Text size="xs" c="dimmed">{payment.transactionId}</Text>
                        )}
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="sm">
                        <Avatar size="sm" color="blue">
                          {payment.customerName.charAt(0)}
                        </Avatar>
                        <Text fw={500} size="sm">{payment.customerName}</Text>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{payment.reservationNumber}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(payment.paymentType)} variant="light">
                        {t(payment.paymentType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getMethodColor(payment.method)} variant="light">
                        {t(payment.method)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(payment.status)}>
                        {t(payment.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${payment.amount}</Text>
                      {payment.fees > 0 && (
                        <Text size="xs" c="dimmed">Fee: ${payment.fees}</Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{payment.processedDate || payment.createdDate}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconReceipt size={14} />
                        </ActionIcon>
                        <Menu>
                          <Menu.Target>
                            <ActionIcon variant="light" size="sm">
                              <IconDots size={14} />
                            </ActionIcon>
                          </Menu.Target>
                          <Menu.Dropdown>
                            <Menu.Item leftSection={<IconPrinter size={14} />}>
                              {t('printReceipt')}
                            </Menu.Item>
                            <Menu.Item leftSection={<IconDownload size={14} />}>
                              {t('downloadReceipt')}
                            </Menu.Item>
                            {payment.status === 'completed' && payment.paymentType !== 'refund' && (
                              <Menu.Item leftSection={<IconRefresh size={14} />}>
                                {t('processRefund')}
                              </Menu.Item>
                            )}
                            {payment.status === 'pending' && (
                              <Menu.Item leftSection={<IconBan size={14} />} color="red">
                                {t('cancelPayment')}
                              </Menu.Item>
                            )}
                          </Menu.Dropdown>
                        </Menu>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="pending" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('pendingPayments')}</Title>
            <Text c="dimmed">{t('pendingPaymentsWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="refunds" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('refundsManagement')}</Title>
            <Text c="dimmed">{t('refundsManagementWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('paymentAnalytics')}</Title>
                <Text c="dimmed">{t('paymentAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
            
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('revenueAnalytics')}</Title>
                <Text c="dimmed">{t('revenueAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>

      {/* Record Payment Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('recordPayment')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('reservation')}
            placeholder={t('selectReservation')}
            data={[
              { value: '1', label: 'RES-2024-001 - Ahmed Al-Rashid' },
              { value: '2', label: 'RES-2024-002 - Sarah Johnson' },
              { value: '3', label: 'RES-2024-003 - Mohammed Hassan' }
            ]}
            searchable
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('paymentType')}
                placeholder={t('selectPaymentType')}
                data={[
                  { value: 'deposit', label: t('deposit') },
                  { value: 'full', label: t('fullPayment') },
                  { value: 'partial', label: t('partialPayment') },
                  { value: 'penalty', label: t('penalty') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('paymentMethod')}
                placeholder={t('selectPaymentMethod')}
                data={[
                  { value: 'credit-card', label: t('creditCard') },
                  { value: 'debit-card', label: t('debitCard') },
                  { value: 'cash', label: t('cash') },
                  { value: 'bank-transfer', label: t('bankTransfer') },
                  { value: 'online', label: t('online') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <NumberInput
                label={t('amount')}
                placeholder={t('enterAmount')}
                prefix="$"
                min={0}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('transactionId')}
                placeholder={t('enterTransactionId')}
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('notes')}
            placeholder={t('enterPaymentNotes')}
            rows={3}
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={() => setAddModalOpen(false)}>
              {t('recordPayment')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
