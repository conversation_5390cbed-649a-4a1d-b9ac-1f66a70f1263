import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Center
} from '@mantine/core'
import {
  IconCalendar,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconTrash,
  IconMapPin,
  IconCreditCard,
  IconCalendarEvent
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface ReservationTableData {
  id: string
  reservation_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  vehicle_id: string
  vehicle_name: string
  plate_number: string
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'no-show'
  pickup_date: string
  return_date: string
  pickup_location: string
  return_location: string
  total_days: number
  daily_rate: number
  subtotal: number
  tax_amount: number
  total_amount: number
  paid_amount: number
  payment_status: 'pending' | 'partial' | 'paid' | 'refunded'
  security_deposit: number
  special_requests: string
  notes: string
  created_at: string
  updated_at: string
}

export function ReservationsComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [reservations, setReservations] = useState<ReservationTableData[]>([])
  const [activeTab, setActiveTab] = useState('all-reservations')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [paymentFilter, setPaymentFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [selectedReservation, setSelectedReservation] = useState<ReservationTableData | null>(null)

  // Form states
  const [formData, setFormData] = useState({
    customer_id: '',
    vehicle_id: '',
    pickup_date: '',
    return_date: '',
    pickup_location: '',
    return_location: '',
    daily_rate: 0,
    security_deposit: 0,
    special_requests: '',
    notes: ''
  })

  // Mock data for development - matches database schema
  const mockReservations: ReservationTableData[] = [
    {
      id: '1',
      reservation_number: 'RES-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971501234567',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      plate_number: 'ABC-123',
      status: 'confirmed',
      pickup_date: '2024-01-20',
      return_date: '2024-01-25',
      pickup_location: 'Dubai Airport',
      return_location: 'Dubai Airport',
      total_days: 5,
      daily_rate: 120,
      subtotal: 600,
      tax_amount: 30,
      total_amount: 630,
      paid_amount: 200,
      payment_status: 'partial',
      security_deposit: 500,
      special_requests: 'GPS Navigation, Child Seat',
      notes: 'Customer prefers white vehicle',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-20T14:30:00Z'
    },
    {
      id: '2',
      reservation_number: 'RES-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+971507654321',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      plate_number: 'XYZ-456',
      status: 'active',
      pickup_date: '2024-01-18',
      return_date: '2024-01-22',
      pickup_location: 'Downtown Dubai',
      return_location: 'Downtown Dubai',
      total_days: 4,
      daily_rate: 200,
      subtotal: 800,
      tax_amount: 40,
      total_amount: 840,
      paid_amount: 840,
      payment_status: 'paid',
      security_deposit: 800,
      special_requests: 'Additional Driver',
      notes: 'Business customer - priority service',
      created_at: '2024-01-10T09:00:00Z',
      updated_at: '2024-01-18T11:15:00Z'
    },
    {
      id: '3',
      reservation_number: 'RES-2024-003',
      customer_id: '3',
      customer_name: 'Mohammed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+971509876543',
      vehicle_id: '3',
      vehicle_name: 'Mercedes C-Class 2023',
      plate_number: 'DEF-789',
      status: 'pending',
      pickup_date: '2024-01-25',
      return_date: '2024-01-30',
      pickup_location: 'Abu Dhabi',
      return_location: 'Abu Dhabi',
      total_days: 5,
      daily_rate: 180,
      subtotal: 900,
      tax_amount: 45,
      total_amount: 945,
      paid_amount: 0,
      payment_status: 'pending',
      security_deposit: 700,
      special_requests: 'Insurance Coverage',
      notes: 'First time customer - requires verification',
      created_at: '2024-01-16T14:00:00Z',
      updated_at: '2024-01-20T16:45:00Z'
    },
    {
      id: '4',
      reservation_number: 'RES-2024-004',
      customer_id: '4',
      customer_name: 'Fatima Al-Zahra',
      customer_email: '<EMAIL>',
      customer_phone: '+971502345678',
      vehicle_id: '4',
      vehicle_name: 'Nissan Altima 2023',
      plate_number: 'GHI-012',
      status: 'completed',
      pickup_date: '2024-01-10',
      return_date: '2024-01-15',
      pickup_location: 'Sharjah',
      return_location: 'Sharjah',
      total_days: 5,
      daily_rate: 100,
      subtotal: 500,
      tax_amount: 25,
      total_amount: 525,
      paid_amount: 525,
      payment_status: 'paid',
      security_deposit: 400,
      special_requests: 'Bluetooth, USB Charger',
      notes: 'Regular customer - excellent payment history',
      created_at: '2024-01-05T12:00:00Z',
      updated_at: '2024-01-15T18:30:00Z'
    }
  ]

  useEffect(() => {
    loadReservations()
  }, [])

  const loadReservations = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await reservationService.getAllReservations()
      setReservations(mockReservations)
    } catch (error) {
      console.error('Error loading reservations:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredReservations = reservations.filter(reservation => {
    const matchesSearch = !searchQuery || 
      reservation.reservation_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reservation.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reservation.vehicle_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      reservation.plate_number.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || reservation.status === statusFilter
    const matchesLocation = !locationFilter || 
      reservation.pickup_location.toLowerCase().includes(locationFilter.toLowerCase())
    const matchesPayment = !paymentFilter || reservation.payment_status === paymentFilter
    
    return matchesSearch && matchesStatus && matchesLocation && matchesPayment
  })

  // Pagination
  const totalItems = filteredReservations.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedReservations = filteredReservations.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const stats = [
    {
      label: t('totalReservations'),
      value: reservations.length.toString(),
      color: 'blue',
      icon: IconCalendar
    },
    {
      label: t('activeReservations'),
      value: reservations.filter(r => r.status === 'active').length.toString(),
      color: 'green',
      icon: IconCar
    },
    {
      label: t('pendingConfirmation'),
      value: reservations.filter(r => r.status === 'pending').length.toString(),
      color: 'orange',
      icon: IconClock
    },
    {
      label: t('completedToday'),
      value: reservations.filter(r => r.status === 'completed' && 
        new Date(r.return_date).toDateString() === new Date().toDateString()).length.toString(),
      color: 'teal',
      icon: IconCheck
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'confirmed': return 'blue'
      case 'active': return 'green'
      case 'completed': return 'teal'
      case 'cancelled': return 'red'
      case 'no-show': return 'gray'
      default: return 'gray'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'orange'
      case 'partial': return 'yellow'
      case 'paid': return 'green'
      case 'refunded': return 'blue'
      default: return 'gray'
    }
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('reservationManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageAllReservationsAndBookings')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setAddModalOpen(true)}
              >
                {t('addReservation')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-reservations">{t('allReservations')}</Tabs.Tab>
            <Tabs.Tab value="calendar">{t('calendar')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-reservations">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <TextInput
                    placeholder={t('searchReservations')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'pending', label: t('pending') },
                      { value: 'confirmed', label: t('confirmed') },
                      { value: 'active', label: t('active') },
                      { value: 'completed', label: t('completed') },
                      { value: 'cancelled', label: t('cancelled') },
                      { value: 'no-show', label: t('noShow') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('paymentStatus')}
                    leftSection={<IconCreditCard size={16} />}
                    value={paymentFilter}
                    onChange={(value) => setPaymentFilter(value || '')}
                    data={[
                      { value: '', label: t('allPayments') },
                      { value: 'pending', label: t('pending') },
                      { value: 'partial', label: t('partial') },
                      { value: 'paid', label: t('paid') },
                      { value: 'refunded', label: t('refunded') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <TextInput
                    placeholder={t('location')}
                    leftSection={<IconMapPin size={16} />}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setLocationFilter('')
                      setPaymentFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Reservations Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('reservation')}</Table.Th>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('vehicle')}</Table.Th>
                    <Table.Th>{t('dates')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('payment')}</Table.Th>
                    <Table.Th>{t('total')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedReservations.map((reservation) => (
                    <Table.Tr key={reservation.id}>
                      <Table.Td>
                        <div>
                          <Text fw={700} size="sm">{reservation.reservation_number}</Text>
                          <Text size="xs" c="dimmed">
                            {new Date(reservation.created_at).toLocaleDateString()}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color="blue">
                            {reservation.customer_name.charAt(0)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{reservation.customer_name}</Text>
                            <Text size="xs" c="dimmed">{reservation.customer_email}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{reservation.vehicle_name}</Text>
                          <Text size="xs" c="dimmed">{reservation.plate_number}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">
                            {new Date(reservation.pickup_date).toLocaleDateString()} - {new Date(reservation.return_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">{reservation.total_days} {t('days')}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(reservation.status)} size="sm">
                          {t(reservation.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Badge color={getPaymentStatusColor(reservation.payment_status)} size="sm">
                            {t(reservation.payment_status)}
                          </Badge>
                          <Text size="xs" c="dimmed">
                            ${reservation.paid_amount} / ${reservation.total_amount}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={700} size="sm">${reservation.total_amount}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedReservation(reservation)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedReservation(reservation)
                              setEditModalOpen(true)
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="red"
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('reservations')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="calendar">
            <Card withBorder>
              <Center py="xl">
                <Stack align="center">
                  <IconCalendarEvent size={48} color="var(--mantine-color-gray-5)" />
                  <Title order={4} c="dimmed">{t('calendarView')}</Title>
                  <Text c="dimmed" ta="center">
                    {t('calendarViewWillBeImplementedHere')}
                  </Text>
                </Stack>
              </Center>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* Add Reservation Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('addReservation')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('customer')}
                  placeholder={t('selectCustomer')}
                  data={[
                    { value: '1', label: 'Ahmed Al-Rashid' },
                    { value: '2', label: 'Sarah Johnson' },
                    { value: '3', label: 'Mohammed Hassan' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('vehicle')}
                  placeholder={t('selectVehicle')}
                  data={[
                    { value: '1', label: 'Toyota Camry 2023 (ABC-123)' },
                    { value: '2', label: 'BMW X5 2022 (XYZ-456)' },
                    { value: '3', label: 'Mercedes C-Class 2023 (DEF-789)' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('pickupDate')}
                  placeholder={t('selectPickupDate')}
                  type="date"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('returnDate')}
                  placeholder={t('selectReturnDate')}
                  type="date"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('pickupLocation')}
                  placeholder={t('selectPickupLocation')}
                  data={[
                    { value: 'dubai-airport', label: 'Dubai Airport' },
                    { value: 'downtown-dubai', label: 'Downtown Dubai' },
                    { value: 'abu-dhabi', label: 'Abu Dhabi' },
                    { value: 'sharjah', label: 'Sharjah' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('returnLocation')}
                  placeholder={t('selectReturnLocation')}
                  data={[
                    { value: 'dubai-airport', label: 'Dubai Airport' },
                    { value: 'downtown-dubai', label: 'Downtown Dubai' },
                    { value: 'abu-dhabi', label: 'Abu Dhabi' },
                    { value: 'sharjah', label: 'Sharjah' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('dailyRate')}
                  placeholder="0"
                  min={0}
                  prefix="$"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('securityDeposit')}
                  placeholder="0"
                  min={0}
                  prefix="$"
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('specialRequests')}
                  placeholder={t('enterSpecialRequests')}
                  rows={3}
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('notes')}
                  placeholder={t('enterNotes')}
                  rows={3}
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconCheck size={16} />} onClick={() => setAddModalOpen(false)}>
                {t('createReservation')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* View Reservation Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedReservation ? selectedReservation.reservation_number : t('reservationDetails')}
          size="lg"
        >
          {selectedReservation && (
            <Stack>
              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('customer')}</Text>
                  <Text fw={500}>{selectedReservation.customer_name}</Text>
                  <Text size="xs" c="dimmed">{selectedReservation.customer_email}</Text>
                  <Text size="xs" c="dimmed">{selectedReservation.customer_phone}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('vehicle')}</Text>
                  <Text fw={500}>{selectedReservation.vehicle_name}</Text>
                  <Text size="xs" c="dimmed">{selectedReservation.plate_number}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('pickupDate')}</Text>
                  <Text fw={500}>{new Date(selectedReservation.pickup_date).toLocaleDateString()}</Text>
                  <Text size="xs" c="dimmed">{selectedReservation.pickup_location}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('returnDate')}</Text>
                  <Text fw={500}>{new Date(selectedReservation.return_date).toLocaleDateString()}</Text>
                  <Text size="xs" c="dimmed">{selectedReservation.return_location}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('status')}</Text>
                  <Badge color={getStatusColor(selectedReservation.status)}>
                    {t(selectedReservation.status)}
                  </Badge>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('paymentStatus')}</Text>
                  <Badge color={getPaymentStatusColor(selectedReservation.payment_status)}>
                    {t(selectedReservation.payment_status)}
                  </Badge>
                </Grid.Col>
                <Grid.Col span={12}>
                  <Divider />
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('subtotal')}</Text>
                  <Text fw={500}>${selectedReservation.subtotal}</Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('taxAmount')}</Text>
                  <Text fw={500}>${selectedReservation.tax_amount}</Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('totalAmount')}</Text>
                  <Text fw={700} size="lg">${selectedReservation.total_amount}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('paidAmount')}</Text>
                  <Text fw={500}>${selectedReservation.paid_amount}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('remainingBalance')}</Text>
                  <Text fw={500}>${selectedReservation.total_amount - selectedReservation.paid_amount}</Text>
                </Grid.Col>
                {selectedReservation.special_requests && (
                  <Grid.Col span={12}>
                    <Text size="sm" c="dimmed">{t('specialRequests')}</Text>
                    <Text>{selectedReservation.special_requests}</Text>
                  </Grid.Col>
                )}
                {selectedReservation.notes && (
                  <Grid.Col span={12}>
                    <Text size="sm" c="dimmed">{t('notes')}</Text>
                    <Text>{selectedReservation.notes}</Text>
                  </Grid.Col>
                )}
              </Grid>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
