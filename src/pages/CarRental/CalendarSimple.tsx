import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Badge,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,Tabs,
  Select,
  SimpleGrid
} from '@mantine/core'
import {
  IconPlus,
  IconCheck,
  IconDownload,
  IconFilter,
  IconChevronLeft,
  IconChevronRight,
  IconCar,
  IconTool,
  IconEye,
  IconX,
  IconCalendar
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface CalendarEvent {
  id: string
  type: 'pickup' | 'return' | 'maintenance' | 'blocked'
  title: string
  customerName?: string
  vehicleName: string
  time: string
  status: 'confirmed' | 'pending' | 'completed' | 'cancelled'
  date: string
}

export function CalendarSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('month')
  const [selectedDate, setSelectedDate] = useState('2024-01-20')
  const [viewFilter, setViewFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Simple mock data for calendar events
  const events: CalendarEvent[] = [
    {
      id: '1',
      type: 'pickup',
      title: 'Vehicle Pickup',
      customerName: 'Ahmed Al-Rashid',
      vehicleName: 'Toyota Camry ABC-123',
      time: '09:00 AM',
      status: 'confirmed',
      date: '2024-01-20'
    },
    {
      id: '2',
      type: 'return',
      title: 'Vehicle Return',
      customerName: 'Sarah Johnson',
      vehicleName: 'BMW X5 XYZ-456',
      time: '02:00 PM',
      status: 'pending',
      date: '2024-01-20'
    },
    {
      id: '3',
      type: 'pickup',
      title: 'Vehicle Pickup',
      customerName: 'Mohammed Hassan',
      vehicleName: 'Mercedes C-Class DEF-789',
      time: '04:00 PM',
      status: 'confirmed',
      date: '2024-01-21'
    },
    {
      id: '4',
      type: 'maintenance',
      title: 'Scheduled Maintenance',
      vehicleName: 'Honda Civic GHI-012',
      time: '10:00 AM',
      status: 'confirmed',
      date: '2024-01-22'
    },
    {
      id: '5',
      type: 'return',
      title: 'Vehicle Return',
      customerName: 'Fatima Ali',
      vehicleName: 'Nissan Altima JKL-345',
      time: '11:30 AM',
      status: 'confirmed',
      date: '2024-01-22'
    }
  ]

  const todayEvents = events.filter(event => event.date === selectedDate)
  const upcomingEvents = events.filter(event => event.date > selectedDate).slice(0, 5)

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'pickup': return 'blue'
      case 'return': return 'green'
      case 'maintenance': return 'orange'
      case 'blocked': return 'red'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'green'
      case 'pending': return 'orange'
      case 'completed': return 'gray'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'pickup': return IconCar
      case 'return': return IconCheck
      case 'maintenance': return IconTool
      case 'inspection': return IconEye
      case 'blocked': return IconX
      default: return IconCalendar
    }
  }

  // Simple calendar grid for demonstration
  const calendarDays = Array.from({ length: 31 }, (_, i) => i + 1)
  const currentMonth = 'January 2024'

  const getDayEvents = (day: number) => {
    const dateStr = `2024-01-${day.toString().padStart(2, '0')}`
    return events.filter(event => event.date === dateStr)
  }

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('reservationCalendar')}
        description={t('visualCalendarViewOfBookings')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconFilter size={16} />}>
              {t('filters')}
            </Button>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportCalendar')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('addEvent')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="month">{t('monthView')}</Tabs.Tab>
          <Tabs.Tab value="week">{t('weekView')}</Tabs.Tab>
          <Tabs.Tab value="day">{t('dayView')}</Tabs.Tab>
          <Tabs.Tab value="agenda">{t('agendaView')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="month" pt="md">
          <Grid>
            {/* Calendar */}
            <Grid.Col span={{ base: 12, lg: 8 }}>
              <Card withBorder>
                {/* Calendar Header */}
                <Group justify="space-between" mb="md">
                  <Group>
                    <Button variant="subtle" size="sm" leftSection={<IconChevronLeft size={16} />}>
                      {t('previous')}
                    </Button>
                    <Title order={4}>{currentMonth}</Title>
                    <Button variant="subtle" size="sm" rightSection={<IconChevronRight size={16} />}>
                      {t('next')}
                    </Button>
                  </Group>
                  <Select
                    placeholder={t('allEvents')}
                    data={[
                      { value: '', label: t('allEvents') },
                      { value: 'pickup', label: t('pickups') },
                      { value: 'return', label: t('returns') },
                      { value: 'maintenance', label: t('maintenance') }
                    ]}
                    value={viewFilter}
                    onChange={(value) => setViewFilter(value || '')}
                    w={150}
                  />
                </Group>

                {/* Simple Calendar Grid */}
                <SimpleGrid cols={7} spacing="xs">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                    <Paper key={day} p="xs" bg="gray.1">
                      <Text size="sm" fw={700} ta="center">{day}</Text>
                    </Paper>
                  ))}
                  
                  {calendarDays.map((day) => {
                    const dayEvents = getDayEvents(day)
                    const isToday = day === 20 // Mock today as 20th
                    
                    return (
                      <Paper 
                        key={day} 
                        p="xs" 
                        withBorder 
                        style={{ 
                          minHeight: '80px',
                          backgroundColor: isToday ? 'var(--mantine-color-blue-0)' : undefined,
                          cursor: 'pointer'
                        }}
                        onClick={() => setSelectedDate(`2024-01-${day.toString().padStart(2, '0')}`)}
                      >
                        <Text size="sm" fw={isToday ? 700 : 400} mb="xs">
                          {day}
                        </Text>
                        <Stack gap={2}>
                          {dayEvents.slice(0, 2).map((event) => {
                            const IconComponent = getEventIcon(event.type)
                            return (
                              <Badge 
                                key={event.id}
                                size="xs" 
                                color={getEventTypeColor(event.type)}
                                leftSection={<IconComponent size={10} />}
                                style={{ fontSize: '9px' }}
                              >
                                {event.time}
                              </Badge>
                            )
                          })}
                          {dayEvents.length > 2 && (
                            <Text size="xs" c="dimmed">+{dayEvents.length - 2} more</Text>
                          )}
                        </Stack>
                      </Paper>
                    )
                  })}
                </SimpleGrid>
              </Card>
            </Grid.Col>

            {/* Today's Events */}
            <Grid.Col span={{ base: 12, lg: 4 }}>
              <Stack>
                <Card withBorder>
                  <Title order={4} mb="md">{t('todaysEvents')} ({selectedDate})</Title>
                  
                  {todayEvents.length === 0 ? (
                    <Text c="dimmed" ta="center" py="md">{t('noEventsToday')}</Text>
                  ) : (
                    <Stack gap="sm">
                      {todayEvents.map((event) => {
                        const IconComponent = getEventIcon(event.type)
                        return (
                          <Paper key={event.id} p="sm" withBorder>
                            <Group justify="space-between" mb="xs">
                              <Group gap="xs">
                                <IconComponent size={16} color={`var(--mantine-color-${getEventTypeColor(event.type)}-6)`} />
                                <Text fw={500} size="sm">{event.title}</Text>
                              </Group>
                              <Badge size="xs" color={getStatusColor(event.status)}>
                                {t(event.status)}
                              </Badge>
                            </Group>
                            <Text size="xs" c="dimmed" mb="xs">{event.time}</Text>
                            {event.customerName && (
                              <Text size="xs" c="dimmed">{event.customerName}</Text>
                            )}
                            <Text size="xs" c="dimmed">{event.vehicleName}</Text>
                          </Paper>
                        )
                      })}
                    </Stack>
                  )}
                </Card>

                <Card withBorder>
                  <Title order={4} mb="md">{t('upcomingEvents')}</Title>
                  
                  <Stack gap="sm">
                    {upcomingEvents.map((event) => {
                      const IconComponent = getEventIcon(event.type)
                      return (
                        <Paper key={event.id} p="sm" withBorder>
                          <Group justify="space-between" mb="xs">
                            <Group gap="xs">
                              <IconComponent size={14} color={`var(--mantine-color-${getEventTypeColor(event.type)}-6)`} />
                              <Text fw={500} size="sm">{event.title}</Text>
                            </Group>
                          </Group>
                          <Text size="xs" c="dimmed">{event.date} at {event.time}</Text>
                          <Text size="xs" c="dimmed">{event.vehicleName}</Text>
                        </Paper>
                      )
                    })}
                  </Stack>
                </Card>
              </Stack>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="week" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('weekView')}</Title>
            <Text c="dimmed">{t('weekViewWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="day" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('dayView')}</Title>
            <Text c="dimmed">{t('dayViewWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="agenda" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('agendaView')}</Title>
            
            <Stack gap="md">
              {events.map((event) => {
                const IconComponent = getEventIcon(event.type)
                return (
                  <Paper key={event.id} p="md" withBorder>
                    <Group justify="space-between">
                      <Group>
                        <IconComponent size={20} color={`var(--mantine-color-${getEventTypeColor(event.type)}-6)`} />
                        <div>
                          <Text fw={500}>{event.title}</Text>
                          <Text size="sm" c="dimmed">{event.date} at {event.time}</Text>
                          {event.customerName && (
                            <Text size="sm" c="dimmed">{event.customerName}</Text>
                          )}
                          <Text size="sm" c="dimmed">{event.vehicleName}</Text>
                        </div>
                      </Group>
                      <Badge color={getStatusColor(event.status)}>
                        {t(event.status)}
                      </Badge>
                    </Group>
                  </Paper>
                )
              })}
            </Stack>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Add Event Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('addCalendarEvent')}
        size="md"
      >
        <Stack>
          <Select
            label={t('eventType')}
            placeholder={t('selectEventType')}
            data={[
              { value: 'pickup', label: t('vehiclePickup') },
              { value: 'return', label: t('vehicleReturn') },
              { value: 'maintenance', label: t('maintenance') },
              { value: 'blocked', label: t('blockedTime') }
            ]}
            required
          />

          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry ABC-123' },
              { value: '2', label: 'BMW X5 XYZ-456' },
              { value: '3', label: 'Mercedes C-Class DEF-789' }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCheck size={16} />} onClick={() => setAddModalOpen(false)}>
              {t('addEvent')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
