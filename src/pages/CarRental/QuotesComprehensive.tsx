import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Center,
  Progress,
  Alert
} from '@mantine/core'
import {
  IconCalendar,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconTrash,
  IconSend,
  IconCurrencyDollar,
  IconFileText,
  IconAlertTriangle,
  IconMail,
  IconPhone,
  IconCalendarEvent,
  IconPercentage
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface QuoteTableData {
  id: string
  quote_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  vehicle_id: string
  vehicle_name: string
  plate_number: string
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired' | 'converted'
  pickup_date: string
  return_date: string
  pickup_location: string
  return_location: string
  total_days: number
  daily_rate: number
  subtotal: number
  discount_amount: number
  tax_amount: number
  total_amount: number
  valid_until: string
  quote_template: string
  special_offers: string
  terms_conditions: string
  notes: string
  sent_date?: string
  viewed_date?: string
  response_date?: string
  converted_to_reservation?: string
  created_by: string
  created_at: string
  updated_at: string
}

export function QuotesComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [quotes, setQuotes] = useState<QuoteTableData[]>([])
  const [activeTab, setActiveTab] = useState('all-quotes')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [dateFilter, setDateFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [sendModalOpen, setSendModalOpen] = useState(false)
  const [selectedQuote, setSelectedQuote] = useState<QuoteTableData | null>(null)

  // Mock data for development - comprehensive quote structure
  const mockQuotes: QuoteTableData[] = [
    {
      id: '1',
      quote_number: 'QUO-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971501234567',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      plate_number: 'ABC-123',
      status: 'sent',
      pickup_date: '2024-01-25',
      return_date: '2024-01-30',
      pickup_location: 'Dubai Airport',
      return_location: 'Dubai Airport',
      total_days: 5,
      daily_rate: 120,
      subtotal: 600,
      discount_amount: 50,
      tax_amount: 27.5,
      total_amount: 577.5,
      valid_until: '2024-01-31',
      quote_template: 'Standard Rental Quote',
      special_offers: 'Early Bird Discount 8%',
      terms_conditions: 'Standard rental terms apply',
      notes: 'Customer requested GPS navigation',
      sent_date: '2024-01-20T10:00:00Z',
      created_by: 'admin',
      created_at: '2024-01-20T09:30:00Z',
      updated_at: '2024-01-20T10:00:00Z'
    },
    {
      id: '2',
      quote_number: 'QUO-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+971507654321',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      plate_number: 'XYZ-456',
      status: 'accepted',
      pickup_date: '2024-01-28',
      return_date: '2024-02-02',
      pickup_location: 'Downtown Dubai',
      return_location: 'Downtown Dubai',
      total_days: 5,
      daily_rate: 200,
      subtotal: 1000,
      discount_amount: 0,
      tax_amount: 50,
      total_amount: 1050,
      valid_until: '2024-01-30',
      quote_template: 'Premium Vehicle Quote',
      special_offers: '',
      terms_conditions: 'Premium vehicle terms apply',
      notes: 'Business customer - priority service',
      sent_date: '2024-01-18T14:00:00Z',
      viewed_date: '2024-01-19T09:15:00Z',
      response_date: '2024-01-19T11:30:00Z',
      created_by: 'admin',
      created_at: '2024-01-18T13:45:00Z',
      updated_at: '2024-01-19T11:30:00Z'
    },
    {
      id: '3',
      quote_number: 'QUO-2024-003',
      customer_id: '3',
      customer_name: 'Mohammed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+971509876543',
      vehicle_id: '3',
      vehicle_name: 'Mercedes C-Class 2023',
      plate_number: 'DEF-789',
      status: 'draft',
      pickup_date: '2024-02-01',
      return_date: '2024-02-07',
      pickup_location: 'Abu Dhabi',
      return_location: 'Abu Dhabi',
      total_days: 6,
      daily_rate: 180,
      subtotal: 1080,
      discount_amount: 108,
      tax_amount: 48.6,
      total_amount: 1020.6,
      valid_until: '2024-02-05',
      quote_template: 'Luxury Vehicle Quote',
      special_offers: 'Weekly Rental Discount 10%',
      terms_conditions: 'Luxury vehicle terms and conditions',
      notes: 'First time customer - requires verification',
      created_by: 'admin',
      created_at: '2024-01-22T16:00:00Z',
      updated_at: '2024-01-22T16:00:00Z'
    },
    {
      id: '4',
      quote_number: 'QUO-2024-004',
      customer_id: '4',
      customer_name: 'Fatima Al-Zahra',
      customer_email: '<EMAIL>',
      customer_phone: '+971502345678',
      vehicle_id: '4',
      vehicle_name: 'Nissan Altima 2023',
      plate_number: 'GHI-012',
      status: 'converted',
      pickup_date: '2024-01-15',
      return_date: '2024-01-20',
      pickup_location: 'Sharjah',
      return_location: 'Sharjah',
      total_days: 5,
      daily_rate: 100,
      subtotal: 500,
      discount_amount: 25,
      tax_amount: 23.75,
      total_amount: 498.75,
      valid_until: '2024-01-25',
      quote_template: 'Economy Vehicle Quote',
      special_offers: 'Loyalty Customer Discount 5%',
      terms_conditions: 'Standard terms apply',
      notes: 'Regular customer - excellent history',
      sent_date: '2024-01-12T11:00:00Z',
      viewed_date: '2024-01-12T15:30:00Z',
      response_date: '2024-01-13T09:00:00Z',
      converted_to_reservation: 'RES-2024-015',
      created_by: 'admin',
      created_at: '2024-01-12T10:30:00Z',
      updated_at: '2024-01-13T09:00:00Z'
    },
    {
      id: '5',
      quote_number: 'QUO-2024-005',
      customer_id: '5',
      customer_name: 'Omar Al-Mansoori',
      customer_email: '<EMAIL>',
      customer_phone: '+971503456789',
      vehicle_id: '5',
      vehicle_name: 'Audi A4 2023',
      plate_number: 'JKL-345',
      status: 'expired',
      pickup_date: '2024-01-20',
      return_date: '2024-01-23',
      pickup_location: 'Dubai Mall',
      return_location: 'Dubai Mall',
      total_days: 3,
      daily_rate: 160,
      subtotal: 480,
      discount_amount: 0,
      tax_amount: 24,
      total_amount: 504,
      valid_until: '2024-01-22',
      quote_template: 'Standard Rental Quote',
      special_offers: '',
      terms_conditions: 'Standard rental terms apply',
      notes: 'Customer did not respond within validity period',
      sent_date: '2024-01-18T16:00:00Z',
      viewed_date: '2024-01-19T10:00:00Z',
      created_by: 'admin',
      created_at: '2024-01-18T15:45:00Z',
      updated_at: '2024-01-22T23:59:59Z'
    }
  ]

  useEffect(() => {
    loadQuotes()
  }, [])

  const loadQuotes = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await quoteService.getAllQuotes()
      setQuotes(mockQuotes)
    } catch (error) {
      console.error('Error loading quotes:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = !searchQuery || 
      quote.quote_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      quote.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      quote.vehicle_name.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || quote.status === statusFilter
    const matchesLocation = !locationFilter || 
      quote.pickup_location.toLowerCase().includes(locationFilter.toLowerCase())
    
    return matchesSearch && matchesStatus && matchesLocation
  })

  // Pagination
  const totalItems = filteredQuotes.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedQuotes = filteredQuotes.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const stats = [
    {
      label: t('totalQuotes'),
      value: quotes.length.toString(),
      color: 'blue',
      icon: IconFileText
    },
    {
      label: t('pendingQuotes'),
      value: quotes.filter(q => ['draft', 'sent', 'viewed'].includes(q.status)).length.toString(),
      color: 'orange',
      icon: IconClock
    },
    {
      label: t('acceptedQuotes'),
      value: quotes.filter(q => q.status === 'accepted').length.toString(),
      color: 'green',
      icon: IconCheck
    },
    {
      label: t('conversionRate'),
      value: `${Math.round((quotes.filter(q => q.status === 'converted').length / quotes.length) * 100)}%`,
      color: 'teal',
      icon: IconPercentage
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'gray'
      case 'sent': return 'blue'
      case 'viewed': return 'cyan'
      case 'accepted': return 'green'
      case 'rejected': return 'red'
      case 'expired': return 'orange'
      case 'converted': return 'teal'
      default: return 'gray'
    }
  }

  const getStatusProgress = (status: string) => {
    switch (status) {
      case 'draft': return 10
      case 'sent': return 30
      case 'viewed': return 50
      case 'accepted': return 80
      case 'converted': return 100
      case 'rejected': return 0
      case 'expired': return 0
      default: return 0
    }
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('quotesManagement')}</Title>
              <Text c="dimmed" size="sm">{t('generateAndManageRentalQuotes')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setAddModalOpen(true)}
              >
                {t('createQuote')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-quotes">{t('allQuotes')}</Tabs.Tab>
            <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-quotes">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <TextInput
                    placeholder={t('searchQuotes')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'draft', label: t('draft') },
                      { value: 'sent', label: t('sent') },
                      { value: 'viewed', label: t('viewed') },
                      { value: 'accepted', label: t('accepted') },
                      { value: 'rejected', label: t('rejected') },
                      { value: 'expired', label: t('expired') },
                      { value: 'converted', label: t('converted') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <TextInput
                    placeholder={t('location')}
                    leftSection={<IconCalendar size={16} />}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('dateRange')}
                    leftSection={<IconCalendar size={16} />}
                    value={dateFilter}
                    onChange={(value) => setDateFilter(value || '')}
                    data={[
                      { value: '', label: t('allDates') },
                      { value: 'today', label: t('today') },
                      { value: 'week', label: t('thisWeek') },
                      { value: 'month', label: t('thisMonth') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setLocationFilter('')
                      setDateFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Quotes Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('quote')}</Table.Th>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('vehicle')}</Table.Th>
                    <Table.Th>{t('dates')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('amount')}</Table.Th>
                    <Table.Th>{t('validUntil')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedQuotes.map((quote) => (
                    <Table.Tr key={quote.id}>
                      <Table.Td>
                        <div>
                          <Text fw={700} size="sm">{quote.quote_number}</Text>
                          <Text size="xs" c="dimmed">
                            {new Date(quote.created_at).toLocaleDateString()}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color="blue">
                            {quote.customer_name.charAt(0)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{quote.customer_name}</Text>
                            <Text size="xs" c="dimmed">{quote.customer_email}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{quote.vehicle_name}</Text>
                          <Text size="xs" c="dimmed">{quote.plate_number}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">
                            {new Date(quote.pickup_date).toLocaleDateString()} - {new Date(quote.return_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">{quote.total_days} {t('days')}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Badge color={getStatusColor(quote.status)} size="sm">
                            {t(quote.status)}
                          </Badge>
                          <Progress
                            value={getStatusProgress(quote.status)}
                            size="xs"
                            mt={4}
                            color={getStatusColor(quote.status)}
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={700} size="sm">${quote.total_amount}</Text>
                          {quote.discount_amount > 0 && (
                            <Text size="xs" c="green">-${quote.discount_amount} {t('discount')}</Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">{new Date(quote.valid_until).toLocaleDateString()}</Text>
                          <Text size="xs" c={new Date(quote.valid_until) < new Date() ? 'red' : 'dimmed'}>
                            {new Date(quote.valid_until) < new Date() ? t('expired') : t('valid')}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedQuote(quote)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedQuote(quote)
                              setEditModalOpen(true)
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="green"
                            onClick={() => {
                              setSelectedQuote(quote)
                              setSendModalOpen(true)
                            }}
                          >
                            <IconSend size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="red"
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('quotes')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="analytics">
            <Card withBorder>
              <Center py="xl">
                <Stack align="center">
                  <IconCalendarEvent size={48} color="var(--mantine-color-gray-5)" />
                  <Title order={4} c="dimmed">{t('quotesAnalytics')}</Title>
                  <Text c="dimmed" ta="center">
                    {t('quotesAnalyticsWillBeImplementedHere')}
                  </Text>
                </Stack>
              </Center>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* Add Quote Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('createQuote')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('customer')}
                  placeholder={t('selectCustomer')}
                  data={[
                    { value: '1', label: 'Ahmed Al-Rashid' },
                    { value: '2', label: 'Sarah Johnson' },
                    { value: '3', label: 'Mohammed Hassan' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('vehicle')}
                  placeholder={t('selectVehicle')}
                  data={[
                    { value: '1', label: 'Toyota Camry 2023 (ABC-123)' },
                    { value: '2', label: 'BMW X5 2022 (XYZ-456)' },
                    { value: '3', label: 'Mercedes C-Class 2023 (DEF-789)' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('pickupDate')}
                  placeholder={t('selectPickupDate')}
                  type="date"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('returnDate')}
                  placeholder={t('selectReturnDate')}
                  type="date"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('dailyRate')}
                  placeholder="0"
                  min={0}
                  prefix="$"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('discountAmount')}
                  placeholder="0"
                  min={0}
                  prefix="$"
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('specialOffers')}
                  placeholder={t('enterSpecialOffers')}
                  rows={2}
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('notes')}
                  placeholder={t('enterNotes')}
                  rows={3}
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconCheck size={16} />} onClick={() => setAddModalOpen(false)}>
                {t('createQuote')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* View Quote Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedQuote ? selectedQuote.quote_number : t('quoteDetails')}
          size="lg"
        >
          {selectedQuote && (
            <Stack>
              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('customer')}</Text>
                  <Text fw={500}>{selectedQuote.customer_name}</Text>
                  <Text size="xs" c="dimmed">{selectedQuote.customer_email}</Text>
                  <Text size="xs" c="dimmed">{selectedQuote.customer_phone}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('vehicle')}</Text>
                  <Text fw={500}>{selectedQuote.vehicle_name}</Text>
                  <Text size="xs" c="dimmed">{selectedQuote.plate_number}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('pickupDate')}</Text>
                  <Text fw={500}>{new Date(selectedQuote.pickup_date).toLocaleDateString()}</Text>
                  <Text size="xs" c="dimmed">{selectedQuote.pickup_location}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('returnDate')}</Text>
                  <Text fw={500}>{new Date(selectedQuote.return_date).toLocaleDateString()}</Text>
                  <Text size="xs" c="dimmed">{selectedQuote.return_location}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('status')}</Text>
                  <Badge color={getStatusColor(selectedQuote.status)}>
                    {t(selectedQuote.status)}
                  </Badge>
                  <Progress
                    value={getStatusProgress(selectedQuote.status)}
                    size="sm"
                    mt={4}
                    color={getStatusColor(selectedQuote.status)}
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('validUntil')}</Text>
                  <Text fw={500}>{new Date(selectedQuote.valid_until).toLocaleDateString()}</Text>
                  <Text size="xs" c={new Date(selectedQuote.valid_until) < new Date() ? 'red' : 'green'}>
                    {new Date(selectedQuote.valid_until) < new Date() ? t('expired') : t('valid')}
                  </Text>
                </Grid.Col>
                <Grid.Col span={12}>
                  <Divider />
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('subtotal')}</Text>
                  <Text fw={500}>${selectedQuote.subtotal}</Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('discount')}</Text>
                  <Text fw={500} c="green">-${selectedQuote.discount_amount}</Text>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Text size="sm" c="dimmed">{t('taxAmount')}</Text>
                  <Text fw={500}>${selectedQuote.tax_amount}</Text>
                </Grid.Col>
                <Grid.Col span={12}>
                  <Text size="sm" c="dimmed">{t('totalAmount')}</Text>
                  <Text fw={700} size="xl">${selectedQuote.total_amount}</Text>
                </Grid.Col>
                {selectedQuote.special_offers && (
                  <Grid.Col span={12}>
                    <Text size="sm" c="dimmed">{t('specialOffers')}</Text>
                    <Text>{selectedQuote.special_offers}</Text>
                  </Grid.Col>
                )}
                {selectedQuote.notes && (
                  <Grid.Col span={12}>
                    <Text size="sm" c="dimmed">{t('notes')}</Text>
                    <Text>{selectedQuote.notes}</Text>
                  </Grid.Col>
                )}
                {selectedQuote.converted_to_reservation && (
                  <Grid.Col span={12}>
                    <Alert color="green" icon={<IconCheck size={16} />}>
                      <Text fw={500}>{t('convertedToReservation')}</Text>
                      <Text size="sm">{t('reservationNumber')}: {selectedQuote.converted_to_reservation}</Text>
                    </Alert>
                  </Grid.Col>
                )}
              </Grid>
            </Stack>
          )}
        </Modal>

        {/* Send Quote Modal */}
        <Modal
          opened={sendModalOpen}
          onClose={() => setSendModalOpen(false)}
          title={t('sendQuote')}
          size="md"
        >
          {selectedQuote && (
            <Stack>
              <Text size="sm">
                {t('sendQuoteToCustomer')}: <strong>{selectedQuote.customer_name}</strong>
              </Text>
              <Text size="sm" c="dimmed">
                {t('email')}: {selectedQuote.customer_email}
              </Text>

              <Textarea
                label={t('emailMessage')}
                placeholder={t('enterEmailMessage')}
                rows={4}
                defaultValue={`Dear ${selectedQuote.customer_name},

Please find attached your rental quote ${selectedQuote.quote_number} for ${selectedQuote.vehicle_name}.

Quote Details:
- Pickup: ${new Date(selectedQuote.pickup_date).toLocaleDateString()}
- Return: ${new Date(selectedQuote.return_date).toLocaleDateString()}
- Total Amount: $${selectedQuote.total_amount}
- Valid Until: ${new Date(selectedQuote.valid_until).toLocaleDateString()}

Best regards,
Carvio Team`}
              />

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setSendModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button leftSection={<IconSend size={16} />} onClick={() => setSendModalOpen(false)}>
                  {t('sendQuote')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
