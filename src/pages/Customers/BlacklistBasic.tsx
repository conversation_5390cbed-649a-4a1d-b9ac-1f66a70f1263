import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar,
  Textarea
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconBan,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconSearch,
  IconShield,
  IconTrash,
  IconX
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface BlacklistCustomer {
  id: string
  customerNumber: string
  name: string
  email: string
  phone: string
  country: string
  reason: string
  blacklistDate: string
  addedBy: string
  status: 'active' | 'temporary' | 'permanent'
  expiryDate?: string
  notes: string
}

export function BlacklistBasic() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Simple mock data
  const blacklistedCustomers: BlacklistCustomer[] = [
    {
      id: '1',
      customerNumber: 'CUS-2023-045',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '******-123-4567',
      country: 'USA',
      reason: 'Repeated late returns',
      blacklistDate: '2024-01-10',
      addedBy: 'Manager',
      status: 'permanent',
      notes: 'Customer consistently returned vehicles 3+ days late despite multiple warnings'
    },
    {
      id: '2',
      customerNumber: 'CUS-2023-078',
      name: 'Maria Garcia',
      email: '<EMAIL>',
      phone: '+34-666-789-012',
      country: 'Spain',
      reason: 'Vehicle damage',
      blacklistDate: '2024-01-05',
      addedBy: 'Operations',
      status: 'temporary',
      expiryDate: '2024-07-05',
      notes: 'Caused significant damage to vehicle interior, refused to pay damages'
    },
    {
      id: '3',
      customerNumber: 'CUS-2023-112',
      name: 'Ahmed Hassan',
      email: '<EMAIL>',
      phone: '+971-50-345-6789',
      country: 'UAE',
      reason: 'Fraudulent payment',
      blacklistDate: '2023-12-20',
      addedBy: 'Finance',
      status: 'permanent',
      notes: 'Used stolen credit card for payment, reported by bank'
    }
  ]

  const stats = [
    { label: t('totalBlacklisted'), value: '47', color: 'red', icon: IconBan },
    { label: t('permanentBans'), value: '23', color: 'dark', icon: IconX },
    { label: t('temporaryBans'), value: '18', color: 'orange', icon: IconClock },
    { label: t('thisMonth'), value: '6', color: 'yellow', icon: IconAlertTriangle }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'permanent': return 'red'
      case 'temporary': return 'orange'
      case 'active': return 'yellow'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredCustomers = blacklistedCustomers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.customerNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || customer.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('blacklistManagement')}
        description={t('manageBlacklistedCustomersAndRestrictions')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportBlacklist')}
            </Button>
            <Button leftSection={<IconBan size={16} />} color="red" onClick={() => setAddModalOpen(true)}>
              {t('addToBlacklist')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="blacklist">{t('blacklistedCustomers')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Blacklist Alerts */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('blacklistAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('highRiskCustomers')}</Text>
                    <Text size="xs">3 {t('customersWithMultipleViolations')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('expiringBans')}</Text>
                    <Text size="xs">5 {t('temporaryBansExpiringThisMonth')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconShield size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('reviewRequired')}</Text>
                    <Text size="xs">2 {t('blacklistEntriesRequireReview')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Blacklist Reasons */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('commonReasons')}</Title>
                
                <Stack gap="sm">
                  <Group justify="space-between">
                    <Text size="sm">{t('lateReturns')}</Text>
                    <Text fw={700}>34%</Text>
                  </Group>
                  
                  <Group justify="space-between">
                    <Text size="sm">{t('vehicleDamage')}</Text>
                    <Text fw={700}>28%</Text>
                  </Group>
                  
                  <Group justify="space-between">
                    <Text size="sm">{t('paymentIssues')}</Text>
                    <Text fw={700}>21%</Text>
                  </Group>
                  
                  <Group justify="space-between">
                    <Text size="sm">{t('fraudulentActivity')}</Text>
                    <Text fw={700}>17%</Text>
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Recent Blacklist Entries */}
          <Card withBorder mt="lg">
            <Title order={4} mb="md">{t('recentBlacklistEntries')}</Title>
            
            <Stack gap="sm">
              {blacklistedCustomers.slice(0, 3).map((customer) => (
                <Paper key={customer.id} p="sm" withBorder>
                  <Group justify="space-between">
                    <Group>
                      <Avatar color="red" radius="xl">
                        {getInitials(customer.name)}
                      </Avatar>
                      <div>
                        <Text fw={500} size="sm">{customer.name}</Text>
                        <Text size="xs" c="dimmed">{customer.reason}</Text>
                      </div>
                    </Group>
                    <div style={{ textAlign: 'right' }}>
                      <Badge color={getStatusColor(customer.status)} size="sm">
                        {t(customer.status)}
                      </Badge>
                      <Text size="xs" c="dimmed">{customer.blacklistDate}</Text>
                    </div>
                  </Group>
                </Paper>
              ))}
            </Stack>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="blacklist" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchBlacklistedCustomers')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'permanent', label: t('permanent') },
                    { value: 'temporary', label: t('temporary') },
                    { value: 'active', label: t('active') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Blacklisted Customers Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('contact')}</Table.Th>
                  <Table.Th>{t('reason')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('blacklistDate')}</Table.Th>
                  <Table.Th>{t('expiryDate')}</Table.Th>
                  <Table.Th>{t('addedBy')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredCustomers.map((customer) => (
                  <Table.Tr key={customer.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color="red" radius="xl" size="sm">
                          {getInitials(customer.name)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{customer.name}</Text>
                          <Text size="xs" c="dimmed">{customer.customerNumber}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{customer.email}</Text>
                        <Text size="xs" c="dimmed">{customer.phone}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{customer.reason}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(customer.status)}>
                        {t(customer.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{customer.blacklistDate}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{customer.expiryDate || '-'}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{customer.addedBy}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="green">
                          <IconCheck size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('blacklistAnalytics')}</Title>
            <Text c="dimmed">{t('analyticsWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Add to Blacklist Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('addCustomerToBlacklist')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid (<EMAIL>)' },
              { value: '2', label: 'Sarah Johnson (<EMAIL>)' },
              { value: '3', label: 'Mohammed Hassan (<EMAIL>)' }
            ]}
            required
          />

          <Select
            label={t('reason')}
            placeholder={t('selectReason')}
            data={[
              { value: 'late-returns', label: t('lateReturns') },
              { value: 'vehicle-damage', label: t('vehicleDamage') },
              { value: 'payment-issues', label: t('paymentIssues') },
              { value: 'fraudulent-activity', label: t('fraudulentActivity') },
              { value: 'other', label: t('other') }
            ]}
            required
          />

          <Select
            label={t('blacklistType')}
            placeholder={t('selectType')}
            data={[
              { value: 'temporary', label: t('temporary') },
              { value: 'permanent', label: t('permanent') }
            ]}
            required
          />

          <Textarea
            label={t('notes')}
            placeholder={t('enterDetailedNotes')}
            rows={3}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconBan size={16} />} color="red" onClick={() => setAddModalOpen(false)}>
              {t('addToBlacklist')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
