import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Center,
  Progress,
  Alert,
  RingProgress,
  Timeline,
  Tooltip,
  Switch
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconBan,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconTrash,
  IconShield,
  IconUser,
  IconPhone,
  IconMail,
  IconMapPin,
  IconCalendar,
  IconCurrencyDollar,
  IconTrendingUp,
  IconUsers,
  IconCalendarEvent,
  IconCreditCard,
  IconFileText,
  IconX,
  IconRefresh
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface BlacklistTableData {
  id: string
  blacklist_number: string
  customer_id: string
  customer_number: string
  customer_name: string
  customer_email: string
  customer_phone: string
  nationality: string
  country: string
  blacklist_reason: 'payment_default' | 'vehicle_damage' | 'fraudulent_activity' | 'policy_violation' | 'late_returns' | 'other'
  blacklist_type: 'temporary' | 'permanent' | 'warning'
  status: 'active' | 'expired' | 'lifted' | 'under_review'
  severity_level: 'low' | 'medium' | 'high' | 'critical'
  blacklist_date: string
  expiry_date?: string
  lifted_date?: string
  lifted_by?: string
  lift_reason?: string
  incident_details: string
  financial_impact: number
  evidence_documents: string[]
  previous_violations: number
  warning_count: number
  review_date?: string
  review_notes?: string
  added_by: string
  last_updated_by: string
  created_at: string
  updated_at: string
}

export function BlacklistComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [blacklistEntries, setBlacklistEntries] = useState<BlacklistTableData[]>([])
  const [activeTab, setActiveTab] = useState('all-blacklist')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [reasonFilter, setReasonFilter] = useState<string>('')
  const [severityFilter, setSeverityFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [liftModalOpen, setLiftModalOpen] = useState(false)
  const [reviewModalOpen, setReviewModalOpen] = useState(false)
  const [selectedEntry, setSelectedEntry] = useState<BlacklistTableData | null>(null)

  // Mock data for development - comprehensive blacklist structure
  const mockBlacklistEntries: BlacklistTableData[] = [
    {
      id: '1',
      blacklist_number: 'BL-2024-001',
      customer_id: '1',
      customer_number: 'CUS-2023-045',
      customer_name: 'John Smith',
      customer_email: '<EMAIL>',
      customer_phone: '+1555123456',
      nationality: 'American',
      country: 'USA',
      blacklist_reason: 'late_returns',
      blacklist_type: 'permanent',
      status: 'active',
      severity_level: 'high',
      blacklist_date: '2024-01-10T10:00:00Z',
      incident_details: 'Customer consistently returned vehicles 3+ days late despite multiple warnings. Total of 8 late returns in 6 months.',
      financial_impact: 2400,
      evidence_documents: ['late_return_report_1.pdf', 'warning_notices.pdf'],
      previous_violations: 8,
      warning_count: 3,
      added_by: 'manager',
      last_updated_by: 'manager',
      created_at: '2024-01-10T10:00:00Z',
      updated_at: '2024-01-10T10:00:00Z'
    },
    {
      id: '2',
      blacklist_number: 'BL-2024-002',
      customer_id: '2',
      customer_number: 'CUS-2023-078',
      customer_name: 'Maria Garcia',
      customer_email: '<EMAIL>',
      customer_phone: '+34666789012',
      nationality: 'Spanish',
      country: 'Spain',
      blacklist_reason: 'vehicle_damage',
      blacklist_type: 'temporary',
      status: 'active',
      severity_level: 'medium',
      blacklist_date: '2024-01-05T14:30:00Z',
      expiry_date: '2024-07-05T14:30:00Z',
      incident_details: 'Caused significant damage to vehicle interior, refused to pay damages. Damage included torn seats and broken dashboard.',
      financial_impact: 3500,
      evidence_documents: ['damage_photos.zip', 'repair_estimate.pdf'],
      previous_violations: 1,
      warning_count: 0,
      review_date: '2024-04-05T14:30:00Z',
      added_by: 'operations',
      last_updated_by: 'operations',
      created_at: '2024-01-05T14:30:00Z',
      updated_at: '2024-01-05T14:30:00Z'
    },
    {
      id: '3',
      blacklist_number: 'BL-2023-089',
      customer_id: '3',
      customer_number: 'CUS-2023-112',
      customer_name: 'Ahmed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+************',
      nationality: 'Egyptian',
      country: 'UAE',
      blacklist_reason: 'fraudulent_activity',
      blacklist_type: 'permanent',
      status: 'active',
      severity_level: 'critical',
      blacklist_date: '2023-12-20T09:15:00Z',
      incident_details: 'Used stolen credit card for payment, reported by bank. Also provided false identification documents.',
      financial_impact: 1200,
      evidence_documents: ['bank_fraud_report.pdf', 'police_report.pdf'],
      previous_violations: 0,
      warning_count: 0,
      added_by: 'finance',
      last_updated_by: 'finance',
      created_at: '2023-12-20T09:15:00Z',
      updated_at: '2023-12-20T09:15:00Z'
    },
    {
      id: '4',
      blacklist_number: 'BL-2023-067',
      customer_id: '4',
      customer_number: 'CUS-2023-034',
      customer_name: 'Robert Wilson',
      customer_email: '<EMAIL>',
      customer_phone: '+************',
      nationality: 'British',
      country: 'UK',
      blacklist_reason: 'payment_default',
      blacklist_type: 'temporary',
      status: 'lifted',
      severity_level: 'medium',
      blacklist_date: '2023-08-15T11:00:00Z',
      expiry_date: '2024-02-15T11:00:00Z',
      lifted_date: '2024-01-20T16:30:00Z',
      lifted_by: 'finance',
      lift_reason: 'Outstanding balance paid in full with penalty fees',
      incident_details: 'Failed to pay outstanding balance of $2,800 for 90+ days despite multiple reminders.',
      financial_impact: 2800,
      evidence_documents: ['payment_history.pdf', 'collection_notices.pdf'],
      previous_violations: 2,
      warning_count: 1,
      added_by: 'finance',
      last_updated_by: 'finance',
      created_at: '2023-08-15T11:00:00Z',
      updated_at: '2024-01-20T16:30:00Z'
    }
  ]

  useEffect(() => {
    loadBlacklistEntries()
  }, [])

  const loadBlacklistEntries = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await blacklistService.getAllBlacklistEntries()
      setBlacklistEntries(mockBlacklistEntries)
    } catch (error) {
      console.error('Error loading blacklist entries:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredEntries = blacklistEntries.filter(entry => {
    const matchesSearch = !searchQuery || 
      entry.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.customer_email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.blacklist_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.customer_number.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || entry.status === statusFilter
    const matchesType = !typeFilter || entry.blacklist_type === typeFilter
    const matchesReason = !reasonFilter || entry.blacklist_reason === reasonFilter
    const matchesSeverity = !severityFilter || entry.severity_level === severityFilter
    
    return matchesSearch && matchesStatus && matchesType && matchesReason && matchesSeverity
  })

  // Pagination
  const totalItems = filteredEntries.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedEntries = filteredEntries.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const totalFinancialImpact = blacklistEntries.reduce((sum, e) => sum + e.financial_impact, 0)
  const activeEntries = blacklistEntries.filter(e => e.status === 'active').length
  const criticalEntries = blacklistEntries.filter(e => e.severity_level === 'critical').length
  const expiringEntries = blacklistEntries.filter(e => 
    e.expiry_date && new Date(e.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  ).length

  const stats = [
    {
      label: t('totalBlacklisted'),
      value: blacklistEntries.length.toString(),
      color: 'red',
      icon: IconBan
    },
    {
      label: t('activeBlacklist'),
      value: activeEntries.toString(),
      color: 'orange',
      icon: IconShield
    },
    {
      label: t('criticalCases'),
      value: criticalEntries.toString(),
      color: 'dark',
      icon: IconAlertTriangle
    },
    {
      label: t('financialImpact'),
      value: `$${totalFinancialImpact.toLocaleString()}`,
      color: 'blue',
      icon: IconCurrencyDollar
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'red'
      case 'expired': return 'gray'
      case 'lifted': return 'green'
      case 'under_review': return 'yellow'
      default: return 'gray'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'blue'
      case 'medium': return 'yellow'
      case 'high': return 'orange'
      case 'critical': return 'red'
      default: return 'gray'
    }
  }

  const getReasonColor = (reason: string) => {
    switch (reason) {
      case 'payment_default': return 'red'
      case 'vehicle_damage': return 'orange'
      case 'fraudulent_activity': return 'dark'
      case 'policy_violation': return 'yellow'
      case 'late_returns': return 'blue'
      case 'other': return 'gray'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('blacklistManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageBlacklistedCustomersAndRestrictions')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconBan size={16} />}
                color="red"
                onClick={() => setAddModalOpen(true)}
              >
                {t('addToBlacklist')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-blacklist">{t('allBlacklist')}</Tabs.Tab>
            <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-blacklist">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <TextInput
                    placeholder={t('searchBlacklist')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'active', label: t('active') },
                      { value: 'expired', label: t('expired') },
                      { value: 'lifted', label: t('lifted') },
                      { value: 'under_review', label: t('underReview') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('type')}
                    leftSection={<IconBan size={16} />}
                    value={typeFilter}
                    onChange={(value) => setTypeFilter(value || '')}
                    data={[
                      { value: '', label: t('allTypes') },
                      { value: 'temporary', label: t('temporary') },
                      { value: 'permanent', label: t('permanent') },
                      { value: 'warning', label: t('warning') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('reason')}
                    leftSection={<IconAlertTriangle size={16} />}
                    value={reasonFilter}
                    onChange={(value) => setReasonFilter(value || '')}
                    data={[
                      { value: '', label: t('allReasons') },
                      { value: 'payment_default', label: t('paymentDefault') },
                      { value: 'vehicle_damage', label: t('vehicleDamage') },
                      { value: 'fraudulent_activity', label: t('fraudulentActivity') },
                      { value: 'policy_violation', label: t('policyViolation') },
                      { value: 'late_returns', label: t('lateReturns') },
                      { value: 'other', label: t('other') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('severity')}
                    leftSection={<IconShield size={16} />}
                    value={severityFilter}
                    onChange={(value) => setSeverityFilter(value || '')}
                    data={[
                      { value: '', label: t('allSeverities') },
                      { value: 'low', label: t('low') },
                      { value: 'medium', label: t('medium') },
                      { value: 'high', label: t('high') },
                      { value: 'critical', label: t('critical') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setTypeFilter('')
                      setReasonFilter('')
                      setSeverityFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Blacklist Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('reason')}</Table.Th>
                    <Table.Th>{t('type')}</Table.Th>
                    <Table.Th>{t('severity')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('impact')}</Table.Th>
                    <Table.Th>{t('blacklistDate')}</Table.Th>
                    <Table.Th>{t('expiryDate')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedEntries.map((entry) => (
                    <Table.Tr key={entry.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color="red">
                            {getInitials(entry.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{entry.customer_name}</Text>
                            <Text size="xs" c="dimmed">{entry.customer_number}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getReasonColor(entry.blacklist_reason)} size="sm">
                          {t(entry.blacklist_reason)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={entry.blacklist_type === 'permanent' ? 'red' : 'orange'} size="sm">
                          {t(entry.blacklist_type)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getSeverityColor(entry.severity_level)} size="sm">
                          {t(entry.severity_level)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(entry.status)} size="sm">
                          {t(entry.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={700} size="sm" c="red">
                            ${entry.financial_impact.toLocaleString()}
                          </Text>
                          {entry.previous_violations > 0 && (
                            <Text size="xs" c="dimmed">
                              {entry.previous_violations} {t('previousViolations')}
                            </Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">
                            {new Date(entry.blacklist_date).toLocaleDateString()}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {t('by')} {entry.added_by}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          {entry.expiry_date ? (
                            <>
                              <Text size="sm">
                                {new Date(entry.expiry_date).toLocaleDateString()}
                              </Text>
                              {new Date(entry.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) && (
                                <Text size="xs" c="orange">{t('expiringSoon')}</Text>
                              )}
                            </>
                          ) : (
                            <Text size="sm" c="dimmed">{t('permanent')}</Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedEntry(entry)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedEntry(entry)
                              setEditModalOpen(true)
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                          {entry.status === 'active' && (
                            <ActionIcon
                              variant="light"
                              size="sm"
                              color="green"
                              onClick={() => {
                                setSelectedEntry(entry)
                                setLiftModalOpen(true)
                              }}
                            >
                              <IconCheck size={16} />
                            </ActionIcon>
                          )}
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="yellow"
                            onClick={() => {
                              setSelectedEntry(entry)
                              setReviewModalOpen(true)
                            }}
                          >
                            <IconRefresh size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('blacklistEntries')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="analytics">
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('blacklistReasonDistribution')}</Title>
                  <Center>
                    <RingProgress
                      size={200}
                      thickness={20}
                      sections={[
                        { value: 30, color: 'red', tooltip: `${t('paymentDefault')}: 30%` },
                        { value: 25, color: 'orange', tooltip: `${t('vehicleDamage')}: 25%` },
                        { value: 20, color: 'dark', tooltip: `${t('fraudulentActivity')}: 20%` },
                        { value: 15, color: 'blue', tooltip: `${t('lateReturns')}: 15%` },
                        { value: 10, color: 'yellow', tooltip: `${t('policyViolation')}: 10%` }
                      ]}
                      label={
                        <Text size="xs" ta="center">
                          {t('blacklistReasons')}
                        </Text>
                      }
                    />
                  </Center>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('financialImpactOverview')}</Title>
                  <Stack>
                    <Group justify="space-between">
                      <Text size="sm">{t('totalFinancialImpact')}</Text>
                      <Text fw={700} size="lg" c="red">${totalFinancialImpact.toLocaleString()}</Text>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm">{t('averageImpactPerCase')}</Text>
                      <Text fw={700} c="orange">
                        ${Math.round(totalFinancialImpact / blacklistEntries.length).toLocaleString()}
                      </Text>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm">{t('criticalCasesImpact')}</Text>
                      <Text fw={700} c="dark">
                        ${blacklistEntries.filter(e => e.severity_level === 'critical').reduce((sum, e) => sum + e.financial_impact, 0).toLocaleString()}
                      </Text>
                    </Group>
                    <Divider />
                    <Group justify="space-between">
                      <Text size="sm">{t('expiringThisMonth')}</Text>
                      <Text fw={700} c="yellow">{expiringEntries}</Text>
                    </Group>
                  </Stack>
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* Add to Blacklist Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('addToBlacklist')}
          size="lg"
        >
          <Stack>
            <Alert color="red" icon={<IconBan size={16} />}>
              <Text fw={500}>{t('blacklistWarning')}</Text>
              <Text size="sm">{t('blacklistActionIsPermanent')}</Text>
            </Alert>

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('customer')}
                  placeholder={t('selectCustomer')}
                  data={[
                    { value: '1', label: 'Ahmed Al-Rashid (<EMAIL>)' },
                    { value: '2', label: 'Sarah Johnson (<EMAIL>)' },
                    { value: '3', label: 'Mohammed Hassan (<EMAIL>)' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('blacklistReason')}
                  placeholder={t('selectReason')}
                  data={[
                    { value: 'payment_default', label: t('paymentDefault') },
                    { value: 'vehicle_damage', label: t('vehicleDamage') },
                    { value: 'fraudulent_activity', label: t('fraudulentActivity') },
                    { value: 'policy_violation', label: t('policyViolation') },
                    { value: 'late_returns', label: t('lateReturns') },
                    { value: 'other', label: t('other') }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('blacklistType')}
                  placeholder={t('selectType')}
                  data={[
                    { value: 'temporary', label: t('temporary') },
                    { value: 'permanent', label: t('permanent') },
                    { value: 'warning', label: t('warning') }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('severityLevel')}
                  placeholder={t('selectSeverity')}
                  data={[
                    { value: 'low', label: t('low') },
                    { value: 'medium', label: t('medium') },
                    { value: 'high', label: t('high') },
                    { value: 'critical', label: t('critical') }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('financialImpact')}
                  placeholder="0"
                  min={0}
                  prefix="$"
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('expiryDate')}
                  placeholder={t('selectExpiryDate')}
                  type="date"
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('incidentDetails')}
                  placeholder={t('enterIncidentDetails')}
                  rows={3}
                  required
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconBan size={16} />} color="red" onClick={() => setAddModalOpen(false)}>
                {t('addToBlacklist')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* View Blacklist Entry Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedEntry ? selectedEntry.blacklist_number : t('blacklistDetails')}
          size="xl"
        >
          {selectedEntry && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('customer')}</Text>
                      <Text fw={500}>{selectedEntry.customer_name}</Text>
                      <Text size="xs" c="dimmed">{selectedEntry.customer_email}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('status')}</Text>
                      <Badge color={getStatusColor(selectedEntry.status)}>
                        {t(selectedEntry.status)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('blacklistReason')}</Text>
                      <Badge color={getReasonColor(selectedEntry.blacklist_reason)}>
                        {t(selectedEntry.blacklist_reason)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('severityLevel')}</Text>
                      <Badge color={getSeverityColor(selectedEntry.severity_level)}>
                        {t(selectedEntry.severity_level)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('blacklistDate')}</Text>
                      <Text fw={500}>{new Date(selectedEntry.blacklist_date).toLocaleDateString()}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('financialImpact')}</Text>
                      <Text fw={700} c="red">${selectedEntry.financial_impact.toLocaleString()}</Text>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <Avatar size="xl" color="red">
                        {getInitials(selectedEntry.customer_name)}
                      </Avatar>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="lg">{selectedEntry.customer_name}</Text>
                        <Text size="sm" c="dimmed">{selectedEntry.customer_number}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <div>
                <Text size="sm" c="dimmed">{t('incidentDetails')}</Text>
                <Text>{selectedEntry.incident_details}</Text>
              </div>

              {selectedEntry.previous_violations > 0 && (
                <Alert color="orange" icon={<IconAlertTriangle size={16} />}>
                  <Text fw={500}>{t('previousViolations')}: {selectedEntry.previous_violations}</Text>
                  <Text size="sm">{t('warningCount')}: {selectedEntry.warning_count}</Text>
                </Alert>
              )}

              {selectedEntry.lifted_date && (
                <Alert color="green" icon={<IconCheck size={16} />}>
                  <Text fw={500}>{t('blacklistLifted')}</Text>
                  <Text size="sm">
                    {t('liftedOn')}: {new Date(selectedEntry.lifted_date).toLocaleDateString()} {t('by')} {selectedEntry.lifted_by}
                  </Text>
                  {selectedEntry.lift_reason && (
                    <Text size="sm">{t('reason')}: {selectedEntry.lift_reason}</Text>
                  )}
                </Alert>
              )}
            </Stack>
          )}
        </Modal>

        {/* Lift Blacklist Modal */}
        <Modal
          opened={liftModalOpen}
          onClose={() => setLiftModalOpen(false)}
          title={t('liftBlacklist')}
          size="md"
        >
          {selectedEntry && (
            <Stack>
              <Alert color="green" icon={<IconCheck size={16} />}>
                <Text fw={500}>{t('liftBlacklistConfirmation')}</Text>
                <Text size="sm">
                  {t('customerWillBeRemovedFromBlacklist')}: {selectedEntry.customer_name}
                </Text>
              </Alert>

              <Textarea
                label={t('liftReason')}
                placeholder={t('enterLiftReason')}
                rows={3}
                required
              />

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setLiftModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button
                  leftSection={<IconCheck size={16} />}
                  color="green"
                  onClick={() => setLiftModalOpen(false)}
                >
                  {t('liftBlacklist')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
