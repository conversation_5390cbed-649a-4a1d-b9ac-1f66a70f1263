import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Center,
  Progress,
  Alert,
  RingProgress,
  Timeline,
  Tooltip
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconFilter,
  IconPlus,
  IconSearch,
  IconTrash,
  IconStar,
  IconUser,
  IconUserPlus,
  IconPhone,
  IconMail,
  IconMapPin,
  IconCalendar,
  IconCurrencyDollar,
  IconBan,
  IconTrendingUp,
  IconUsers,
  IconCalendarEvent,
  IconCreditCard,
  IconFileText
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface CustomerTableData {
  id: string
  customer_number: string
  first_name: string
  last_name: string
  full_name: string
  email: string
  phone: string
  secondary_phone?: string
  date_of_birth?: string
  nationality: string
  address: string
  city: string
  country: string
  postal_code?: string
  license_number: string
  license_expiry: string
  license_country: string
  status: 'active' | 'inactive' | 'vip' | 'blocked' | 'suspended'
  customer_type: 'individual' | 'corporate' | 'government'
  preferred_language: string
  total_reservations: number
  total_spent: number
  average_rental_value: number
  last_rental_date?: string
  next_rental_date?: string
  loyalty_points: number
  credit_limit: number
  outstanding_balance: number
  payment_terms: string
  emergency_contact_name?: string
  emergency_contact_phone?: string
  notes: string
  marketing_consent: boolean
  blacklisted: boolean
  blacklist_reason?: string
  created_by: string
  created_at: string
  updated_at: string
}

interface CustomerReservation {
  id: string
  reservation_number: string
  vehicle_name: string
  pickup_date: string
  return_date: string
  status: string
  total_amount: number
}

export function CustomersComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<CustomerTableData[]>([])
  const [activeTab, setActiveTab] = useState('all-customers')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [countryFilter, setCountryFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [blacklistModalOpen, setBlacklistModalOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerTableData | null>(null)

  // Mock data for development - comprehensive customer structure
  const mockCustomers: CustomerTableData[] = [
    {
      id: '1',
      customer_number: 'CUS-2024-001',
      first_name: 'Ahmed',
      last_name: 'Al-Rashid',
      full_name: 'Ahmed Al-Rashid',
      email: '<EMAIL>',
      phone: '+971501234567',
      secondary_phone: '+971501234568',
      date_of_birth: '1985-03-15',
      nationality: 'Emirati',
      address: '123 Sheikh Zayed Road',
      city: 'Dubai',
      country: 'UAE',
      postal_code: '12345',
      license_number: 'DL123456789',
      license_expiry: '2026-03-15',
      license_country: 'UAE',
      status: 'vip',
      customer_type: 'individual',
      preferred_language: 'en',
      total_reservations: 25,
      total_spent: 18750,
      average_rental_value: 750,
      last_rental_date: '2024-01-20',
      next_rental_date: '2024-02-15',
      loyalty_points: 1875,
      credit_limit: 5000,
      outstanding_balance: 0,
      payment_terms: 'immediate',
      emergency_contact_name: 'Fatima Al-Rashid',
      emergency_contact_phone: '+971501234569',
      notes: 'VIP customer - prefers luxury vehicles',
      marketing_consent: true,
      blacklisted: false,
      created_by: 'admin',
      created_at: '2023-01-15T10:00:00Z',
      updated_at: '2024-01-20T14:30:00Z'
    },
    {
      id: '2',
      customer_number: 'CUS-2024-002',
      first_name: 'Sarah',
      last_name: 'Johnson',
      full_name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+**********',
      date_of_birth: '1990-07-22',
      nationality: 'American',
      address: '456 Business Bay',
      city: 'Dubai',
      country: 'UAE',
      license_number: 'US987654321',
      license_expiry: '2025-07-22',
      license_country: 'USA',
      status: 'active',
      customer_type: 'corporate',
      preferred_language: 'en',
      total_reservations: 12,
      total_spent: 9600,
      average_rental_value: 800,
      last_rental_date: '2024-01-18',
      loyalty_points: 960,
      credit_limit: 10000,
      outstanding_balance: 1200,
      payment_terms: '30_days',
      notes: 'Corporate customer - business rentals',
      marketing_consent: true,
      blacklisted: false,
      created_by: 'admin',
      created_at: '2023-06-10T09:00:00Z',
      updated_at: '2024-01-18T16:45:00Z'
    },
    {
      id: '3',
      customer_number: 'CUS-2024-003',
      first_name: 'Mohammed',
      last_name: 'Hassan',
      full_name: 'Mohammed Hassan',
      email: '<EMAIL>',
      phone: '+971509876543',
      date_of_birth: '1988-11-10',
      nationality: 'Egyptian',
      address: '789 Marina Walk',
      city: 'Dubai',
      country: 'UAE',
      license_number: 'EG456789123',
      license_expiry: '2025-11-10',
      license_country: 'Egypt',
      status: 'active',
      customer_type: 'individual',
      preferred_language: 'ar',
      total_reservations: 8,
      total_spent: 4800,
      average_rental_value: 600,
      last_rental_date: '2024-01-15',
      loyalty_points: 480,
      credit_limit: 2000,
      outstanding_balance: 0,
      payment_terms: 'immediate',
      notes: 'Regular customer - prefers economy vehicles',
      marketing_consent: false,
      blacklisted: false,
      created_by: 'admin',
      created_at: '2023-09-05T14:00:00Z',
      updated_at: '2024-01-15T11:20:00Z'
    },
    {
      id: '4',
      customer_number: 'CUS-2024-004',
      first_name: 'Emma',
      last_name: 'Wilson',
      full_name: 'Emma Wilson',
      email: '<EMAIL>',
      phone: '+442012345678',
      date_of_birth: '1992-04-18',
      nationality: 'British',
      address: '321 Downtown',
      city: 'Dubai',
      country: 'UAE',
      license_number: 'UK123789456',
      license_expiry: '2024-04-18',
      license_country: 'UK',
      status: 'suspended',
      customer_type: 'individual',
      preferred_language: 'en',
      total_reservations: 3,
      total_spent: 1350,
      average_rental_value: 450,
      last_rental_date: '2023-12-01',
      loyalty_points: 135,
      credit_limit: 1000,
      outstanding_balance: 450,
      payment_terms: 'immediate',
      notes: 'Suspended due to overdue payment',
      marketing_consent: true,
      blacklisted: false,
      created_by: 'admin',
      created_at: '2023-11-20T12:00:00Z',
      updated_at: '2024-01-10T09:15:00Z'
    }
  ]

  useEffect(() => {
    loadCustomers()
  }, [])

  const loadCustomers = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await customerService.getAllCustomers()
      setCustomers(mockCustomers)
    } catch (error) {
      console.error('Error loading customers:', error)
    } finally {
      setLoading(false)
    }
  }

  // Filter and search logic
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = !searchQuery || 
      customer.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.customer_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.phone.includes(searchQuery)
    
    const matchesStatus = !statusFilter || customer.status === statusFilter
    const matchesType = !typeFilter || customer.customer_type === typeFilter
    const matchesCountry = !countryFilter || customer.country === countryFilter
    
    return matchesSearch && matchesStatus && matchesType && matchesCountry
  })

  // Pagination
  const totalItems = filteredCustomers.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedCustomers = filteredCustomers.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const totalRevenue = customers.reduce((sum, c) => sum + c.total_spent, 0)
  const averageCustomerValue = totalRevenue / customers.length
  const vipCustomers = customers.filter(c => c.status === 'vip').length
  const activeCustomers = customers.filter(c => c.status === 'active').length

  const stats = [
    {
      label: t('totalCustomers'),
      value: customers.length.toString(),
      color: 'blue',
      icon: IconUsers
    },
    {
      label: t('activeCustomers'),
      value: activeCustomers.toString(),
      color: 'green',
      icon: IconUser
    },
    {
      label: t('vipCustomers'),
      value: vipCustomers.toString(),
      color: 'yellow',
      icon: IconStar
    },
    {
      label: t('averageCustomerValue'),
      value: `$${averageCustomerValue.toFixed(0)}`,
      color: 'teal',
      icon: IconTrendingUp
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'vip': return 'yellow'
      case 'inactive': return 'gray'
      case 'blocked': return 'red'
      case 'suspended': return 'orange'
      default: return 'gray'
    }
  }

  const getCustomerTypeColor = (type: string) => {
    switch (type) {
      case 'individual': return 'blue'
      case 'corporate': return 'purple'
      case 'government': return 'teal'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('customersManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageCustomerDatabaseAndRelationships')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setAddModalOpen(true)}
              >
                {t('addCustomer')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-customers">{t('allCustomers')}</Tabs.Tab>
            <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-customers">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder={t('searchCustomers')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'active', label: t('active') },
                      { value: 'vip', label: t('vip') },
                      { value: 'inactive', label: t('inactive') },
                      { value: 'blocked', label: t('blocked') },
                      { value: 'suspended', label: t('suspended') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('customerType')}
                    leftSection={<IconUser size={16} />}
                    value={typeFilter}
                    onChange={(value) => setTypeFilter(value || '')}
                    data={[
                      { value: '', label: t('allTypes') },
                      { value: 'individual', label: t('individual') },
                      { value: 'corporate', label: t('corporate') },
                      { value: 'government', label: t('government') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('country')}
                    leftSection={<IconMapPin size={16} />}
                    value={countryFilter}
                    onChange={(value) => setCountryFilter(value || '')}
                    data={[
                      { value: '', label: t('allCountries') },
                      { value: 'UAE', label: 'UAE' },
                      { value: 'USA', label: 'USA' },
                      { value: 'UK', label: 'UK' },
                      { value: 'SA', label: 'Saudi Arabia' }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setTypeFilter('')
                      setCountryFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Customers Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('contact')}</Table.Th>
                    <Table.Th>{t('type')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('rentals')}</Table.Th>
                    <Table.Th>{t('totalSpent')}</Table.Th>
                    <Table.Th>{t('loyaltyPoints')}</Table.Th>
                    <Table.Th>{t('lastRental')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedCustomers.map((customer) => (
                    <Table.Tr key={customer.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color={getStatusColor(customer.status)}>
                            {getInitials(customer.full_name)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{customer.full_name}</Text>
                            <Text size="xs" c="dimmed">{customer.customer_number}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Group gap="xs">
                            <IconMail size={12} color="var(--mantine-color-gray-6)" />
                            <Text size="xs">{customer.email}</Text>
                          </Group>
                          <Group gap="xs">
                            <IconPhone size={12} color="var(--mantine-color-gray-6)" />
                            <Text size="xs">{customer.phone}</Text>
                          </Group>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getCustomerTypeColor(customer.customer_type)} size="sm">
                          {t(customer.customer_type)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(customer.status)} size="sm">
                          {t(customer.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{customer.total_reservations}</Text>
                          <Text size="xs" c="dimmed">
                            ${customer.average_rental_value} {t('avg')}
                          </Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={700} size="sm">${customer.total_spent.toLocaleString()}</Text>
                        {customer.outstanding_balance > 0 && (
                          <Text size="xs" c="red">
                            ${customer.outstanding_balance} {t('outstanding')}
                          </Text>
                        )}
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <IconStar size={12} color="var(--mantine-color-yellow-6)" />
                          <Text size="sm">{customer.loyalty_points}</Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          {customer.last_rental_date ? (
                            <>
                              <Text size="sm">
                                {new Date(customer.last_rental_date).toLocaleDateString()}
                              </Text>
                              {customer.next_rental_date && (
                                <Text size="xs" c="blue">
                                  {t('next')}: {new Date(customer.next_rental_date).toLocaleDateString()}
                                </Text>
                              )}
                            </>
                          ) : (
                            <Text size="sm" c="dimmed">{t('noRentals')}</Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedCustomer(customer)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedCustomer(customer)
                              setEditModalOpen(true)
                            }}
                          >
                            <IconEdit size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="green"
                          >
                            <IconPhone size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="orange"
                          >
                            <IconMail size={16} />
                          </ActionIcon>
                          {!customer.blacklisted && (
                            <ActionIcon
                              variant="light"
                              size="sm"
                              color="red"
                              onClick={() => {
                                setSelectedCustomer(customer)
                                setBlacklistModalOpen(true)
                              }}
                            >
                              <IconBan size={16} />
                            </ActionIcon>
                          )}
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('customers')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="analytics">
            <Grid>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('customerStatusDistribution')}</Title>
                  <Center>
                    <RingProgress
                      size={200}
                      thickness={20}
                      sections={[
                        { value: 60, color: 'green', tooltip: `${t('active')}: 60%` },
                        { value: 25, color: 'yellow', tooltip: `${t('vip')}: 25%` },
                        { value: 10, color: 'gray', tooltip: `${t('inactive')}: 10%` },
                        { value: 5, color: 'red', tooltip: `${t('blocked')}: 5%` }
                      ]}
                      label={
                        <Text size="xs" ta="center">
                          {t('customerStatus')}
                        </Text>
                      }
                    />
                  </Center>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('revenueOverview')}</Title>
                  <Stack>
                    <Group justify="space-between">
                      <Text size="sm">{t('totalRevenue')}</Text>
                      <Text fw={700} size="lg" c="green">${totalRevenue.toLocaleString()}</Text>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm">{t('averageCustomerValue')}</Text>
                      <Text fw={700} c="blue">${averageCustomerValue.toFixed(0)}</Text>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm">{t('vipCustomerRevenue')}</Text>
                      <Text fw={700} c="yellow">
                        ${customers.filter(c => c.status === 'vip').reduce((sum, c) => sum + c.total_spent, 0).toLocaleString()}
                      </Text>
                    </Group>
                    <Divider />
                    <Group justify="space-between">
                      <Text size="sm">{t('outstandingBalance')}</Text>
                      <Text fw={700} c="red">
                        ${customers.reduce((sum, c) => sum + c.outstanding_balance, 0).toLocaleString()}
                      </Text>
                    </Group>
                  </Stack>
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* Add Customer Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('addCustomer')}
          size="lg"
          withCloseButton
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label={t('firstName')}
                  placeholder={t('enterFirstName')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('lastName')}
                  placeholder={t('enterLastName')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('email')}
                  placeholder={t('enterEmail')}
                  type="email"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('phone')}
                  placeholder={t('enterPhone')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('dateOfBirth')}
                  placeholder={t('selectDateOfBirth')}
                  type="date"
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('nationality')}
                  placeholder={t('selectNationality')}
                  data={[
                    { value: 'Emirati', label: 'Emirati' },
                    { value: 'American', label: 'American' },
                    { value: 'British', label: 'British' },
                    { value: 'Egyptian', label: 'Egyptian' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('address')}
                  placeholder={t('enterAddress')}
                  rows={2}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <TextInput
                  label={t('city')}
                  placeholder={t('enterCity')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <Select
                  label={t('country')}
                  placeholder={t('selectCountry')}
                  data={[
                    { value: 'UAE', label: 'United Arab Emirates' },
                    { value: 'USA', label: 'United States' },
                    { value: 'UK', label: 'United Kingdom' },
                    { value: 'SA', label: 'Saudi Arabia' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={4}>
                <TextInput
                  label={t('postalCode')}
                  placeholder={t('enterPostalCode')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('licenseNumber')}
                  placeholder={t('enterLicenseNumber')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('licenseExpiry')}
                  placeholder={t('selectLicenseExpiry')}
                  type="date"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('customerType')}
                  placeholder={t('selectCustomerType')}
                  data={[
                    { value: 'individual', label: t('individual') },
                    { value: 'corporate', label: t('corporate') },
                    { value: 'government', label: t('government') }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('creditLimit')}
                  placeholder="0"
                  min={0}
                  prefix="$"
                />
              </Grid.Col>
            </Grid>

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconUserPlus size={16} />} onClick={() => setAddModalOpen(false)}>
                {t('addCustomer')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* View Customer Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedCustomer ? selectedCustomer.full_name : t('customerDetails')}
          size="xl"
          withCloseButton
        >
          {selectedCustomer && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('customerNumber')}</Text>
                      <Text fw={500}>{selectedCustomer.customer_number}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('status')}</Text>
                      <Badge color={getStatusColor(selectedCustomer.status)}>
                        {t(selectedCustomer.status)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('email')}</Text>
                      <Text fw={500}>{selectedCustomer.email}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('phone')}</Text>
                      <Text fw={500}>{selectedCustomer.phone}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('nationality')}</Text>
                      <Text fw={500}>{selectedCustomer.nationality}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('customerType')}</Text>
                      <Badge color={getCustomerTypeColor(selectedCustomer.customer_type)}>
                        {t(selectedCustomer.customer_type)}
                      </Badge>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Text size="sm" c="dimmed">{t('address')}</Text>
                      <Text fw={500}>
                        {selectedCustomer.address}, {selectedCustomer.city}, {selectedCustomer.country}
                      </Text>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <Avatar size="xl" color={getStatusColor(selectedCustomer.status)}>
                        {getInitials(selectedCustomer.full_name)}
                      </Avatar>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="lg">{selectedCustomer.full_name}</Text>
                        <Text size="sm" c="dimmed">{selectedCustomer.customer_number}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <Grid>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('totalReservations')}</Text>
                  <Text fw={700} size="xl">{selectedCustomer.total_reservations}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('totalSpent')}</Text>
                  <Text fw={700} size="xl" c="green">${selectedCustomer.total_spent.toLocaleString()}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('loyaltyPoints')}</Text>
                  <Text fw={700} size="xl" c="yellow">{selectedCustomer.loyalty_points}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('creditLimit')}</Text>
                  <Text fw={700} size="xl" c="blue">${selectedCustomer.credit_limit.toLocaleString()}</Text>
                </Grid.Col>
              </Grid>

              {selectedCustomer.outstanding_balance > 0 && (
                <Alert color="red" icon={<IconAlertTriangle size={16} />}>
                  <Text fw={500}>{t('outstandingBalance')}</Text>
                  <Text size="sm">${selectedCustomer.outstanding_balance} - {t('paymentRequired')}</Text>
                </Alert>
              )}

              {selectedCustomer.notes && (
                <>
                  <Divider />
                  <div>
                    <Text size="sm" c="dimmed">{t('notes')}</Text>
                    <Text>{selectedCustomer.notes}</Text>
                  </div>
                </>
              )}
            </Stack>
          )}
        </Modal>

        {/* Blacklist Modal */}
        <Modal
          opened={blacklistModalOpen}
          onClose={() => setBlacklistModalOpen(false)}
          title={t('blacklistCustomer')}
          size="md"
        >
          {selectedCustomer && (
            <Stack>
              <Alert color="red" icon={<IconBan size={16} />}>
                <Text fw={500}>{t('blacklistWarning')}</Text>
                <Text size="sm">
                  {t('customerWillBeBlacklisted')}: {selectedCustomer.full_name}
                </Text>
              </Alert>

              <Select
                label={t('blacklistReason')}
                placeholder={t('selectBlacklistReason')}
                data={[
                  { value: 'payment_default', label: t('paymentDefault') },
                  { value: 'vehicle_damage', label: t('vehicleDamage') },
                  { value: 'fraudulent_activity', label: t('fraudulentActivity') },
                  { value: 'policy_violation', label: t('policyViolation') },
                  { value: 'other', label: t('other') }
                ]}
                required
              />

              <Textarea
                label={t('blacklistNotes')}
                placeholder={t('enterBlacklistNotes')}
                rows={3}
                required
              />

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setBlacklistModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button
                  leftSection={<IconBan size={16} />}
                  color="red"
                  onClick={() => setBlacklistModalOpen(false)}
                >
                  {t('blacklistCustomer')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}
