import { useState } from 'react'
import {
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Alert,
  Tabs,
  Timeline
} from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconEye,
  IconTrash,
  IconTool,
  IconCalendar,
  IconAlertTriangle,
  IconClock,
  IconCar,
  IconCurrencyDollar
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface MaintenanceRecord {
  id: string
  vehicleId: string
  vehicleName: string
  plateNumber: string
  type: 'scheduled' | 'repair' | 'inspection' | 'service'
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  scheduledDate: string
  completedDate?: string
  cost: number
  mileage: number
  serviceProvider: string
  technician: string
  notes: string
}

export function Maintenance() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Mock data
  const maintenanceRecords: MaintenanceRecord[] = [
    {
      id: '1',
      vehicleId: '1',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      type: 'scheduled',
      description: 'Regular oil change and filter replacement',
      status: 'pending',
      priority: 'medium',
      scheduledDate: '2024-01-20',
      cost: 150,
      mileage: 15000,
      serviceProvider: 'AutoCare',
      technician: 'Ahmed Hassan',
      notes: 'Due for regular maintenance'
    },
    {
      id: '2',
      vehicleId: '2',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      type: 'repair',
      description: 'Brake pad replacement',
      status: 'in-progress',
      priority: 'high',
      scheduledDate: '2024-01-18',
      cost: 450,
      mileage: 25000,
      serviceProvider: 'BMW Service',
      technician: 'Mohammed Ali',
      notes: 'Customer reported squeaking noise'
    },
    {
      id: '3',
      vehicleId: '3',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      type: 'inspection',
      description: 'Annual safety inspection',
      status: 'completed',
      priority: 'medium',
      scheduledDate: '2024-01-15',
      completedDate: '2024-01-15',
      cost: 200,
      mileage: 8000,
      serviceProvider: 'Mercedes Service',
      technician: 'Sarah Ahmed',
      notes: 'All systems checked and approved'
    }
  ]

  const stats = [
    { label: t('pendingMaintenance'), value: '8', color: 'orange', icon: IconClock },
    { label: t('inProgress'), value: '3', color: 'blue', icon: IconTool },
    { label: t('completedThisMonth'), value: '24', color: 'green', icon: IconCar },
    { label: t('totalCostThisMonth'), value: '$12,450', color: 'red', icon: IconCurrencyDollar }
  ]

  const upcomingMaintenance = [
    { vehicle: 'Toyota Camry ABC-123', type: 'Oil Change', date: '2024-01-20', priority: 'medium' },
    { vehicle: 'Honda Civic DEF-456', type: 'Tire Rotation', date: '2024-01-22', priority: 'low' },
    { vehicle: 'BMW X3 GHI-789', type: 'Brake Inspection', date: '2024-01-25', priority: 'high' }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'in-progress': return 'blue'
      case 'pending': return 'orange'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'red'
      case 'high': return 'orange'
      case 'medium': return 'yellow'
      case 'low': return 'green'
      default: return 'gray'
    }
  }

  const filteredRecords = maintenanceRecords.filter(record => {
    const matchesSearch = record.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.plateNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || record.status === statusFilter
    const matchesType = !typeFilter || record.type === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('maintenanceManagement')}
        description={t('scheduleAndTrackVehicleMaintenance')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconCalendar size={16} />}>
              {t('maintenanceCalendar')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('scheduleMaintenance')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="records">{t('maintenanceRecords')}</Tabs.Tab>
          <Tabs.Tab value="schedule">{t('schedule')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Upcoming Maintenance */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('upcomingMaintenance')}</Title>
                  <Button variant="light" size="sm">
                    {t('viewAll')}
                  </Button>
                </Group>
                
                <Stack gap="sm">
                  {upcomingMaintenance.map((item, index) => (
                    <Paper key={index} p="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text fw={500} size="sm">{item.vehicle}</Text>
                          <Text size="xs" c="dimmed">{item.type}</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getPriorityColor(item.priority)} size="sm">
                            {t(item.priority)}
                          </Badge>
                          <Text size="xs" c="dimmed">{item.date}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>

            {/* Maintenance Alerts */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('maintenanceAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('urgentMaintenance')}</Text>
                    <Text size="xs">BMW X5 (XYZ-456) - {t('brakeSystemCheck')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('overdueMaintenance')}</Text>
                    <Text size="xs">Honda Civic (DEF-456) - {t('oilChangeOverdue')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCar size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('highMileageVehicles')}</Text>
                    <Text size="xs">3 {t('vehiclesNeedInspection')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="records" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchMaintenanceRecords')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'pending', label: t('pending') },
                    { value: 'in-progress', label: t('inProgress') },
                    { value: 'completed', label: t('completed') },
                    { value: 'cancelled', label: t('cancelled') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allTypes')}
                  data={[
                    { value: '', label: t('allTypes') },
                    { value: 'scheduled', label: t('scheduled') },
                    { value: 'repair', label: t('repair') },
                    { value: 'inspection', label: t('inspection') },
                    { value: 'service', label: t('service') }
                  ]}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Button variant="light" fullWidth>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Maintenance Records Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('description')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('priority')}</Table.Th>
                  <Table.Th>{t('scheduledDate')}</Table.Th>
                  <Table.Th>{t('cost')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredRecords.map((record) => (
                  <Table.Tr key={record.id}>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{record.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{record.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="light">{t(record.type)}</Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{record.description}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(record.status)}>
                        {t(record.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getPriorityColor(record.priority)} variant="light">
                        {t(record.priority)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>{record.scheduledDate}</Table.Td>
                    <Table.Td>
                      <Text fw={700}>${record.cost}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="schedule" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('maintenanceSchedule')}</Title>
            <Text c="dimmed" mb="lg">{t('maintenanceCalendarWillBeImplementedHere')}</Text>
            
            <Timeline active={1} bulletSize={24} lineWidth={2}>
              <Timeline.Item bullet={<IconClock size={12} />} title={t('today')}>
                <Text c="dimmed" size="sm">
                  BMW X5 - {t('brakeInspection')} (09:00 AM)
                </Text>
                <Text c="dimmed" size="sm">
                  Toyota Camry - {t('oilChange')} (02:00 PM)
                </Text>
              </Timeline.Item>

              <Timeline.Item bullet={<IconCalendar size={12} />} title={t('tomorrow')}>
                <Text c="dimmed" size="sm">
                  Honda Civic - {t('tireRotation')} (10:00 AM)
                </Text>
              </Timeline.Item>

              <Timeline.Item bullet={<IconTool size={12} />} title={t('thisWeek')}>
                <Text c="dimmed" size="sm">
                  Mercedes C-Class - {t('annualInspection')} (Jan 25)
                </Text>
                <Text c="dimmed" size="sm">
                  Nissan Altima - {t('transmissionService')} (Jan 27)
                </Text>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('maintenanceCosts')}</Title>
                <Text c="dimmed">{t('costAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
            
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('vehicleDowntime')}</Title>
                <Text c="dimmed">{t('downtimeAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>

      {/* Add Maintenance Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('scheduleMaintenance')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023 (ABC-123)' },
              { value: '2', label: 'BMW X5 2022 (XYZ-456)' },
              { value: '3', label: 'Mercedes C-Class 2023 (DEF-789)' }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('maintenanceType')}
                placeholder={t('selectType')}
                data={[
                  { value: 'scheduled', label: t('scheduled') },
                  { value: 'repair', label: t('repair') },
                  { value: 'inspection', label: t('inspection') },
                  { value: 'service', label: t('service') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('priority')}
                placeholder={t('selectPriority')}
                data={[
                  { value: 'low', label: t('low') },
                  { value: 'medium', label: t('medium') },
                  { value: 'high', label: t('high') },
                  { value: 'urgent', label: t('urgent') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('description')}
            placeholder={t('enterMaintenanceDescription')}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <DatePickerInput
                label={t('scheduledDate')}
                placeholder={t('selectDate')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <NumberInput
                label={t('estimatedCost')}
                placeholder={t('enterCost')}
                prefix="$"
                min={0}
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('serviceProvider')}
                placeholder={t('enterServiceProvider')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('technician')}
                placeholder={t('enterTechnician')}
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('notes')}
            placeholder={t('enterAdditionalNotes')}
            rows={3}
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={() => setAddModalOpen(false)}>
              {t('scheduleMaintenance')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
