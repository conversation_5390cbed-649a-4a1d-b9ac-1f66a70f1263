import { useState, useMemo } from 'react'
import {
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Alert,
  Tabs,
  Timeline
} from '@mantine/core'
import { DatePickerInput, Calendar } from '@mantine/dates'
import { notifications } from '@mantine/notifications'
import {
  IconPlus,
  IconSearch,
  IconEdit,
  IconEye,
  IconTrash,
  IconTool,
  IconCalendar,
  IconAlertTriangle,
  IconClock,
  IconCar,
  IconCurrencyDollar,
  IconCalendarEvent,
  IconBell,
  IconFileExport
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'
import { useAppStore, MaintenanceRecord } from '../../store/useAppStore'



export function Maintenance() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Schedule tab filters
  const [scheduleVehicleFilter, setScheduleVehicleFilter] = useState<string>('')
  const [scheduleTypeFilter, setScheduleTypeFilter] = useState<string>('')

  // Reminder modal state
  const [reminderModalOpen, setReminderModalOpen] = useState(false)

  // Calendar modal state
  const [calendarModalOpen, setCalendarModalOpen] = useState(false)

  // Get data from store
  const { vehicles, maintenanceRecords, setMaintenanceRecords } = useAppStore()

  // Initialize with sample data if empty
  useMemo(() => {
    if (maintenanceRecords.length === 0) {
      const sampleRecords: MaintenanceRecord[] = [
        {
          id: '1',
          vehicle_id: '1',
          maintenance_type: 'service',
          description: 'Regular oil change and filter replacement',
          scheduled_date: '2024-01-20',
          status: 'scheduled',
          cost: 150,
          mileage: 15000,
          service_provider: 'AutoCare',
          notes: 'Due for regular maintenance',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          vehicle_id: '2',
          maintenance_type: 'repair',
          description: 'Brake pad replacement',
          scheduled_date: '2024-01-18',
          status: 'in_progress',
          cost: 450,
          mileage: 25000,
          service_provider: 'BMW Service',
          notes: 'Customer reported squeaking noise',
          created_at: '2024-01-16T09:00:00Z',
          updated_at: '2024-01-18T14:00:00Z'
        },
        {
          id: '3',
          vehicle_id: '3',
          maintenance_type: 'inspection',
          description: 'Annual safety inspection',
          scheduled_date: '2024-01-15',
          completed_date: '2024-01-15',
          status: 'completed',
          cost: 200,
          mileage: 18000,
          service_provider: 'Mercedes Service',
          notes: 'Passed inspection',
          created_at: '2024-01-10T11:00:00Z',
          updated_at: '2024-01-15T16:00:00Z'
        }
      ]
      setMaintenanceRecords(sampleRecords)
    }
  }, [maintenanceRecords.length, setMaintenanceRecords])

  // Transform maintenance records for display
  const displayRecords = useMemo(() => {
    return maintenanceRecords.map(record => {
      const vehicle = vehicles.find(v => v.id === record.vehicle_id)
      return {
        ...record,
        vehicleName: vehicle ? `${vehicle.make} ${vehicle.model} ${vehicle.year}` : 'Unknown Vehicle',
        plateNumber: vehicle?.plate_number || 'N/A',
        type: record.maintenance_type,
        scheduledDate: record.scheduled_date,
        priority: 'medium' as const // Default priority since it's not in the store model
      }
    })
  }, [maintenanceRecords, vehicles])

  // Calculate real stats from maintenance data
  const stats = useMemo(() => {
    const pending = displayRecords.filter(r => r.status === 'scheduled').length
    const inProgress = displayRecords.filter(r => r.status === 'in_progress').length
    const completedThisMonth = displayRecords.filter(r =>
      r.status === 'completed' &&
      new Date(r.completed_date || r.scheduled_date).getMonth() === new Date().getMonth()
    ).length
    const totalCostThisMonth = displayRecords
      .filter(r =>
        r.status === 'completed' &&
        new Date(r.completed_date || r.scheduled_date).getMonth() === new Date().getMonth()
      )
      .reduce((sum, r) => sum + (r.cost || 0), 0)

    return [
      { label: t('pendingMaintenance'), value: pending.toString(), color: 'orange', icon: IconClock },
      { label: t('inProgress'), value: inProgress.toString(), color: 'blue', icon: IconTool },
      { label: t('completedThisMonth'), value: completedThisMonth.toString(), color: 'green', icon: IconCar },
      { label: t('totalCostThisMonth'), value: `$${totalCostThisMonth.toLocaleString()}`, color: 'red', icon: IconCurrencyDollar }
    ]
  }, [displayRecords, t])

  // Generate upcoming maintenance from real data
  const upcomingMaintenance = useMemo(() => {
    return displayRecords
      .filter(r => r.status === 'scheduled')
      .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
      .slice(0, 3)
      .map(r => ({
        vehicle: `${r.vehicleName} ${r.plateNumber}`,
        type: r.maintenance_type.charAt(0).toUpperCase() + r.maintenance_type.slice(1),
        date: r.scheduled_date,
        priority: r.priority
      }))
  }, [displayRecords])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'in_progress': return 'blue'
      case 'scheduled': return 'orange'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'red'
      case 'high': return 'orange'
      case 'medium': return 'yellow'
      case 'low': return 'green'
      default: return 'gray'
    }
  }

  const filteredRecords = displayRecords.filter(record => {
    const matchesSearch = record.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.plateNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         record.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || record.status === statusFilter
    const matchesType = !typeFilter || record.type === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  // Filtered records for schedule tab
  const scheduleFilteredRecords = displayRecords.filter(record => {
    const matchesVehicle = !scheduleVehicleFilter || record.vehicle_id === scheduleVehicleFilter
    const matchesType = !scheduleTypeFilter || record.maintenance_type === scheduleTypeFilter
    const isScheduled = record.status === 'scheduled'

    return matchesVehicle && matchesType && isScheduled
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('maintenanceManagement')}
        description={t('scheduleAndTrackVehicleMaintenance')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconCalendar size={16} />}>
              {t('maintenanceCalendar')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('scheduleMaintenance')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="records">{t('maintenanceRecords')}</Tabs.Tab>
          <Tabs.Tab value="schedule">{t('schedule')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Upcoming Maintenance */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('upcomingMaintenance')}</Title>
                  <Button variant="light" size="sm">
                    {t('viewAll')}
                  </Button>
                </Group>
                
                <Stack gap="sm">
                  {upcomingMaintenance.map((item, index) => (
                    <Paper key={index} p="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text fw={500} size="sm">{item.vehicle}</Text>
                          <Text size="xs" c="dimmed">{item.type}</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getPriorityColor(item.priority)} size="sm">
                            {t(item.priority)}
                          </Badge>
                          <Text size="xs" c="dimmed">{item.date}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>

            {/* Maintenance Alerts */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('maintenanceAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('urgentMaintenance')}</Text>
                    <Text size="xs">BMW X5 (XYZ-456) - {t('brakeSystemCheck')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('overdueMaintenance')}</Text>
                    <Text size="xs">Honda Civic (DEF-456) - {t('oilChangeOverdue')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCar size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('highMileageVehicles')}</Text>
                    <Text size="xs">3 {t('vehiclesNeedInspection')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="records" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchMaintenanceRecords')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'scheduled', label: t('pending') },
                    { value: 'in_progress', label: t('inProgress') },
                    { value: 'completed', label: t('completed') },
                    { value: 'cancelled', label: t('cancelled') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allTypes')}
                  data={[
                    { value: '', label: t('allTypes') },
                    { value: 'service', label: t('service') },
                    { value: 'repair', label: t('repair') },
                    { value: 'inspection', label: t('inspection') }
                  ]}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Button variant="light" fullWidth>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Maintenance Records Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('description')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('priority')}</Table.Th>
                  <Table.Th>{t('scheduledDate')}</Table.Th>
                  <Table.Th>{t('cost')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredRecords.map((record) => (
                  <Table.Tr key={record.id}>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{record.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{record.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="light">{t(record.type)}</Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{record.description}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(record.status)}>
                        {record.status === 'scheduled' ? t('pending') :
                         record.status === 'in_progress' ? t('inProgress') :
                         t(record.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getPriorityColor(record.priority)} variant="light">
                        {t(record.priority)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>{record.scheduledDate}</Table.Td>
                    <Table.Td>
                      <Text fw={700}>${record.cost}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="schedule" pt="md">
          <Grid>
            {/* Schedule Controls */}
            <Grid.Col span={12}>
              <Card withBorder mb="lg">
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('maintenanceSchedule')}</Title>
                  <Group>
                    <Select
                      placeholder="Filter by Vehicle"
                      data={[
                        { value: '', label: 'All Vehicles' },
                        ...vehicles.map(v => ({
                          value: v.id,
                          label: `${v.make} ${v.model} (${v.plate_number})`
                        }))
                      ]}
                      value={scheduleVehicleFilter}
                      onChange={(value) => setScheduleVehicleFilter(value || '')}
                      w={200}
                    />
                    <Select
                      placeholder="Filter by Type"
                      data={[
                        { value: '', label: 'All Types' },
                        { value: 'service', label: 'Service' },
                        { value: 'repair', label: 'Repair' },
                        { value: 'inspection', label: 'Inspection' }
                      ]}
                      value={scheduleTypeFilter}
                      onChange={(value) => setScheduleTypeFilter(value || '')}
                      w={150}
                    />
                    <Button
                      variant="light"
                      leftSection={<IconCalendar size={16} />}
                      onClick={() => setCalendarModalOpen(true)}
                    >
                      Calendar View
                    </Button>
                  </Group>
                </Group>
              </Card>
            </Grid.Col>

            {/* Timeline View */}
            <Grid.Col span={{ base: 12, lg: 8 }}>
              <Card withBorder>
                <Title order={5} mb="md">Schedule Timeline</Title>

                <Timeline active={1} bulletSize={24} lineWidth={2}>
                  <Timeline.Item bullet={<IconClock size={12} />} title={t('today')} color="red">
                    <Stack gap="xs">
                      {scheduleFilteredRecords
                        .filter(r => new Date(r.scheduled_date).toDateString() === new Date().toDateString())
                        .map(r => (
                          <Paper key={r.id} p="sm" withBorder>
                            <Group justify="space-between">
                              <div>
                                <Text fw={500} size="sm">{r.vehicleName}</Text>
                                <Text size="xs" c="dimmed">{r.maintenance_type.charAt(0).toUpperCase() + r.maintenance_type.slice(1)}</Text>
                                <Text size="xs" c="dimmed">{r.description}</Text>
                              </div>
                              <div style={{ textAlign: 'right' }}>
                                <Text size="xs" fw={500}>
                                  {new Date(r.scheduled_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </Text>
                                <Badge size="xs" color={getPriorityColor(r.priority)}>
                                  {t(r.priority)}
                                </Badge>
                                <Text size="xs" c="dimmed">${r.cost}</Text>
                              </div>
                            </Group>
                          </Paper>
                        ))}
                      {scheduleFilteredRecords.filter(r => new Date(r.scheduled_date).toDateString() === new Date().toDateString()).length === 0 && (
                        <Text c="dimmed" size="sm" ta="center" py="md">No maintenance scheduled for today</Text>
                      )}
                    </Stack>
                  </Timeline.Item>

                  <Timeline.Item bullet={<IconCalendar size={12} />} title={t('tomorrow')} color="orange">
                    <Stack gap="xs">
                      {scheduleFilteredRecords
                        .filter(r => {
                          const tomorrow = new Date()
                          tomorrow.setDate(tomorrow.getDate() + 1)
                          return new Date(r.scheduled_date).toDateString() === tomorrow.toDateString()
                        })
                        .map(r => (
                          <Paper key={r.id} p="sm" withBorder>
                            <Group justify="space-between">
                              <div>
                                <Text fw={500} size="sm">{r.vehicleName}</Text>
                                <Text size="xs" c="dimmed">{r.maintenance_type.charAt(0).toUpperCase() + r.maintenance_type.slice(1)}</Text>
                                <Text size="xs" c="dimmed">{r.description}</Text>
                              </div>
                              <div style={{ textAlign: 'right' }}>
                                <Text size="xs" fw={500}>
                                  {new Date(r.scheduled_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </Text>
                                <Badge size="xs" color={getPriorityColor(r.priority)}>
                                  {t(r.priority)}
                                </Badge>
                                <Text size="xs" c="dimmed">${r.cost}</Text>
                              </div>
                            </Group>
                          </Paper>
                        ))}
                      {scheduleFilteredRecords.filter(r => {
                        const tomorrow = new Date()
                        tomorrow.setDate(tomorrow.getDate() + 1)
                        return new Date(r.scheduled_date).toDateString() === tomorrow.toDateString()
                      }).length === 0 && (
                        <Text c="dimmed" size="sm" ta="center" py="md">No maintenance scheduled for tomorrow</Text>
                      )}
                    </Stack>
                  </Timeline.Item>

                  <Timeline.Item bullet={<IconTool size={12} />} title={t('thisWeek')} color="blue">
                    <Stack gap="xs">
                      {scheduleFilteredRecords
                        .filter(r => {
                          const today = new Date()
                          const weekFromNow = new Date()
                          weekFromNow.setDate(today.getDate() + 7)
                          const scheduleDate = new Date(r.scheduled_date)
                          return scheduleDate > today && scheduleDate <= weekFromNow
                        })
                        .map(r => (
                          <Paper key={r.id} p="sm" withBorder>
                            <Group justify="space-between">
                              <div>
                                <Text fw={500} size="sm">{r.vehicleName}</Text>
                                <Text size="xs" c="dimmed">{r.maintenance_type.charAt(0).toUpperCase() + r.maintenance_type.slice(1)}</Text>
                                <Text size="xs" c="dimmed">{r.description}</Text>
                              </div>
                              <div style={{ textAlign: 'right' }}>
                                <Text size="xs" fw={500}>
                                  {new Date(r.scheduled_date).toLocaleDateString()}
                                </Text>
                                <Badge size="xs" color={getPriorityColor(r.priority)}>
                                  {t(r.priority)}
                                </Badge>
                                <Text size="xs" c="dimmed">${r.cost}</Text>
                              </div>
                            </Group>
                          </Paper>
                        ))}
                      {scheduleFilteredRecords.filter(r => {
                        const today = new Date()
                        const weekFromNow = new Date()
                        weekFromNow.setDate(today.getDate() + 7)
                        const scheduleDate = new Date(r.scheduled_date)
                        return scheduleDate > today && scheduleDate <= weekFromNow
                      }).length === 0 && (
                        <Text c="dimmed" size="sm" ta="center" py="md">No maintenance scheduled for this week</Text>
                      )}
                    </Stack>
                  </Timeline.Item>

                  <Timeline.Item bullet={<IconCalendarEvent size={12} />} title="Next 30 Days" color="green">
                    <Stack gap="xs">
                      {scheduleFilteredRecords
                        .filter(r => {
                          const today = new Date()
                          const monthFromNow = new Date()
                          monthFromNow.setDate(today.getDate() + 30)
                          const scheduleDate = new Date(r.scheduled_date)
                          return scheduleDate > today && scheduleDate <= monthFromNow
                        })
                        .slice(0, 5)
                        .map(r => (
                          <Paper key={r.id} p="sm" withBorder>
                            <Group justify="space-between">
                              <div>
                                <Text fw={500} size="sm">{r.vehicleName}</Text>
                                <Text size="xs" c="dimmed">{r.maintenance_type.charAt(0).toUpperCase() + r.maintenance_type.slice(1)}</Text>
                                <Text size="xs" c="dimmed">{r.description}</Text>
                              </div>
                              <div style={{ textAlign: 'right' }}>
                                <Text size="xs" fw={500}>
                                  {new Date(r.scheduled_date).toLocaleDateString()}
                                </Text>
                                <Badge size="xs" color={getPriorityColor(r.priority)}>
                                  {t(r.priority)}
                                </Badge>
                                <Text size="xs" c="dimmed">${r.cost}</Text>
                              </div>
                            </Group>
                          </Paper>
                        ))}
                      {scheduleFilteredRecords.filter(r => {
                        const today = new Date()
                        const monthFromNow = new Date()
                        monthFromNow.setDate(today.getDate() + 30)
                        const scheduleDate = new Date(r.scheduled_date)
                        return scheduleDate > today && scheduleDate <= monthFromNow
                      }).length === 0 && (
                        <Text c="dimmed" size="sm" ta="center" py="md">No maintenance scheduled for next 30 days</Text>
                      )}
                    </Stack>
                  </Timeline.Item>
                </Timeline>
              </Card>
            </Grid.Col>

            {/* Quick Stats & Actions */}
            <Grid.Col span={{ base: 12, lg: 4 }}>
              <Stack>
                {/* Schedule Summary */}
                <Card withBorder>
                  <Title order={5} mb="md">Schedule Summary</Title>
                  <Stack gap="sm">
                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Today</Text>
                      <Badge color="red" variant="light">
                        {displayRecords.filter(r => r.status === 'scheduled' && new Date(r.scheduled_date).toDateString() === new Date().toDateString()).length}
                      </Badge>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">This Week</Text>
                      <Badge color="blue" variant="light">
                        {displayRecords.filter(r => {
                          const today = new Date()
                          const weekFromNow = new Date()
                          weekFromNow.setDate(today.getDate() + 7)
                          const scheduleDate = new Date(r.scheduled_date)
                          return r.status === 'scheduled' && scheduleDate >= today && scheduleDate <= weekFromNow
                        }).length}
                      </Badge>
                    </Group>
                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">This Month</Text>
                      <Badge color="green" variant="light">
                        {displayRecords.filter(r => {
                          const today = new Date()
                          const scheduleDate = new Date(r.scheduled_date)
                          return r.status === 'scheduled' &&
                                 scheduleDate.getMonth() === today.getMonth() &&
                                 scheduleDate.getFullYear() === today.getFullYear()
                        }).length}
                      </Badge>
                    </Group>
                    <Divider />
                    <Group justify="space-between">
                      <Text size="sm" fw={500}>Total Scheduled</Text>
                      <Text size="sm" fw={700}>
                        {displayRecords.filter(r => r.status === 'scheduled').length}
                      </Text>
                    </Group>
                  </Stack>
                </Card>

                {/* Quick Actions */}
                <Card withBorder>
                  <Title order={5} mb="md">Quick Actions</Title>
                  <Stack gap="sm">
                    <Button
                      variant="light"
                      fullWidth
                      leftSection={<IconPlus size={16} />}
                      onClick={() => setAddModalOpen(true)}
                    >
                      Schedule Maintenance
                    </Button>
                    <Button
                      variant="light"
                      fullWidth
                      leftSection={<IconCalendar size={16} />}
                      onClick={() => {
                        // Switch to records tab to show all maintenance
                        setActiveTab('records')
                      }}
                    >
                      View All Records
                    </Button>
                    <Button
                      variant="light"
                      fullWidth
                      leftSection={<IconBell size={16} />}
                      onClick={() => setReminderModalOpen(true)}
                    >
                      Set Reminders
                    </Button>
                    <Button
                      variant="light"
                      fullWidth
                      leftSection={<IconFileExport size={16} />}
                      onClick={() => {
                        // Export schedule data
                        const scheduleData = displayRecords
                          .filter(r => r.status === 'scheduled')
                          .map(r => ({
                            vehicle: r.vehicleName,
                            type: r.maintenance_type,
                            description: r.description,
                            date: r.scheduled_date,
                            cost: r.cost,
                            priority: r.priority
                          }))

                        if (scheduleData.length === 0) {
                          notifications.show({
                            title: 'No Data to Export',
                            message: 'There are no scheduled maintenance records to export.',
                            color: 'orange',
                            icon: <IconFileExport size={16} />
                          })
                          return
                        }

                        const dataStr = JSON.stringify(scheduleData, null, 2)
                        const dataBlob = new Blob([dataStr], { type: 'application/json' })
                        const url = URL.createObjectURL(dataBlob)
                        const link = document.createElement('a')
                        link.href = url
                        link.download = 'maintenance-schedule.json'
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link)
                        URL.revokeObjectURL(url)

                        notifications.show({
                          title: 'Export Successful',
                          message: `Exported ${scheduleData.length} maintenance records to maintenance-schedule.json`,
                          color: 'green',
                          icon: <IconFileExport size={16} />
                        })
                      }}
                    >
                      Export Schedule
                    </Button>
                  </Stack>
                </Card>

              </Stack>
            </Grid.Col>
          </Grid>

          {/* Overdue Maintenance - Full Width Row */}
          <Card withBorder mt="lg">
            <Group justify="space-between" mb="md">
              <Title order={4} c="red">⚠️ Overdue Maintenance</Title>
              <Badge color="red" variant="light">
                {displayRecords.filter(r => r.status === 'scheduled' && new Date(r.scheduled_date) < new Date()).length} Overdue
              </Badge>
            </Group>

            {displayRecords.filter(r => r.status === 'scheduled' && new Date(r.scheduled_date) < new Date()).length === 0 ? (
              <Alert color="green" variant="light" icon={<IconCar size={16} />}>
                <Text fw={500}>Great! No overdue maintenance</Text>
                <Text size="sm" c="dimmed">All scheduled maintenance is up to date.</Text>
              </Alert>
            ) : (
              <Grid>
                {displayRecords
                  .filter(r => r.status === 'scheduled' && new Date(r.scheduled_date) < new Date())
                  .map(r => {
                    const daysOverdue = Math.floor((new Date().getTime() - new Date(r.scheduled_date).getTime()) / (1000 * 60 * 60 * 24))
                    return (
                      <Grid.Col key={r.id} span={{ base: 12, md: 6, lg: 4 }}>
                        <Alert color="red" variant="light" p="md">
                          <Group justify="space-between" mb="xs">
                            <Text fw={600} size="sm">{r.vehicleName}</Text>
                            <Badge color="red" size="xs">
                              {daysOverdue} day{daysOverdue !== 1 ? 's' : ''} overdue
                            </Badge>
                          </Group>
                          <Text size="sm" mb="xs">
                            <strong>{r.maintenance_type.charAt(0).toUpperCase() + r.maintenance_type.slice(1)}</strong>
                          </Text>
                          <Text size="xs" c="dimmed" mb="xs">{r.description}</Text>
                          <Group justify="space-between" align="center">
                            <Text size="xs" c="dimmed">
                              Due: {new Date(r.scheduled_date).toLocaleDateString()}
                            </Text>
                            <Text size="xs" fw={500} c="red">
                              ${r.cost}
                            </Text>
                          </Group>
                          <Group mt="sm" gap="xs">
                            <Button
                              size="xs"
                              variant="filled"
                              color="red"
                              fullWidth
                              onClick={() => {
                                // Update the overdue maintenance to in_progress status
                                const updatedRecords = maintenanceRecords.map(record =>
                                  record.id === r.id
                                    ? { ...record, status: 'in_progress' as const }
                                    : record
                                )
                                setMaintenanceRecords(updatedRecords)

                                // Show success notification
                                notifications.show({
                                  title: 'Maintenance Scheduled',
                                  message: `${r.vehicleName} maintenance has been moved to in progress.`,
                                  color: 'green',
                                  icon: <IconClock size={16} />
                                })
                              }}
                            >
                              Schedule Now
                            </Button>
                          </Group>
                        </Alert>
                      </Grid.Col>
                    )
                  })}
              </Grid>
            )}
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder h="100%">
                <Title order={4} mb="md">{t('maintenanceCosts')}</Title>
                <Stack gap="md" h="100%" justify="space-between">
                  <Stack gap="md">
                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Total Maintenance Cost</Text>
                      <Text fw={700} size="lg">
                        ${displayRecords.reduce((sum, r) => sum + (r.cost || 0), 0).toLocaleString()}
                      </Text>
                    </Group>

                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Average Cost per Service</Text>
                      <Text fw={500}>
                        ${displayRecords.length > 0 ? Math.round(displayRecords.reduce((sum, r) => sum + (r.cost || 0), 0) / displayRecords.length).toLocaleString() : '0'}
                      </Text>
                    </Group>

                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">This Month's Spending</Text>
                      <Text fw={500} c="blue">
                        ${displayRecords
                          .filter(r => new Date(r.scheduled_date).getMonth() === new Date().getMonth())
                          .reduce((sum, r) => sum + (r.cost || 0), 0)
                          .toLocaleString()}
                      </Text>
                    </Group>
                  </Stack>

                  <div>
                    <Divider mb="md" />
                    <Text size="sm" fw={500} mb="xs">Cost by Type</Text>
                    {['service', 'repair', 'inspection'].map(type => {
                      const typeCost = displayRecords
                        .filter(r => r.maintenance_type === type)
                        .reduce((sum, r) => sum + (r.cost || 0), 0)
                      return (
                        <Group key={type} justify="space-between" mb="xs">
                          <Text size="xs" c="dimmed" tt="capitalize">{type}</Text>
                          <Text size="xs">${typeCost.toLocaleString()}</Text>
                        </Group>
                      )
                    })}
                  </div>
                </Stack>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder h="100%">
                <Title order={4} mb="md">{t('vehicleDowntime')}</Title>
                <Stack gap="md" h="100%" justify="space-between">
                  <Stack gap="md">
                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Vehicles in Maintenance</Text>
                      <Text fw={700} size="lg" c="orange">
                        {displayRecords.filter(r => r.status === 'in_progress').length}
                      </Text>
                    </Group>

                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Completed This Month</Text>
                      <Text fw={500} c="green">
                        {displayRecords.filter(r =>
                          r.status === 'completed' &&
                          new Date(r.completed_date || r.scheduled_date).getMonth() === new Date().getMonth()
                        ).length}
                      </Text>
                    </Group>

                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Pending Maintenance</Text>
                      <Text fw={500} c="yellow">
                        {displayRecords.filter(r => r.status === 'scheduled').length}
                      </Text>
                    </Group>
                  </Stack>

                  <div>
                    <Divider mb="md" />
                    <Text size="sm" fw={500} mb="xs">Recent Activity</Text>
                    <Stack gap="xs" mih={80}>
                      {displayRecords
                        .filter(r => r.status === 'completed')
                        .sort((a, b) => new Date(b.completed_date || b.scheduled_date).getTime() - new Date(a.completed_date || a.scheduled_date).getTime())
                        .slice(0, 3)
                        .map(r => (
                          <Group key={r.id} justify="space-between">
                            <div>
                              <Text size="xs" fw={500}>{r.vehicleName}</Text>
                              <Text size="xs" c="dimmed">{r.maintenance_type}</Text>
                            </div>
                            <Text size="xs" c="dimmed">
                              {new Date(r.completed_date || r.scheduled_date).toLocaleDateString()}
                            </Text>
                          </Group>
                        ))}
                      {displayRecords.filter(r => r.status === 'completed').length === 0 && (
                        <Text size="xs" c="dimmed">No completed maintenance yet</Text>
                      )}
                    </Stack>
                  </div>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>

      {/* Add Maintenance Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('scheduleMaintenance')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={vehicles.map(v => ({
              value: v.id,
              label: `${v.make} ${v.model} ${v.year} (${v.plate_number})`
            }))}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('maintenanceType')}
                placeholder={t('selectType')}
                data={[
                  { value: 'service', label: t('service') },
                  { value: 'repair', label: t('repair') },
                  { value: 'inspection', label: t('inspection') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('priority')}
                placeholder={t('selectPriority')}
                data={[
                  { value: 'low', label: t('low') },
                  { value: 'medium', label: t('medium') },
                  { value: 'high', label: t('high') },
                  { value: 'urgent', label: t('urgent') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('description')}
            placeholder={t('enterMaintenanceDescription')}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <DatePickerInput
                label={t('scheduledDate')}
                placeholder={t('selectDate')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <NumberInput
                label={t('estimatedCost')}
                placeholder={t('enterCost')}
                prefix="$"
                min={0}
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('serviceProvider')}
                placeholder={t('enterServiceProvider')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('technician')}
                placeholder={t('enterTechnician')}
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('notes')}
            placeholder={t('enterAdditionalNotes')}
            rows={3}
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={() => setAddModalOpen(false)}>
              {t('scheduleMaintenance')}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Set Reminders Modal */}
      <Modal
        opened={reminderModalOpen}
        onClose={() => setReminderModalOpen(false)}
        title="Set Maintenance Reminders"
        size="lg"
      >
        <Stack>
          <Text size="sm" c="dimmed" mb="md">
            Configure automatic reminders for upcoming maintenance. You'll receive notifications before scheduled maintenance dates.
          </Text>

          <Select
            label="Select Maintenance Record"
            placeholder="Choose maintenance to set reminder for"
            data={displayRecords
              .filter(r => r.status === 'scheduled')
              .map(r => ({
                value: r.id,
                label: `${r.vehicleName} - ${r.maintenance_type} (${r.scheduled_date})`
              }))}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label="Reminder Time"
                placeholder="When to remind"
                data={[
                  { value: '1', label: '1 day before' },
                  { value: '3', label: '3 days before' },
                  { value: '7', label: '1 week before' },
                  { value: '14', label: '2 weeks before' },
                  { value: '30', label: '1 month before' }
                ]}
                defaultValue="3"
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label="Notification Method"
                placeholder="How to notify"
                data={[
                  { value: 'email', label: 'Email' },
                  { value: 'sms', label: 'SMS' },
                  { value: 'push', label: 'Push Notification' },
                  { value: 'all', label: 'All Methods' }
                ]}
                defaultValue="email"
                required
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label="Custom Message (Optional)"
            placeholder="Add a custom message to the reminder..."
            rows={3}
          />

          <Alert color="blue" icon={<IconBell size={16} />}>
            <Text size="sm">
              <strong>Note:</strong> Reminders will be sent automatically based on your settings.
              You can modify or cancel reminders anytime from the maintenance records.
            </Text>
          </Alert>

          <Divider />

          <Group justify="space-between">
            <Button variant="light" onClick={() => setReminderModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                setReminderModalOpen(false)
                notifications.show({
                  title: 'Reminder Set Successfully',
                  message: 'You will be notified before the scheduled maintenance date.',
                  color: 'green',
                  icon: <IconBell size={16} />
                })
              }}
            >
              Set Reminder
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Calendar View Modal */}
      <Modal
        opened={calendarModalOpen}
        onClose={() => setCalendarModalOpen(false)}
        title="Maintenance Calendar"
        size="xl"
      >
        <Stack>
          <Text size="sm" c="dimmed" mb="md">
            View and manage maintenance schedules in calendar format. Click on dates to see scheduled maintenance.
          </Text>

          <Grid>
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Card withBorder>
                <Calendar
                  size="lg"
                  renderDay={(date) => {
                    const dateStr = date.toISOString().split('T')[0]
                    const maintenanceOnDate = displayRecords.filter(r =>
                      r.status === 'scheduled' && r.scheduled_date.startsWith(dateStr)
                    )

                    return (
                      <div style={{ position: 'relative' }}>
                        <div>{date.getDate()}</div>
                        {maintenanceOnDate.length > 0 && (
                          <div
                            style={{
                              position: 'absolute',
                              bottom: 2,
                              right: 2,
                              width: 6,
                              height: 6,
                              borderRadius: '50%',
                              backgroundColor: maintenanceOnDate.some(r => new Date(r.scheduled_date) < new Date()) ? '#fa5252' : '#228be6'
                            }}
                          />
                        )}
                      </div>
                    )
                  }}
                />
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card withBorder>
                <Title order={5} mb="md">Upcoming Maintenance</Title>
                <Stack gap="xs" mah={400} style={{ overflowY: 'auto' }}>
                  {displayRecords
                    .filter(r => r.status === 'scheduled')
                    .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
                    .slice(0, 10)
                    .map(r => {
                      const isOverdue = new Date(r.scheduled_date) < new Date()
                      return (
                        <Paper key={r.id} p="sm" withBorder>
                          <Group justify="space-between" mb="xs">
                            <Text size="sm" fw={500}>{r.vehicleName}</Text>
                            <Badge size="xs" color={isOverdue ? 'red' : 'blue'}>
                              {new Date(r.scheduled_date).toLocaleDateString()}
                            </Badge>
                          </Group>
                          <Text size="xs" c="dimmed" mb="xs">
                            {r.maintenance_type.charAt(0).toUpperCase() + r.maintenance_type.slice(1)}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {r.description}
                          </Text>
                          {isOverdue && (
                            <Badge size="xs" color="red" variant="light" mt="xs">
                              Overdue
                            </Badge>
                          )}
                        </Paper>
                      )
                    })}
                  {displayRecords.filter(r => r.status === 'scheduled').length === 0 && (
                    <Text size="sm" c="dimmed" ta="center">No scheduled maintenance</Text>
                  )}
                </Stack>
              </Card>

              <Card withBorder mt="md">
                <Title order={5} mb="md">Calendar Legend</Title>
                <Stack gap="xs">
                  <Group gap="xs">
                    <div style={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: '#228be6' }} />
                    <Text size="xs">Scheduled Maintenance</Text>
                  </Group>
                  <Group gap="xs">
                    <div style={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: '#fa5252' }} />
                    <Text size="xs">Overdue Maintenance</Text>
                  </Group>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={() => setCalendarModalOpen(false)}>
              Close
            </Button>
            <Button onClick={() => {
              setCalendarModalOpen(false)
              setAddModalOpen(true)
            }}>
              Schedule New Maintenance
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
