import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Alert,
  Tabs,
  FileInput} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconCurrencyDollar,
  IconDownload,
  IconEdit,
  IconEye,
  IconPhoto,
  IconPlus,
  IconPrinter,
  IconSearch,
  IconTool,
  IconTrash
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface RepairOrder {
  id: string
  orderNumber: string
  vehicleId: string
  vehicleName: string
  plateNumber: string
  issueType: 'mechanical' | 'electrical' | 'bodywork' | 'accident' | 'wear'
  description: string
  status: 'pending' | 'diagnosed' | 'approved' | 'in-repair' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  reportedDate: string
  estimatedCost: number
  actualCost?: number
  estimatedDuration: number
  actualDuration?: number
  serviceProvider: string
  technician: string
  customerReported: boolean
  damagePhotos: string[]
  notes: string
  partsRequired: string[]
  laborHours: number
}

export function RepairOrders() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Mock data
  const repairOrders: RepairOrder[] = [
    {
      id: '1',
      orderNumber: 'RO-2024-001',
      vehicleId: '1',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      issueType: 'mechanical',
      description: 'Engine making unusual noise, possible timing chain issue',
      status: 'diagnosed',
      priority: 'high',
      reportedDate: '2024-01-15',
      estimatedCost: 1200,
      estimatedDuration: 3,
      serviceProvider: 'Toyota Service',
      technician: 'Ahmed Hassan',
      customerReported: true,
      damagePhotos: [],
      notes: 'Customer reported noise during acceleration',
      partsRequired: ['Timing chain', 'Chain tensioner', 'Engine oil'],
      laborHours: 8
    },
    {
      id: '2',
      orderNumber: 'RO-2024-002',
      vehicleId: '2',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      issueType: 'accident',
      description: 'Front bumper damage from minor collision',
      status: 'in-repair',
      priority: 'medium',
      reportedDate: '2024-01-12',
      estimatedCost: 800,
      actualCost: 850,
      estimatedDuration: 2,
      actualDuration: 2,
      serviceProvider: 'BMW Collision',
      technician: 'Mohammed Ali',
      customerReported: false,
      damagePhotos: ['photo1.jpg', 'photo2.jpg'],
      notes: 'Minor collision during rental period',
      partsRequired: ['Front bumper', 'Headlight assembly'],
      laborHours: 6
    },
    {
      id: '3',
      orderNumber: 'RO-2024-003',
      vehicleId: '3',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      issueType: 'electrical',
      description: 'Air conditioning system not working',
      status: 'completed',
      priority: 'medium',
      reportedDate: '2024-01-10',
      estimatedCost: 450,
      actualCost: 420,
      estimatedDuration: 1,
      actualDuration: 1,
      serviceProvider: 'Mercedes Service',
      technician: 'Sarah Ahmed',
      customerReported: true,
      damagePhotos: [],
      notes: 'AC compressor replacement completed',
      partsRequired: ['AC compressor', 'Refrigerant'],
      laborHours: 4
    }
  ]

  const stats = [
    { label: t('activeRepairs'), value: '12', color: 'blue', icon: IconTool },
    { label: t('pendingApproval'), value: '5', color: 'orange', icon: IconCar},
    { label: t('completedThisMonth'), value: '28', color: 'green', icon: IconCheck },
    { label: t('totalCostThisMonth'), value: '$18,750', color: 'red', icon: IconCurrencyDollar }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'in-repair': return 'blue'
      case 'approved': return 'cyan'
      case 'diagnosed': return 'yellow'
      case 'pending': return 'orange'
      case 'cancelled': return 'red'
      default: return 'gray'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'red'
      case 'high': return 'orange'
      case 'medium': return 'yellow'
      case 'low': return 'green'
      default: return 'gray'
    }
  }

  const getIssueTypeColor = (type: string) => {
    switch (type) {
      case 'accident': return 'red'
      case 'mechanical': return 'blue'
      case 'electrical': return 'yellow'
      case 'bodywork': return 'purple'
      case 'wear': return 'gray'
      default: return 'blue'
    }
  }

  const filteredOrders = repairOrders.filter(order => {
    const matchesSearch = order.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.plateNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || order.status === statusFilter
    const matchesType = !typeFilter || order.issueType === typeFilter
    
    return matchesSearch && matchesStatus && matchesType
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('repairOrders')}
        description={t('manageVehicleRepairsAndServiceOrders')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportReports')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('createRepairOrder')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="orders">{t('repairOrders')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Urgent Repairs */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('urgentRepairs')}</Title>
                  <Button variant="light" size="sm">
                    {t('viewAll')}
                  </Button>
                </Group>
                
                <Stack gap="sm">
                  {repairOrders.filter(order => order.priority === 'high' || order.priority === 'urgent').map((order) => (
                    <Paper key={order.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text fw={500} size="sm">{order.vehicleName}</Text>
                          <Text size="xs" c="dimmed">{order.description}</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getPriorityColor(order.priority)} size="sm">
                            {t(order.priority)}
                          </Badge>
                          <Text size="xs" c="dimmed">{order.reportedDate}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Activity */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentActivity')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconCheck size={16} />} color="green">
                    <Text fw={500} size="sm">{t('repairCompleted')}</Text>
                    <Text size="xs">Mercedes C-Class (DEF-789) - AC Repair</Text>
                  </Alert>
                  
                  <Alert icon={<IconTool size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('repairIn')}</Text>
                    <Text size="xs">BMW X5 (XYZ-456) - Bumper Replacement</Text>
                  </Alert>
                  
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('awaitingApproval')}</Text>
                    <Text size="xs">Toyota Camry (ABC-123) - Engine Repair</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="orders" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchRepairOrders')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'pending', label: t('pending') },
                    { value: 'diagnosed', label: t('diagnosed') },
                    { value: 'approved', label: t('approved') },
                    { value: 'in-repair', label: t('inRepair') },
                    { value: 'completed', label: t('completed') },
                    { value: 'cancelled', label: t('cancelled') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allTypes')}
                  data={[
                    { value: '', label: t('allTypes') },
                    { value: 'mechanical', label: t('mechanical') },
                    { value: 'electrical', label: t('electrical') },
                    { value: 'bodywork', label: t('bodywork') },
                    { value: 'accident', label: t('accident') },
                    { value: 'wear', label: t('wear') }
                  ]}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Repair Orders Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('orderNumber')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('issueType')}</Table.Th>
                  <Table.Th>{t('description')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('priority')}</Table.Th>
                  <Table.Th>{t('estimatedCost')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredOrders.map((order) => (
                  <Table.Tr key={order.id}>
                    <Table.Td>
                      <Text fw={700} size="sm">{order.orderNumber}</Text>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{order.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{order.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getIssueTypeColor(order.issueType)} variant="light">
                        {t(order.issueType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" lineClamp={2}>{order.description}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(order.status)}>
                        {t(order.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getPriorityColor(order.priority)} variant="light">
                        {t(order.priority)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${order.estimatedCost}</Text>
                      {order.actualCost && (
                        <Text size="xs" c="dimmed">Actual: ${order.actualCost}</Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPrinter size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Grid>
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('repairCostAnalysis')}</Title>
                <Text c="dimmed">{t('costAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
            
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('repairTimeAnalysis')}</Title>
                <Text c="dimmed">{t('timeAnalyticsWillBeImplementedHere')}</Text>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>

      {/* Create Repair Order Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('createRepairOrder')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023 (ABC-123)' },
              { value: '2', label: 'BMW X5 2022 (XYZ-456)' },
              { value: '3', label: 'Mercedes C-Class 2023 (DEF-789)' }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('issueType')}
                placeholder={t('selectIssueType')}
                data={[
                  { value: 'mechanical', label: t('mechanical') },
                  { value: 'electrical', label: t('electrical') },
                  { value: 'bodywork', label: t('bodywork') },
                  { value: 'accident', label: t('accident') },
                  { value: 'wear', label: t('wear') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('priority')}
                placeholder={t('selectPriority')}
                data={[
                  { value: 'low', label: t('low') },
                  { value: 'medium', label: t('medium') },
                  { value: 'high', label: t('high') },
                  { value: 'urgent', label: t('urgent') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('problemDescription')}
            placeholder={t('describeProblemInDetail')}
            required
            rows={3}
          />

          <Grid>
            <Grid.Col span={6}>
              <NumberInput
                label={t('estimatedCost')}
                placeholder={t('enterEstimatedCost')}
                prefix="$"
                min={0}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <NumberInput
                label={t('estimatedDuration')}
                placeholder={t('enterDurationInDays')}
                suffix=" days"
                min={0}
              />
            </Grid.Col>
          </Grid>

          <FileInput
            label={t('damagePhotos')}
            placeholder={t('uploadDamagePhotos')}
            leftSection={<IconPhoto size={14} />}
            multiple
            accept="image/*"
          />

          <Textarea
            label={t('additionalNotes')}
            placeholder={t('enterAdditionalNotes')}
            rows={2}
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button onClick={() => setAddModalOpen(false)}>
              {t('createRepairOrder')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
