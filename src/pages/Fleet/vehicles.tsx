import { useState, useEffect, useMemo } from 'react'
import {
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Checkbox,
  Menu,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Tabs,
  Tooltip,
  Pagination,
  MultiSelect,
  RingProgress,
  ThemeIcon,
  FileInput,
  Alert
} from '@mantine/core'
import { DateInput } from '@mantine/dates'
import { notifications } from '@mantine/notifications'
import {
  IconPlus,
  IconSearch,
  IconFilter,
  IconEdit,
  IconEye,
  IconTrash,
  IconCar,
  IconDots,
  IconDownload,
  IconUpload,
  IconTool,
  IconAlertTriangle,
  IconSortAscending,
  IconRefresh,
  IconPrinter,
  IconQrcode,
  IconMapPin,
  IconChartBar,
  IconUser,
  IconClock,
  IconArrowRight
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { vehicleService } from '../../services/vehicleService'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'
import { useAppStore } from '../../store/useAppStore'
import { PageHeader } from '../../components/Common/PageHeader'

interface VehicleTableData {
  id: string
  make: string
  model: string
  year: number
  plate_number: string
  category_name?: string
  location_name?: string
  status: 'available' | 'rented' | 'maintenance' | 'out_of_service' | 'reserved'
  daily_rate: number
  current_mileage: number
  fuel_type: string
  transmission: string
  color: string
  vin: string
  fuel_level: number
  insurance_expiry: string
  registration_expiry: string
  last_service_date?: string
  next_service_due?: string
  features?: string
  notes?: string
  created_at: string
  updated_at: string
}

export function Vehicles() {
  const { t } = useTranslation()

  // Get real data from store
  const {
    vehicles: storeVehicles,
    categories,
    locations,
    reservations,
    contracts,
    customers,
    setVehicles,
    setCategories,
    setLocations,
    addVehicle
  } = useAppStore()

  // State management
  const [error] = useState<string | null>(null)
  const [dataInitialized, setDataInitialized] = useState(false)

  // Filters and search
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [categoryFilter, setCategoryFilter] = useState<string[]>([])
  const [locationFilter, setLocationFilter] = useState('')

  // Sorting and pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)

  // Selection and bulk actions
  const [selectedVehicles, setSelectedVehicles] = useState<string[]>([])

  // Modals and forms
  const [activeTab, setActiveTab] = useState('overview')
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [bulkImportModalOpen, setBulkImportModalOpen] = useState(false)
  const [scheduleMaintenanceModalOpen, setScheduleMaintenanceModalOpen] = useState(false)
  const [selectedVehicle, setSelectedVehicle] = useState<VehicleTableData | null>(null)

  // Add vehicle form state
  const [newVehicle, setNewVehicle] = useState({
    make: '',
    model: '',
    year: new Date().getFullYear(),
    plate_number: '',
    category_id: '',
    location_id: '',
    daily_rate: 0,
    fuel_type: '',
    notes: ''
  })

  // Bulk import state
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importProgress, setImportProgress] = useState(0)
  const [isImporting, setIsImporting] = useState(false)

  // Schedule maintenance state
  const [maintenanceType, setMaintenanceType] = useState('')
  const [maintenanceDate, setMaintenanceDate] = useState<Date | null>(null)
  const [maintenanceNotes, setMaintenanceNotes] = useState('')
  const [selectedVehicleForMaintenance, setSelectedVehicleForMaintenance] = useState('')

  // Advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  // Force initialization on component mount
  useEffect(() => {
    console.log('Vehicles page: Current vehicles count:', storeVehicles.length)
    console.log('Vehicles page: Categories count:', categories.length)
    console.log('Vehicles page: Locations count:', locations.length)

    // Always initialize if any data is missing
    if (storeVehicles.length === 0 || categories.length === 0 || locations.length === 0) {
      console.log('Vehicles page: Initializing sample data...')
      const sampleVehicles = [
        {
          id: '1',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          plate_number: 'A-12345',
          category_id: '1',
          location_id: '1',
          status: 'available' as const,
          daily_rate: 150,
          current_mileage: 15000,
          fuel_type: 'gasoline' as const,
          transmission: 'automatic' as const,
          color: 'White',
          vin: 'JT2BF28K0X0123456',
          fuel_level: 85,
          insurance_expiry: new Date('2024-12-31'),
          registration_expiry: new Date('2024-06-30'),
          last_service_date: new Date('2024-01-15'),
          next_service_due: new Date('2024-07-15'),
          features: ['GPS', 'Bluetooth', 'Air Conditioning'],
          notes: 'Excellent condition',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          make: 'Honda',
          model: 'Civic',
          year: 2022,
          plate_number: 'B-67890',
          category_id: '1',
          location_id: '2',
          status: 'rented' as const,
          daily_rate: 120,
          current_mileage: 22000,
          fuel_type: 'gasoline' as const,
          transmission: 'automatic' as const,
          color: 'Blue',
          vin: 'JHMFC1F39DX123456',
          fuel_level: 60,
          insurance_expiry: new Date('2024-11-30'),
          registration_expiry: new Date('2024-05-31'),
          last_service_date: new Date('2024-02-10'),
          next_service_due: new Date('2024-08-10'),
          features: ['GPS', 'Backup Camera'],
          notes: 'Recently serviced',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '3',
          make: 'BMW',
          model: 'X5',
          year: 2023,
          plate_number: 'C-11111',
          category_id: '3',
          location_id: '1',
          status: 'available' as const,
          daily_rate: 300,
          current_mileage: 8000,
          fuel_type: 'gasoline' as const,
          transmission: 'automatic' as const,
          color: 'Black',
          vin: 'WBAFR7C50DC123456',
          fuel_level: 95,
          insurance_expiry: new Date('2025-01-31'),
          registration_expiry: new Date('2024-08-31'),
          last_service_date: new Date('2024-03-01'),
          next_service_due: new Date('2024-09-01'),
          features: ['GPS', 'Leather Seats', 'Sunroof', 'Premium Audio'],
          notes: 'Luxury vehicle',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '4',
          make: 'Nissan',
          model: 'Altima',
          year: 2021,
          plate_number: 'D-22222',
          category_id: '2',
          location_id: '3',
          status: 'maintenance' as const,
          daily_rate: 110,
          current_mileage: 35000,
          fuel_type: 'gasoline' as const,
          transmission: 'automatic' as const,
          color: 'Silver',
          vin: '1N4BL4BV1MN123456',
          fuel_level: 40,
          insurance_expiry: new Date('2024-10-31'),
          registration_expiry: new Date('2024-04-30'),
          last_service_date: new Date('2024-01-20'),
          next_service_due: new Date('2024-07-20'),
          features: ['GPS', 'Bluetooth'],
          notes: 'In for routine maintenance',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '5',
          make: 'Mercedes',
          model: 'C-Class',
          year: 2023,
          plate_number: 'E-33333',
          category_id: '4',
          location_id: '2',
          status: 'available' as const,
          daily_rate: 250,
          current_mileage: 12000,
          fuel_type: 'gasoline' as const,
          transmission: 'automatic' as const,
          color: 'White',
          vin: 'WDDGF4HB0NR123456',
          fuel_level: 80,
          insurance_expiry: new Date('2025-02-28'),
          registration_expiry: new Date('2024-09-30'),
          last_service_date: new Date('2024-02-15'),
          next_service_due: new Date('2024-08-15'),
          features: ['GPS', 'Leather Seats', 'Premium Audio', 'Heated Seats'],
          notes: 'Premium luxury sedan',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]

      const sampleCategories = [
        {
          id: '1',
          name: 'Economy',
          description: 'Budget-friendly vehicles',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          name: 'Sedan',
          description: 'Comfortable sedans',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '3',
          name: 'SUV',
          description: 'Sport utility vehicles',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '4',
          name: 'Luxury',
          description: 'Premium luxury vehicles',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]

      const sampleLocations = [
        {
          id: '1',
          name: 'Dubai Airport',
          address: 'Dubai International Airport',
          is_main_branch: true,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '2',
          name: 'Abu Dhabi Marina',
          address: 'Abu Dhabi Marina District',
          is_main_branch: false,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: '3',
          name: 'Sharjah City',
          address: 'Sharjah City Center',
          is_main_branch: false,
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]

      setVehicles(sampleVehicles)
      setCategories(sampleCategories)
      setLocations(sampleLocations)
      setDataInitialized(true)
      console.log('Vehicles page: Sample data initialized!')
    } else if (storeVehicles.length > 0 && categories.length > 0 && locations.length > 0) {
      setDataInitialized(true)
      console.log('Vehicles page: Data already exists, marking as initialized')
    }
  }, [storeVehicles.length, categories.length, locations.length, setVehicles, setCategories, setLocations])

  // Transform store vehicles to table format
  const displayVehicles: VehicleTableData[] = useMemo(() => {
    if (!storeVehicles || storeVehicles.length === 0) {
      return []
    }

    return storeVehicles.map((vehicle) => ({
      id: vehicle.id,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      plate_number: vehicle.plate_number,
      category_name: categories?.find(c => c.id === vehicle.category_id)?.name || 'N/A',
      location_name: locations?.find(l => l.id === vehicle.location_id)?.name || 'N/A',
      status: vehicle.status,
      daily_rate: vehicle.daily_rate || 0,
      current_mileage: vehicle.current_mileage || 0,
      fuel_type: vehicle.fuel_type || 'gasoline',
      transmission: vehicle.transmission || 'automatic',
      color: vehicle.color || 'Unknown',
      vin: vehicle.vin || 'N/A',
      fuel_level: vehicle.fuel_level || 100,
      insurance_expiry: vehicle.insurance_expiry ? new Date(vehicle.insurance_expiry).toLocaleDateString() : 'N/A',
      registration_expiry: vehicle.registration_expiry ? new Date(vehicle.registration_expiry).toLocaleDateString() : 'N/A',
      last_service_date: vehicle.last_service_date ? new Date(vehicle.last_service_date).toLocaleDateString() : undefined,
      next_service_due: vehicle.next_service_due ? new Date(vehicle.next_service_due).toLocaleDateString() : undefined,
      features: Array.isArray(vehicle.features) ? JSON.stringify(vehicle.features) : (vehicle.features ? String(vehicle.features) : ''),
      notes: vehicle.notes || '',
      created_at: new Date(vehicle.created_at).toISOString(),
      updated_at: new Date(vehicle.updated_at).toISOString()
    }))
  }, [storeVehicles, categories, locations])

  // Calculate real stats from store data
  const displayStats = useMemo(() => {
    const total = storeVehicles.length
    const available = storeVehicles.filter(v => v.status === 'available').length
    const rented = storeVehicles.filter(v => v.status === 'rented').length
    const maintenance = storeVehicles.filter(v => v.status === 'maintenance').length

    return [
      { label: t('totalVehicles'), value: total.toString(), color: 'blue' },
      { label: t('available'), value: available.toString(), color: 'green' },
      { label: t('rented'), value: rented.toString(), color: 'orange' },
      { label: t('inMaintenance'), value: maintenance.toString(), color: 'red' }
    ]
  }, [storeVehicles, t])

  // Generate recent activity from real data
  const recentActivity = useMemo(() => {
    const activities: Array<{
      id: string
      type: 'vehicle_added' | 'status_change' | 'rental' | 'maintenance' | 'return'
      vehicle: string
      plateNumber: string
      action: string
      time: string
      timeDetail: string
      status?: string
      customer?: string
    }> = []

    // Add recent vehicle additions (based on created_at)
    const recentVehicles = storeVehicles
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 2)
      .map(v => ({
        id: `vehicle-${v.id}`,
        type: 'vehicle_added' as const,
        vehicle: `${v.make} ${v.model} ${v.year}`,
        plateNumber: v.plate_number,
        action: 'Vehicle added to fleet',
        time: new Date(v.created_at).toLocaleDateString(),
        timeDetail: new Date(v.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        status: v.status
      }))

    // Add recent status changes (simulated based on maintenance status)
    const maintenanceVehicles = storeVehicles
      .filter(v => v.status === 'maintenance')
      .slice(0, 1)
      .map(v => ({
        id: `maintenance-${v.id}`,
        type: 'maintenance' as const,
        vehicle: `${v.make} ${v.model} ${v.year}`,
        plateNumber: v.plate_number,
        action: 'Scheduled for maintenance',
        time: v.last_service_date ? new Date(v.last_service_date).toLocaleDateString() : new Date().toLocaleDateString(),
        timeDetail: v.last_service_date ? new Date(v.last_service_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        status: 'maintenance'
      }))

    // Add recent rentals from contracts
    const recentRentals = contracts
      .filter(c => c.status === 'active')
      .slice(0, 2)
      .map(c => {
        const vehicle = storeVehicles.find(v => v.id === c.vehicle_id)
        const customer = customers.find(cust => cust.id === c.customer_id)
        return {
          id: `rental-${c.id}`,
          type: 'rental' as const,
          vehicle: vehicle ? `${vehicle.make} ${vehicle.model} ${vehicle.year}` : 'Unknown Vehicle',
          plateNumber: vehicle?.plate_number || 'N/A',
          action: 'Vehicle rented out',
          time: new Date(c.created_at).toLocaleDateString(),
          timeDetail: new Date(c.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          status: 'rented',
          customer: customer ? `${customer.first_name} ${customer.last_name}` : `Customer ${c.customer_id.slice(0, 8)}...`
        }
      })

    activities.push(...recentVehicles, ...maintenanceVehicles, ...recentRentals)
    return activities.slice(0, 5) // Show 5 most recent activities
  }, [storeVehicles, contracts, customers])

  // Update pagination based on real data
  useEffect(() => {
    setTotalItems(displayVehicles.length)
    setTotalPages(Math.ceil(displayVehicles.length / itemsPerPage))
  }, [displayVehicles.length, itemsPerPage])

  // Real-time data - no more mock data needed!

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'green'
      case 'rented': return 'orange'
      case 'maintenance': return 'red'
      case 'out-of-service': return 'gray'
      default: return 'blue'
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVehicles(displayVehicles.map(v => v.id))
    } else {
      setSelectedVehicles([])
    }
  }

  const handleSelectVehicle = (vehicleId: string, checked: boolean) => {
    if (checked) {
      setSelectedVehicles(prev => [...prev, vehicleId])
    } else {
      setSelectedVehicles(prev => prev.filter(id => id !== vehicleId))
    }
  }

  // Handle bulk import
  const handleBulkImport = async () => {
    if (!importFile) {
      notifications.show({
        title: 'Error',
        message: 'Please select a file to import',
        color: 'red'
      })
      return
    }

    setIsImporting(true)
    setImportProgress(0)

    // Simulate import progress
    const interval = setInterval(() => {
      setImportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsImporting(false)
          setBulkImportModalOpen(false)
          setImportFile(null)
          setImportProgress(0)

          // Show success notification
          notifications.show({
            title: 'Success',
            message: `Successfully imported vehicles from ${importFile.name}`,
            color: 'green'
          })

          // Force a refresh of the data to show new activity
          window.location.reload()
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  // Handle schedule maintenance
  const handleScheduleMaintenance = () => {
    if (!selectedVehicleForMaintenance || !maintenanceType || !maintenanceDate) {
      notifications.show({
        title: 'Error',
        message: 'Please fill in all required fields',
        color: 'red'
      })
      return
    }

    const selectedVehicle = displayVehicles.find(v => v.id === selectedVehicleForMaintenance)
    const maintenanceTypeLabel = maintenanceType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())

    // Here you would typically save to your backend
    notifications.show({
      title: 'Success',
      message: `${maintenanceTypeLabel} scheduled for ${selectedVehicle?.make} ${selectedVehicle?.model} on ${maintenanceDate.toLocaleDateString()}`,
      color: 'green'
    })

    // Reset form and close modal
    setScheduleMaintenanceModalOpen(false)
    setMaintenanceType('')
    setMaintenanceDate(null)
    setMaintenanceNotes('')
    setSelectedVehicleForMaintenance('')
  }

  // Handle adding new vehicle
  const handleAddVehicle = () => {
    // Validate required fields
    if (!newVehicle.make || !newVehicle.model || !newVehicle.plate_number || !newVehicle.category_id) {
      notifications.show({
        title: 'Error',
        message: 'Please fill in all required fields',
        color: 'red'
      })
      return
    }

    // Create new vehicle object
    const vehicle: Vehicle = {
      id: `vehicle-${Date.now()}`,
      make: newVehicle.make,
      model: newVehicle.model,
      year: newVehicle.year,
      plate_number: newVehicle.plate_number,
      vin: `VIN${Date.now()}`, // Generate a temporary VIN
      category_id: newVehicle.category_id,
      color: 'White', // Default color
      transmission: 'automatic',
      fuel_type: newVehicle.fuel_type as 'gasoline' | 'diesel' | 'electric' | 'hybrid',
      engine_size: 2.0,
      seating_capacity: 5,
      current_mileage: 0,
      fuel_level: 100,
      status: 'available',
      daily_rate: newVehicle.daily_rate,
      weekly_rate: newVehicle.daily_rate * 6,
      monthly_rate: newVehicle.daily_rate * 25,
      security_deposit: newVehicle.daily_rate * 3,
      location_id: newVehicle.location_id || '1',
      features: ['Air Conditioning', 'Bluetooth'],
      notes: newVehicle.notes,
      last_service_date: new Date(),
      next_service_due: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
      insurance_expiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      registration_expiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    }

    // Add to store
    addVehicle(vehicle)

    // Show success notification
    notifications.show({
      title: 'Success',
      message: `${vehicle.make} ${vehicle.model} has been added successfully!`,
      color: 'green'
    })

    // Reset form and close modal
    setNewVehicle({
      make: '',
      model: '',
      year: new Date().getFullYear(),
      plate_number: '',
      category_id: '',
      location_id: '',
      daily_rate: 0,
      fuel_type: '',
      notes: ''
    })
    setAddModalOpen(false)
  }

  const filteredVehicles = displayVehicles.filter(vehicle => {
    const matchesSearch = vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         vehicle.plate_number.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter.length === 0 || statusFilter.includes(vehicle.status)
    const matchesCategory = categoryFilter.length === 0 || (vehicle.category_name && categoryFilter.includes(vehicle.category_name))
    const matchesLocation = !locationFilter || (vehicle.location_name && vehicle.location_name.toLowerCase().includes(locationFilter.toLowerCase()))

    return matchesSearch && matchesStatus && matchesCategory && matchesLocation
  })

  // Show loading state until data is initialized
  if (!dataInitialized) {
    return (
      <ErrorBoundary>
        <LoadingState message="Loading vehicles..." />
      </ErrorBoundary>
    )
  }

  return (
    <ErrorBoundary>
      <Stack gap="lg">
        <PageHeader
          title="Vehicle Management"
          description="Manage your vehicle inventory"
          actions={
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                Export
              </Button>
              <Button variant="light" leftSection={<IconUpload size={16} />}>
                Import
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setAddModalOpen(true)}
              >
                Add Vehicle
              </Button>
            </Group>
          }
        />

        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>

          <Tabs.List mb="lg">
            <Tabs.Tab value="overview" leftSection={<IconChartBar size={16} />}>
              Overview
            </Tabs.Tab>
            <Tabs.Tab value="vehicles" leftSection={<IconCar size={16} />}>
              All Vehicles ({filteredVehicles.length})
            </Tabs.Tab>
            <Tabs.Tab value="maintenance" leftSection={<IconTool size={16} />}>
              Maintenance
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview">
            {/* Stats */}
            <Grid mb="lg">
              {displayStats.map((stat: any) => (
                <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                  <Paper p="md" withBorder>
                    <Group justify="space-between">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <ThemeIcon size="xl" variant="light" color={stat.color}>
                        <IconCar size={24} />
                      </ThemeIcon>
                    </Group>
                  </Paper>
                </Grid.Col>
              ))}
            </Grid>

            {/* Quick Actions */}
            <Grid mb="lg">
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">Quick Actions</Title>
                  <Stack gap="sm">
                    <Button
                      variant="light"
                      fullWidth
                      leftSection={<IconPlus size={16} />}
                      onClick={() => setAddModalOpen(true)}
                    >
                      Add New Vehicle
                    </Button>
                    <Button
                      variant="light"
                      fullWidth
                      leftSection={<IconUpload size={16} />}
                      onClick={() => setBulkImportModalOpen(true)}
                    >
                      Bulk Import
                    </Button>
                    <Button
                      variant="light"
                      fullWidth
                      leftSection={<IconTool size={16} />}
                      onClick={() => setScheduleMaintenanceModalOpen(true)}
                    >
                      Schedule Maintenance
                    </Button>
                  </Stack>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">Recent Activity</Title>
                  {recentActivity.length > 0 ? (
                    <Stack gap="sm">
                      {recentActivity.map((activity) => (
                        <Paper key={activity.id} p="sm" withBorder radius="sm">
                          <Group justify="space-between" align="flex-start">
                            <Group gap="sm" align="flex-start">
                              <ThemeIcon
                                size="sm"
                                variant="light"
                                color={
                                  activity.type === 'vehicle_added' ? 'blue' :
                                  activity.type === 'rental' ? 'orange' :
                                  activity.type === 'maintenance' ? 'red' :
                                  'gray'
                                }
                              >
                                {activity.type === 'vehicle_added' && <IconPlus size={14} />}
                                {activity.type === 'rental' && <IconUser size={14} />}
                                {activity.type === 'maintenance' && <IconTool size={14} />}
                                {activity.type === 'status_change' && <IconArrowRight size={14} />}
                                {activity.type === 'return' && <IconCar size={14} />}
                              </ThemeIcon>
                              <div style={{ flex: 1 }}>
                                <Text size="sm" fw={500}>
                                  {activity.vehicle}
                                </Text>
                                <Text size="xs" c="dimmed">
                                  {activity.plateNumber} • {activity.action}
                                </Text>
                                {activity.customer && (
                                  <Text size="xs" c="blue">
                                    Customer: {activity.customer}
                                  </Text>
                                )}
                              </div>
                            </Group>
                            <div style={{ textAlign: 'right' }}>
                              <Text size="xs" c="dimmed">
                                {activity.time}
                              </Text>
                              <Text size="xs" c="dimmed">
                                {activity.timeDetail}
                              </Text>
                              {activity.status && (
                                <Badge size="xs" variant="light" color={
                                  activity.status === 'available' ? 'green' :
                                  activity.status === 'rented' ? 'orange' :
                                  activity.status === 'maintenance' ? 'red' :
                                  'gray'
                                }>
                                  {activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                                </Badge>
                              )}
                            </div>
                          </Group>
                        </Paper>
                      ))}
                      <Group justify="center" mt="xs">
                        <Button variant="light" size="xs" leftSection={<IconClock size={14} />}>
                          View All Activity
                        </Button>
                      </Group>
                    </Stack>
                  ) : (
                    <Text c="dimmed" size="sm" ta="center" py="md">
                      No recent activity to display
                    </Text>
                  )}
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="vehicles">
            {/* Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <TextInput
                    placeholder="Search Vehicles"
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <MultiSelect
                    placeholder="All Status"
                    data={[
                      { value: 'available', label: 'Available' },
                      { value: 'rented', label: 'Rented' },
                      { value: 'maintenance', label: 'Maintenance' },
                      { value: 'out_of_service', label: 'Out of Service' },
                      { value: 'reserved', label: 'Reserved' }
                    ]}
                    value={statusFilter}
                    onChange={setStatusFilter}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <MultiSelect
                    placeholder="All Categories"
                    data={categories?.map(cat => ({ value: cat.name, label: cat.name })) || []}
                    value={categoryFilter}
                    onChange={setCategoryFilter}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <TextInput
                    placeholder="Location"
                    leftSection={<IconMapPin size={16} />}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Group>
                    <Button
                      variant="light"
                      leftSection={<IconFilter size={16} />}
                      onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    >
                      Filters
                    </Button>
                    <Button variant="light" leftSection={<IconRefresh size={16} />} onClick={() => window.location.reload()}>
                      Refresh
                    </Button>
                  </Group>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Bulk Actions */}
            {selectedVehicles.length > 0 && (
              <Card withBorder mb="md" bg="blue.0">
                <Group justify="space-between">
                  <Text fw={500}>
                    {selectedVehicles.length} vehicles selected
                  </Text>
                  <Group>
                    <Button variant="light" size="sm">
                      Bulk Edit
                    </Button>
                    <Button variant="light" size="sm" color="red">
                      Bulk Delete
                    </Button>
                    <Button variant="light" size="sm" leftSection={<IconDownload size={14} />}>
                      Export
                    </Button>
                  </Group>
                </Group>
              </Card>
            )}

            {/* Vehicle Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>
                      <Checkbox
                        checked={selectedVehicles.length === filteredVehicles.length && filteredVehicles.length > 0}
                        indeterminate={selectedVehicles.length > 0 && selectedVehicles.length < filteredVehicles.length}
                        onChange={(e) => handleSelectAll(e.currentTarget.checked)}
                      />
                    </Table.Th>
                    <Table.Th>
                      <Group gap="xs">
                        <Text>Vehicle</Text>
                        <ActionIcon size="xs" variant="transparent">
                          <IconSortAscending size={12} />
                        </ActionIcon>
                      </Group>
                    </Table.Th>
                    <Table.Th>Plate Number</Table.Th>
                    <Table.Th>Category</Table.Th>
                    <Table.Th>Location</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Daily Rate</Table.Th>
                    <Table.Th>Fuel Level</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {filteredVehicles.map((vehicle) => (
                    <Table.Tr key={vehicle.id}>
                      <Table.Td>
                        <Checkbox
                          checked={selectedVehicles.includes(vehicle.id)}
                          onChange={(e) => handleSelectVehicle(vehicle.id, e.currentTarget.checked)}
                        />
                      </Table.Td>
                      <Table.Td>
                        <Group>
                          <div>
                            <Text fw={500}>{vehicle.make} {vehicle.model} {vehicle.year}</Text>
                            <Text size="sm" c="dimmed">{vehicle.transmission} • {vehicle.fuel_type}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={700}>{vehicle.plate_number}</Text>
                      </Table.Td>
                      <Table.Td>{vehicle.category_name || 'N/A'}</Table.Td>
                      <Table.Td>{vehicle.location_name || 'N/A'}</Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(vehicle.status)} variant="light">
                          {vehicle.status.charAt(0).toUpperCase() + vehicle.status.slice(1).replace('_', ' ')}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={700}>${vehicle.daily_rate}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <RingProgress
                            size={30}
                            thickness={3}
                            sections={[{ value: vehicle.fuel_level, color: vehicle.fuel_level > 50 ? 'green' : vehicle.fuel_level > 25 ? 'orange' : 'red' }]}
                          />
                          <Text size="xs">{vehicle.fuel_level}%</Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label="View Details">
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => {
                                setSelectedVehicle(vehicle)
                                setViewModalOpen(true)
                              }}
                            >
                              <IconEye size={14} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Edit">
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => {
                                setSelectedVehicle(vehicle)
                                setEditModalOpen(true)
                              }}
                            >
                              <IconEdit size={14} />
                            </ActionIcon>
                          </Tooltip>
                          <Menu>
                            <Menu.Target>
                              <ActionIcon variant="light" size="sm">
                                <IconDots size={14} />
                              </ActionIcon>
                            </Menu.Target>
                            <Menu.Dropdown>
                              <Menu.Item leftSection={<IconQrcode size={14} />}>
                                Generate QR
                              </Menu.Item>
                              <Menu.Item leftSection={<IconPrinter size={14} />}>
                                Print Label
                              </Menu.Item>
                              <Menu.Item leftSection={<IconTool size={14} />}>
                                Schedule Maintenance
                              </Menu.Item>
                              <Menu.Divider />
                              <Menu.Item leftSection={<IconTrash size={14} />} color="red">
                                Delete
                              </Menu.Item>
                            </Menu.Dropdown>
                          </Menu>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)} - {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} vehicles
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="maintenance">
            <Card withBorder>
              <Title order={4} mb="md">Maintenance Schedule</Title>
              <Text c="dimmed">Maintenance schedule will be implemented here</Text>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* Add Vehicle Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title="Add New Vehicle"
          size="lg"
          withCloseButton
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="Make"
                  placeholder="Enter Make"
                  value={newVehicle.make}
                  onChange={(e) => setNewVehicle(prev => ({ ...prev, make: e.target.value }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Model"
                  placeholder="Enter Model"
                  value={newVehicle.model}
                  onChange={(e) => setNewVehicle(prev => ({ ...prev, model: e.target.value }))}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Year"
                  placeholder="Enter Year"
                  value={newVehicle.year}
                  onChange={(value) => setNewVehicle(prev => ({ ...prev, year: value || new Date().getFullYear() }))}
                  min={1990}
                  max={new Date().getFullYear() + 1}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Plate Number"
                  placeholder="Enter Plate Number"
                  value={newVehicle.plate_number}
                  onChange={(e) => setNewVehicle(prev => ({ ...prev, plate_number: e.target.value }))}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Category"
                  placeholder="Select Category"
                  data={categories?.map(cat => ({ value: cat.id, label: cat.name })) || []}
                  value={newVehicle.category_id}
                  onChange={(value) => setNewVehicle(prev => ({ ...prev, category_id: value || '' }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Location"
                  placeholder="Select Location"
                  data={locations?.map(loc => ({ value: loc.id, label: loc.name })) || []}
                  value={newVehicle.location_id}
                  onChange={(value) => setNewVehicle(prev => ({ ...prev, location_id: value || '' }))}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label="Daily Rate"
                  placeholder="Enter Daily Rate"
                  value={newVehicle.daily_rate}
                  onChange={(value) => setNewVehicle(prev => ({ ...prev, daily_rate: value || 0 }))}
                  min={0}
                  prefix="AED "
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label="Fuel Type"
                  placeholder="Select Fuel Type"
                  data={[
                    { value: 'gasoline', label: 'Gasoline' },
                    { value: 'diesel', label: 'Diesel' },
                    { value: 'electric', label: 'Electric' },
                    { value: 'hybrid', label: 'Hybrid' }
                  ]}
                  value={newVehicle.fuel_type}
                  onChange={(value) => setNewVehicle(prev => ({ ...prev, fuel_type: value || '' }))}
                  required
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label="Notes"
              placeholder="Enter Notes"
              value={newVehicle.notes}
              onChange={(e) => setNewVehicle(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
            />

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddVehicle}>
                Add Vehicle
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* View Vehicle Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedVehicle ? `${selectedVehicle.make} ${selectedVehicle.model}` : 'Vehicle Details'}
          size="lg"
          withCloseButton
        >
          {selectedVehicle && (
            <Stack>
              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">Plate Number</Text>
                  <Text fw={500}>{selectedVehicle.plate_number}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">VIN</Text>
                  <Text fw={500}>{selectedVehicle.vin}</Text>
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">Category</Text>
                  <Text fw={500}>{selectedVehicle.category_name || 'N/A'}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">Status</Text>
                  <Badge color={getStatusColor(selectedVehicle.status)}>
                    {selectedVehicle.status.charAt(0).toUpperCase() + selectedVehicle.status.slice(1).replace('_', ' ')}
                  </Badge>
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">Daily Rate</Text>
                  <Text fw={500}>AED {selectedVehicle.daily_rate}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">Mileage</Text>
                  <Text fw={500}>{selectedVehicle.current_mileage.toLocaleString()} km</Text>
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">Fuel Level</Text>
                  <Group gap="xs">
                    <RingProgress
                      size={40}
                      thickness={4}
                      sections={[{ value: selectedVehicle.fuel_level, color: selectedVehicle.fuel_level > 50 ? 'green' : selectedVehicle.fuel_level > 25 ? 'orange' : 'red' }]}
                    />
                    <Text fw={500}>{selectedVehicle.fuel_level}%</Text>
                  </Group>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">Location</Text>
                  <Text fw={500}>{selectedVehicle.location_name || 'N/A'}</Text>
                </Grid.Col>
              </Grid>

              {selectedVehicle.notes && (
                <div>
                  <Text size="sm" c="dimmed">Notes</Text>
                  <Text>{selectedVehicle.notes}</Text>
                </div>
              )}

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setViewModalOpen(false)}>
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setViewModalOpen(false)
                    setEditModalOpen(true)
                  }}
                  leftSection={<IconEdit size={16} />}
                >
                  Edit
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Edit Vehicle Modal */}
        <Modal
          opened={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          title={selectedVehicle ? `Edit ${selectedVehicle.make} ${selectedVehicle.model}` : 'Edit Vehicle'}
          size="lg"
          withCloseButton
        >
          {selectedVehicle && (
            <Stack>
              <Text c="dimmed">Edit vehicle form will be implemented here</Text>
              <Group justify="flex-end">
                <Button variant="light" onClick={() => setEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setEditModalOpen(false)}>
                  Save Changes
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Bulk Import Modal */}
        <Modal
          opened={bulkImportModalOpen}
          onClose={() => setBulkImportModalOpen(false)}
          title="Bulk Import Vehicles"
          size="md"
          withCloseButton
        >
          <Stack>
            <Text size="sm" c="dimmed">
              Upload a CSV file with vehicle data to import multiple vehicles at once.
            </Text>

            <FileInput
              label="Select CSV File"
              placeholder="Choose file..."
              accept=".csv,.xlsx,.xls"
              value={importFile}
              onChange={setImportFile}
              leftSection={<IconUpload size={16} />}
            />

            {importFile && (
              <Alert color="blue" title="File Selected">
                <Text size="sm">
                  File: {importFile.name} ({(importFile.size / 1024).toFixed(1)} KB)
                </Text>
              </Alert>
            )}

            {isImporting && (
              <div>
                <Text size="sm" mb="xs">Importing vehicles... {importProgress}%</Text>
                <RingProgress
                  size={60}
                  thickness={6}
                  sections={[{ value: importProgress, color: 'blue' }]}
                />
              </div>
            )}

            <Text size="xs" c="dimmed">
              Supported formats: CSV, Excel (.xlsx, .xls)
              <br />
              Required columns: Make, Model, Year, Plate Number, Category, Location, Daily Rate
            </Text>

            <Divider />

            <Group justify="flex-end">
              <Button
                variant="light"
                onClick={() => {
                  setBulkImportModalOpen(false)
                  setImportFile(null)
                  setImportProgress(0)
                }}
                disabled={isImporting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleBulkImport}
                disabled={!importFile || isImporting}
                loading={isImporting}
              >
                Import Vehicles
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Schedule Maintenance Modal */}
        <Modal
          opened={scheduleMaintenanceModalOpen}
          onClose={() => setScheduleMaintenanceModalOpen(false)}
          title="Schedule Maintenance"
          size="md"
          withCloseButton
        >
          <Stack>
            <Select
              label="Select Vehicle"
              placeholder="Choose vehicle..."
              data={displayVehicles.map(v => ({
                value: v.id,
                label: `${v.make} ${v.model} (${v.plate_number})`
              }))}
              value={selectedVehicleForMaintenance}
              onChange={(value) => setSelectedVehicleForMaintenance(value || '')}
              required
            />

            <Select
              label="Maintenance Type"
              placeholder="Select maintenance type..."
              data={[
                { value: 'oil_change', label: 'Oil Change' },
                { value: 'tire_rotation', label: 'Tire Rotation' },
                { value: 'brake_inspection', label: 'Brake Inspection' },
                { value: 'engine_service', label: 'Engine Service' },
                { value: 'transmission_service', label: 'Transmission Service' },
                { value: 'annual_inspection', label: 'Annual Inspection' },
                { value: 'other', label: 'Other' }
              ]}
              value={maintenanceType}
              onChange={(value) => setMaintenanceType(value || '')}
              required
            />

            <DateInput
              label="Scheduled Date"
              placeholder="Select date..."
              value={maintenanceDate}
              onChange={setMaintenanceDate}
              minDate={new Date()}
              required
            />

            <Textarea
              label="Notes"
              placeholder="Additional notes or instructions..."
              value={maintenanceNotes}
              onChange={(e) => setMaintenanceNotes(e.target.value)}
              rows={3}
            />

            <Divider />

            <Group justify="flex-end">
              <Button
                variant="light"
                onClick={() => {
                  setScheduleMaintenanceModalOpen(false)
                  setMaintenanceType('')
                  setMaintenanceDate(null)
                  setMaintenanceNotes('')
                  setSelectedVehicleForMaintenance('')
                }}
              >
                Cancel
              </Button>
              <Button onClick={handleScheduleMaintenance}>
                Schedule Maintenance
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </ErrorBoundary>
  )
}


