import { useState } from 'react'
import {
  Title,
  Text,
  Button,
  Group,
  Card,
  Stack,
  Select,
  Grid,
  Paper,
  Badge,
  Progress,
  Table,
  ActionIcon,
  Modal,
  Divider,
  TextInput,
  Alert,
  Checkbox,
  NumberInput,
  Textarea
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconEdit,
  IconTrash,
  IconDownload,
  IconRefresh,
  IconCar,
  IconCurrencyDollar,
  IconMapPin,
  IconSettings,
  IconSearch,
  IconPlus,
  IconEye,
  IconCheck,
  IconStack
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface BatchOperation {
  id: string
  name: string
  type: 'price' | 'status' | 'location' | 'category' | 'maintenance'
  description: string
  vehicleCount: number
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  createdAt: string
}

interface Vehicle {
  id: string
  make: string
  model: string
  year: number
  plateNumber: string
  category: string
  status: 'available' | 'rented' | 'maintenance' | 'out-of-service'
  location: string
  dailyRate: number
  mileage: number
}

export function BatchModifications() {
  const { t } = useTranslation()
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [batchModalOpen, setBatchModalOpen] = useState(false)
  const [vehicleSearchQuery, setVehicleSearchQuery] = useState('')
  const [vehicleStatusFilter, setVehicleStatusFilter] = useState<string>('')
  const [vehicleCategoryFilter, setVehicleCategoryFilter] = useState<string>('')
  const [selectedOperation, setSelectedOperation] = useState<string>('')
  const [selectedVehicles, setSelectedVehicles] = useState<string[]>([])
  const [batchOperationType, setBatchOperationType] = useState<string>('')
  const [batchOperationData, setBatchOperationData] = useState<any>({})

  // Mock vehicle data
  const vehicles: Vehicle[] = [
    {
      id: '1',
      make: 'Toyota',
      model: 'Camry',
      year: 2023,
      plateNumber: 'ABC-123',
      category: 'Sedan',
      status: 'available',
      location: 'Dubai',
      dailyRate: 120,
      mileage: 15000
    },
    {
      id: '2',
      make: 'BMW',
      model: 'X5',
      year: 2022,
      plateNumber: 'XYZ-456',
      category: 'SUV',
      status: 'rented',
      location: 'Abu Dhabi',
      dailyRate: 200,
      mileage: 25000
    },
    {
      id: '3',
      make: 'Mercedes',
      model: 'C-Class',
      year: 2023,
      plateNumber: 'DEF-789',
      category: 'Luxury',
      status: 'maintenance',
      location: 'Dubai',
      dailyRate: 180,
      mileage: 8000
    },
    {
      id: '4',
      make: 'Honda',
      model: 'Civic',
      year: 2023,
      plateNumber: 'GHI-012',
      category: 'Economy',
      status: 'available',
      location: 'Sharjah',
      dailyRate: 80,
      mileage: 12000
    },
    {
      id: '5',
      make: 'Nissan',
      model: 'Altima',
      year: 2022,
      plateNumber: 'JKL-345',
      category: 'Sedan',
      status: 'available',
      location: 'Dubai',
      dailyRate: 100,
      mileage: 18000
    }
  ]

  // Mock data for recent operations
  const recentOperations: BatchOperation[] = [
    {
      id: '1',
      name: 'Price Update - Summer Season',
      type: 'price',
      description: 'Increase daily rates by 15% for summer season',
      vehicleCount: 45,
      status: 'completed',
      progress: 100,
      createdAt: '2024-01-15'
    },
    {
      id: '2',
      name: 'Location Transfer - Dubai to Abu Dhabi',
      type: 'location',
      description: 'Transfer 20 vehicles from Dubai to Abu Dhabi branch',
      vehicleCount: 20,
      status: 'running',
      progress: 65,
      createdAt: '2024-01-14'
    },
    {
      id: '3',
      name: 'Maintenance Schedule',
      type: 'maintenance',
      description: 'Schedule maintenance for vehicles over 50,000 km',
      vehicleCount: 12,
      status: 'pending',
      progress: 0,
      createdAt: '2024-01-13'
    }
  ]

  const operationTypes = [
    {
      value: 'price',
      label: t('priceUpdate'),
      icon: <IconCurrencyDollar size={20} />,
      description: t('updateDailyRatesForMultipleVehicles')
    },
    {
      value: 'status',
      label: t('statusChange'),
      icon: <IconSettings size={20} />,
      description: t('changeStatusForMultipleVehicles')
    },
    {
      value: 'location',
      label: t('locationTransfer'),
      icon: <IconMapPin size={20} />,
      description: t('transferVehiclesBetweenLocations')
    },
    {
      value: 'category',
      label: t('categoryUpdate'),
      icon: <IconCar size={20} />,
      description: t('updateCategoryForMultipleVehicles')
    },
    {
      value: 'maintenance',
      label: t('maintenanceScheduling'),
      icon: <IconSettings size={20} />,
      description: t('scheduleMaintenanceForMultipleVehicles')
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'running': return 'blue'
      case 'pending': return 'yellow'
      case 'failed': return 'red'
      default: return 'gray'
    }
  }

  // Simplified for clean design - removed complex functionality

  return (
    <Stack gap="lg">
      {/* Header - consistent with other pages */}
      <Group justify="space-between">
        <div>
          <Title order={2}>{t('batchModifications')}</Title>
          <Text c="dimmed" size="sm">{t('performBulkOperationsOnMultipleVehicles')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportHistory')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setBatchModalOpen(true)}>
            Create New Batch Operation
          </Button>
        </Group>
      </Group>

      {/* Stats - consistent with other pages */}
      <Grid>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md">
            <Group justify="space-between">
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                  {t('totalOperations')}
                </Text>
                <Text fw={700} size="xl">
                  {recentOperations.length}
                </Text>
              </div>
              <IconSettings size={24} color="var(--mantine-color-blue-6)" />
            </Group>
          </Card>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md">
            <Group justify="space-between">
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                  {t('runningOperations')}
                </Text>
                <Text fw={700} size="xl">
                  {recentOperations.filter(op => op.status === 'running').length}
                </Text>
              </div>
              <IconRefresh size={24} color="var(--mantine-color-green-6)" />
            </Group>
          </Card>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md">
            <Group justify="space-between">
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                  {t('completedToday')}
                </Text>
                <Text fw={700} size="xl">
                  {recentOperations.filter(op => op.status === 'completed').length}
                </Text>
              </div>
              <IconCheck size={24} color="var(--mantine-color-teal-6)" />
            </Group>
          </Card>
        </Grid.Col>
        <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
          <Card withBorder p="md">
            <Group justify="space-between">
              <div>
                <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                  {t('vehiclesProcessed')}
                </Text>
                <Text fw={700} size="xl">
                  {recentOperations.reduce((sum, op) => sum + op.vehicleCount, 0)}
                </Text>
              </div>
              <IconCar size={24} color="var(--mantine-color-orange-6)" />
            </Group>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Search and Filters - consistent with other pages */}
      <Card withBorder p="md">
        <Grid>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <TextInput
              placeholder={t('searchOperations')}
              leftSection={<IconSearch size={16} />}
              value={vehicleSearchQuery}
              onChange={(e) => setVehicleSearchQuery(e.target.value)}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 2 }}>
            <Select
              placeholder={t('allStatus')}
              data={[
                { value: '', label: t('allStatus') },
                { value: 'pending', label: t('pending') },
                { value: 'running', label: t('running') },
                { value: 'completed', label: t('completed') },
                { value: 'failed', label: t('failed') }
              ]}
              value={vehicleStatusFilter}
              onChange={(value) => setVehicleStatusFilter(value || '')}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 2 }}>
            <Select
              placeholder={t('operationType')}
              data={operationTypes.map(type => ({ value: type.value, label: type.label }))}
              value={vehicleCategoryFilter}
              onChange={(value) => setVehicleCategoryFilter(value || '')}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Group>
              <Button variant="light" leftSection={<IconRefresh size={16} />}>
                {t('refresh')}
              </Button>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
            </Group>
          </Grid.Col>
        </Grid>
      </Card>

      {/* Quick Actions - Operation Types */}
      <Card withBorder>
        <Title order={4} mb="md">{t('quickActions')}</Title>
        <Grid>
          {operationTypes.map((type) => (
            <Grid.Col key={type.value} span={{ base: 12, sm: 6, md: 4 }}>
              <Paper
                p="md"
                withBorder
                style={{
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    backgroundColor: 'var(--mantine-color-gray-0)',
                    borderColor: 'var(--mantine-color-blue-6)'
                  }
                }}
                onClick={() => setSelectedOperation(type.value)}
              >
                <Group>
                  {type.icon}
                  <div>
                    <Text fw={500} size="sm">{type.label}</Text>
                    <Text size="xs" c="dimmed">{type.description}</Text>
                  </div>
                </Group>
              </Paper>
            </Grid.Col>
          ))}
        </Grid>
      </Card>

      <Grid>
        {/* Operation History - Main Table */}
        <Grid.Col span={{ base: 12, lg: 8 }}>
          <Card withBorder>
            <Group justify="space-between" mb="md">
              <Title order={4}>{t('operationHistory')}</Title>
              <Group>
                <Button variant="light" size="sm" leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Group>
            </Group>

            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('operation')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('vehicles')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('date')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {recentOperations.map((operation) => (
                  <Table.Tr key={operation.id}>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{operation.name}</Text>
                        <Text size="xs" c="dimmed">{operation.description}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="light">{t(operation.type)}</Badge>
                    </Table.Td>
                    <Table.Td>{operation.vehicleCount}</Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(operation.status)}>
                        {t(operation.status)}
                      </Badge>
                      {operation.status === 'running' && (
                        <Progress value={operation.progress} size="xs" mt="xs" />
                      )}
                    </Table.Td>
                    <Table.Td>{operation.createdAt}</Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Grid.Col>

        {/* Recent Operations Sidebar */}
        <Grid.Col span={{ base: 12, lg: 4 }}>
          <Card withBorder>
            <Group justify="space-between" mb="md">
              <Title order={4}>{t('recentOperations')}</Title>
              <ActionIcon variant="light" size="sm">
                <IconRefresh size={14} />
              </ActionIcon>
            </Group>

            <Stack gap="sm">
              {recentOperations.map((operation) => (
                <Paper key={operation.id} p="sm" withBorder>
                  <Group justify="space-between" mb="xs">
                    <Text fw={500} size="sm">{operation.name}</Text>
                    <Badge color={getStatusColor(operation.status)} size="sm">
                      {t(operation.status)}
                    </Badge>
                  </Group>
                  <Text size="xs" c="dimmed" mb="xs">
                    {operation.vehicleCount} {t('vehicles')} • {operation.createdAt}
                  </Text>
                  {operation.status === 'running' && (
                    <Progress value={operation.progress} size="xs" />
                  )}
                </Paper>
              ))}
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Create Batch Operation Modal */}
      <Modal
        opened={batchModalOpen}
        onClose={() => setBatchModalOpen(false)}
        title="Create New Batch Operation"
        size="xl"
        withCloseButton
      >
        <Stack>
          <Alert color="blue" icon={<IconStack size={16} />}>
            <Text fw={500}>Batch Vehicle Operations</Text>
            <Text size="sm">Select vehicles and configure bulk operations to apply changes to multiple vehicles simultaneously</Text>
          </Alert>

          {/* Step 1: Operation Type Selection */}
          <div>
            <Text fw={500} mb="sm">Step 1: Select Operation Type</Text>
            <Grid>
              {operationTypes.map((type) => (
                <Grid.Col key={type.value} span={{ base: 12, sm: 6 }}>
                  <Paper
                    p="md"
                    withBorder
                    style={{
                      cursor: 'pointer',
                      backgroundColor: batchOperationType === type.value ? 'var(--mantine-color-blue-0)' : undefined,
                      borderColor: batchOperationType === type.value ? 'var(--mantine-color-blue-6)' : undefined
                    }}
                    onClick={() => setBatchOperationType(type.value)}
                  >
                    <Group>
                      <Checkbox
                        checked={batchOperationType === type.value}
                        onChange={() => setBatchOperationType(type.value)}
                      />
                      {type.icon}
                      <div>
                        <Text fw={500} size="sm">{type.label}</Text>
                        <Text size="xs" c="dimmed">{type.description}</Text>
                      </div>
                    </Group>
                  </Paper>
                </Grid.Col>
              ))}
            </Grid>
          </div>

          {/* Step 2: Vehicle Selection */}
          {batchOperationType && (
            <div>
              <Text fw={500} mb="sm">Step 2: Select Vehicles ({selectedVehicles.length} selected)</Text>

              {/* Vehicle Selection Filters */}
              <Grid mb="md">
                <Grid.Col span={4}>
                  <TextInput
                    placeholder="Search vehicles..."
                    leftSection={<IconSearch size={16} />}
                    value={vehicleSearchQuery}
                    onChange={(e) => setVehicleSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    placeholder="All Status"
                    data={[
                      { value: '', label: 'All Status' },
                      { value: 'available', label: 'Available' },
                      { value: 'rented', label: 'Rented' },
                      { value: 'maintenance', label: 'Maintenance' },
                      { value: 'out-of-service', label: 'Out of Service' }
                    ]}
                    value={vehicleStatusFilter}
                    onChange={(value) => setVehicleStatusFilter(value || '')}
                  />
                </Grid.Col>
                <Grid.Col span={3}>
                  <Select
                    placeholder="All Categories"
                    data={[
                      { value: '', label: 'All Categories' },
                      { value: 'Economy', label: 'Economy' },
                      { value: 'Sedan', label: 'Sedan' },
                      { value: 'SUV', label: 'SUV' },
                      { value: 'Luxury', label: 'Luxury' }
                    ]}
                    value={vehicleCategoryFilter}
                    onChange={(value) => setVehicleCategoryFilter(value || '')}
                  />
                </Grid.Col>
                <Grid.Col span={2}>
                  <Group>
                    <Button
                      variant="light"
                      size="sm"
                      onClick={() => setSelectedVehicles(vehicles.map(v => v.id))}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="light"
                      size="sm"
                      onClick={() => setSelectedVehicles([])}
                    >
                      Clear
                    </Button>
                  </Group>
                </Grid.Col>
              </Grid>

              {/* Vehicle Selection Table */}
              <Paper withBorder style={{ maxHeight: '300px', overflow: 'auto' }}>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>
                        <Checkbox
                          checked={selectedVehicles.length === vehicles.length && vehicles.length > 0}
                          indeterminate={selectedVehicles.length > 0 && selectedVehicles.length < vehicles.length}
                          onChange={(event) => {
                            if (event.currentTarget.checked) {
                              setSelectedVehicles(vehicles.map(v => v.id))
                            } else {
                              setSelectedVehicles([])
                            }
                          }}
                        />
                      </Table.Th>
                      <Table.Th>Vehicle</Table.Th>
                      <Table.Th>Category</Table.Th>
                      <Table.Th>Status</Table.Th>
                      <Table.Th>Location</Table.Th>
                      <Table.Th>Daily Rate</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {vehicles
                      .filter(vehicle =>
                        (!vehicleSearchQuery ||
                         vehicle.make.toLowerCase().includes(vehicleSearchQuery.toLowerCase()) ||
                         vehicle.model.toLowerCase().includes(vehicleSearchQuery.toLowerCase()) ||
                         vehicle.plateNumber.toLowerCase().includes(vehicleSearchQuery.toLowerCase())) &&
                        (!vehicleStatusFilter || vehicle.status === vehicleStatusFilter) &&
                        (!vehicleCategoryFilter || vehicle.category === vehicleCategoryFilter)
                      )
                      .map((vehicle) => (
                        <Table.Tr key={vehicle.id}>
                          <Table.Td>
                            <Checkbox
                              checked={selectedVehicles.includes(vehicle.id)}
                              onChange={(event) => {
                                if (event.currentTarget.checked) {
                                  setSelectedVehicles([...selectedVehicles, vehicle.id])
                                } else {
                                  setSelectedVehicles(selectedVehicles.filter(id => id !== vehicle.id))
                                }
                              }}
                            />
                          </Table.Td>
                          <Table.Td>
                            <div>
                              <Text fw={500} size="sm">{vehicle.make} {vehicle.model} {vehicle.year}</Text>
                              <Text size="xs" c="dimmed">{vehicle.plateNumber}</Text>
                            </div>
                          </Table.Td>
                          <Table.Td>
                            <Badge variant="light">{vehicle.category}</Badge>
                          </Table.Td>
                          <Table.Td>
                            <Badge color={vehicle.status === 'available' ? 'green' : vehicle.status === 'rented' ? 'blue' : 'orange'}>
                              {vehicle.status.charAt(0).toUpperCase() + vehicle.status.slice(1)}
                            </Badge>
                          </Table.Td>
                          <Table.Td>{vehicle.location}</Table.Td>
                          <Table.Td>AED {vehicle.dailyRate}</Table.Td>
                        </Table.Tr>
                      ))}
                  </Table.Tbody>
                </Table>
              </Paper>
            </div>
          )}

          {/* Step 3: Operation Configuration */}
          {batchOperationType && selectedVehicles.length > 0 && (
            <div>
              <Text fw={500} mb="sm">Step 3: Configure Operation</Text>

              {batchOperationType === 'price' && (
                <Grid>
                  <Grid.Col span={6}>
                    <Select
                      label="Price Update Method"
                      placeholder="Select method"
                      data={[
                        { value: 'percentage', label: 'Percentage Change' },
                        { value: 'fixed', label: 'Fixed Amount Change' },
                        { value: 'set', label: 'Set New Price' }
                      ]}
                      value={batchOperationData.priceMethod}
                      onChange={(value) => setBatchOperationData({...batchOperationData, priceMethod: value})}
                      required
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <NumberInput
                      label={batchOperationData.priceMethod === 'percentage' ? 'Percentage (%)' : 'Amount (AED)'}
                      placeholder={batchOperationData.priceMethod === 'percentage' ? '15' : '50'}
                      value={batchOperationData.priceValue}
                      onChange={(value) => setBatchOperationData({...batchOperationData, priceValue: value})}
                      required
                    />
                  </Grid.Col>
                </Grid>
              )}

              {batchOperationType === 'status' && (
                <Select
                  label="New Status"
                  placeholder="Select new status"
                  data={[
                    { value: 'available', label: 'Available' },
                    { value: 'maintenance', label: 'Maintenance' },
                    { value: 'out-of-service', label: 'Out of Service' }
                  ]}
                  value={batchOperationData.newStatus}
                  onChange={(value) => setBatchOperationData({...batchOperationData, newStatus: value})}
                  required
                />
              )}

              {batchOperationType === 'location' && (
                <Select
                  label="New Location"
                  placeholder="Select new location"
                  data={[
                    { value: 'Dubai', label: 'Dubai' },
                    { value: 'Abu Dhabi', label: 'Abu Dhabi' },
                    { value: 'Sharjah', label: 'Sharjah' },
                    { value: 'Ajman', label: 'Ajman' },
                    { value: 'Ras Al Khaimah', label: 'Ras Al Khaimah' }
                  ]}
                  value={batchOperationData.newLocation}
                  onChange={(value) => setBatchOperationData({...batchOperationData, newLocation: value})}
                  required
                />
              )}

              {batchOperationType === 'category' && (
                <Select
                  label="New Category"
                  placeholder="Select new category"
                  data={[
                    { value: 'Economy', label: 'Economy' },
                    { value: 'Sedan', label: 'Sedan' },
                    { value: 'SUV', label: 'SUV' },
                    { value: 'Luxury', label: 'Luxury' },
                    { value: 'Sports', label: 'Sports' }
                  ]}
                  value={batchOperationData.newCategory}
                  onChange={(value) => setBatchOperationData({...batchOperationData, newCategory: value})}
                  required
                />
              )}

              {batchOperationType === 'maintenance' && (
                <Grid>
                  <Grid.Col span={6}>
                    <Select
                      label="Maintenance Type"
                      placeholder="Select maintenance type"
                      data={[
                        { value: 'routine', label: 'Routine Maintenance' },
                        { value: 'inspection', label: 'Safety Inspection' },
                        { value: 'repair', label: 'Repair Work' },
                        { value: 'service', label: 'Full Service' }
                      ]}
                      value={batchOperationData.maintenanceType}
                      onChange={(value) => setBatchOperationData({...batchOperationData, maintenanceType: value})}
                      required
                    />
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <TextInput
                      label="Scheduled Date"
                      placeholder="YYYY-MM-DD"
                      type="date"
                      value={batchOperationData.maintenanceDate}
                      onChange={(e) => setBatchOperationData({...batchOperationData, maintenanceDate: e.target.value})}
                      required
                    />
                  </Grid.Col>
                </Grid>
              )}

              <Textarea
                label="Operation Notes"
                placeholder="Add notes about this batch operation..."
                rows={3}
                value={batchOperationData.notes}
                onChange={(e) => setBatchOperationData({...batchOperationData, notes: e.target.value})}
              />

              <Group>
                <Checkbox
                  label="Send notification to affected customers"
                  checked={batchOperationData.notifyCustomers}
                  onChange={(e) => setBatchOperationData({...batchOperationData, notifyCustomers: e.currentTarget.checked})}
                />
                <Checkbox
                  label="Generate operation log"
                  checked={batchOperationData.generateLog}
                  onChange={(e) => setBatchOperationData({...batchOperationData, generateLog: e.currentTarget.checked})}
                  defaultChecked
                />
              </Group>
            </div>
          )}

          <Divider />

          <Group justify="space-between">
            <Text size="sm" c="dimmed">
              {selectedVehicles.length > 0
                ? `This operation will affect ${selectedVehicles.length} vehicle${selectedVehicles.length > 1 ? 's' : ''}`
                : 'Please select vehicles and configure the operation'
              }
            </Text>
            <Group>
              <Button variant="light" onClick={() => {
                setBatchModalOpen(false)
                setBatchOperationType('')
                setSelectedVehicles([])
                setBatchOperationData({})
              }}>
                Cancel
              </Button>
              <Button
                leftSection={<IconStack size={16} />}
                disabled={!batchOperationType || selectedVehicles.length === 0}
                onClick={() => {
                  // Handle batch operation execution
                  console.log('Executing batch operation:', {
                    type: batchOperationType,
                    vehicles: selectedVehicles,
                    data: batchOperationData
                  })
                  setBatchModalOpen(false)
                  setBatchOperationType('')
                  setSelectedVehicles([])
                  setBatchOperationData({})
                  // Show success notification or redirect
                }}
              >
                Execute Batch Operation
              </Button>
            </Group>
          </Group>
        </Stack>
      </Modal>

      {/* Confirmation Modal */}
      <Modal
        opened={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        title={t('confirmBatchOperation')}
        withCloseButton
      >
        <Stack>
          <Alert icon={<IconAlertTriangle size={16} />} color="red">
            {t('thisActionCannotBeUndone')}
          </Alert>

          <Text>{t('areYouSureYouWantToExecuteThisOperation')}</Text>

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setConfirmModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button color="red" onClick={() => setConfirmModalOpen(false)}>
              {t('confirm')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}
