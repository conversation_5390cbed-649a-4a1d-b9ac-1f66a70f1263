import { Container, Title, Text, Card, Group, Badge, Button, Table, ActionIcon, Stack, Grid, NumberInput, Select, TextInput, Modal, Textarea } from '@mantine/core'
import { IconPlus, IconEdit, IconTrash, IconEye, IconDownload, IconSearch, IconFilter, IconAlertTriangle, IconCurrencyDollar, IconCalendar, IconMapPin, IconCheck } from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { notifications } from '@mantine/notifications'

interface TrafficFine {
  id: string
  vehicle_plate: string
  vehicle_model: string
  violation_type: string
  violation_date: string
  location: string
  fine_amount: number
  status: 'pending' | 'paid' | 'disputed' | 'cancelled'
  payment_deadline: string
  reference_number: string
  customer_name?: string
  contract_id?: string
}

export function TrafficFines() {
  const { t } = useTranslation()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedFine, setSelectedFine] = useState<TrafficFine | null>(null)

  // Form data for adding new fine
  const [formData, setFormData] = useState({
    vehicle_plate: '',
    violation_type: '',
    violation_date: '',
    location: '',
    fine_amount: 0,
    reference_number: '',
    payment_deadline: '',
    notes: ''
  })

  // Mock data - now using state so we can add to it
  const [trafficFines, setTrafficFines] = useState<TrafficFine[]>([
    {
      id: '1',
      vehicle_plate: 'ABC-123',
      vehicle_model: 'Toyota Camry 2023',
      violation_type: 'Speeding',
      violation_date: '2024-01-15',
      location: 'Sheikh Zayed Road, Dubai',
      fine_amount: 600,
      status: 'pending',
      payment_deadline: '2024-02-15',
      reference_number: 'TF-2024-001',
      customer_name: 'Ahmed Al-Rashid',
      contract_id: 'RC-2024-001'
    },
    {
      id: '2',
      vehicle_plate: 'XYZ-789',
      vehicle_model: 'Honda Accord 2022',
      violation_type: 'Red Light Violation',
      violation_date: '2024-01-20',
      location: 'Al Wasl Road, Dubai',
      fine_amount: 1000,
      status: 'paid',
      payment_deadline: '2024-02-20',
      reference_number: 'TF-2024-002'
    }
  ])

  // Handler functions
  const handleAddFine = () => {
    if (!formData.vehicle_plate || !formData.violation_type || !formData.violation_date ||
        !formData.location || !formData.fine_amount || !formData.payment_deadline) {
      notifications.show({
        title: 'Validation Error',
        message: 'Please fill in all required fields.',
        color: 'red'
      })
      return
    }

    const newFine: TrafficFine = {
      id: Date.now().toString(),
      vehicle_plate: formData.vehicle_plate,
      vehicle_model: formData.vehicle_plate === 'ABC-123' ? 'Toyota Camry 2023' : 'Honda Accord 2022',
      violation_type: formData.violation_type,
      violation_date: formData.violation_date,
      location: formData.location,
      fine_amount: formData.fine_amount,
      status: 'pending',
      payment_deadline: formData.payment_deadline,
      reference_number: formData.reference_number || `TF-${Date.now()}`
    }

    setTrafficFines(prev => [...prev, newFine])
    setAddModalOpen(false)
    resetForm()

    notifications.show({
      title: 'Fine Added',
      message: 'Traffic fine has been added successfully.',
      color: 'green',
      icon: <IconCheck size={16} />
    })
  }

  const resetForm = () => {
    setFormData({
      vehicle_plate: '',
      violation_type: '',
      violation_date: '',
      location: '',
      fine_amount: 0,
      reference_number: '',
      payment_deadline: '',
      notes: ''
    })
  }

  const handleCloseAddModal = () => {
    setAddModalOpen(false)
    resetForm()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'green'
      case 'pending': return 'orange'
      case 'disputed': return 'blue'
      case 'cancelled': return 'gray'
      default: return 'gray'
    }
  }

  const filteredFines = trafficFines.filter(fine => {
    const matchesSearch = fine.vehicle_plate.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         fine.violation_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         fine.location.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || fine.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const totalFines = trafficFines.length
  const pendingFines = trafficFines.filter(f => f.status === 'pending').length
  const paidFines = trafficFines.filter(f => f.status === 'paid').length
  const totalAmount = trafficFines.reduce((sum, f) => sum + f.fine_amount, 0)

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={2}>{t('trafficFines')}</Title>
            <Text c="dimmed">{t('manageTrafficViolationsAndFines')}</Text>
          </div>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
            {t('addFine')}
          </Button>
        </Group>

        {/* Stats Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalFines')}</Text>
                  <Text fw={700} size="xl">{totalFines}</Text>
                </div>
                <IconAlertTriangle size={24} color="orange" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('pendingFines')}</Text>
                  <Text fw={700} size="xl">{pendingFines}</Text>
                </div>
                <IconAlertTriangle size={24} color="red" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('paidFines')}</Text>
                  <Text fw={700} size="xl">{paidFines}</Text>
                </div>
                <IconAlertTriangle size={24} color="green" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalAmount')}</Text>
                  <Text fw={700} size="xl">AED {totalAmount.toLocaleString()}</Text>
                </div>
                <IconCurrencyDollar size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Search and Filters */}
        <Card withBorder p="md">
          <Group>
            <TextInput
              placeholder={t('searchFines')}
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ flex: 1 }}
            />
            <Select
              placeholder={t('filterByStatus')}
              leftSection={<IconFilter size={16} />}
              data={[
                { value: '', label: t('allStatuses') },
                { value: 'pending', label: t('pending') },
                { value: 'paid', label: t('paid') },
                { value: 'disputed', label: t('disputed') },
                { value: 'cancelled', label: t('cancelled') }
              ]}
              value={statusFilter}
              onChange={(value) => setStatusFilter(value || '')}
              clearable
            />
            <Button leftSection={<IconDownload size={16} />} variant="light">
              {t('export')}
            </Button>
          </Group>
        </Card>

        {/* Traffic Fines Table */}
        <Card withBorder>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>{t('vehicle')}</Table.Th>
                <Table.Th>{t('violationType')}</Table.Th>
                <Table.Th>{t('date')}</Table.Th>
                <Table.Th>{t('location')}</Table.Th>
                <Table.Th>{t('amount')}</Table.Th>
                <Table.Th>{t('status')}</Table.Th>
                <Table.Th>{t('deadline')}</Table.Th>
                <Table.Th>{t('actions')}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {filteredFines.map((fine) => (
                <Table.Tr key={fine.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{fine.vehicle_plate}</Text>
                      <Text size="sm" c="dimmed">{fine.vehicle_model}</Text>
                    </div>
                  </Table.Td>
                  <Table.Td>{fine.violation_type}</Table.Td>
                  <Table.Td>{fine.violation_date}</Table.Td>
                  <Table.Td>{fine.location}</Table.Td>
                  <Table.Td>AED {fine.fine_amount.toLocaleString()}</Table.Td>
                  <Table.Td>
                    <Badge color={getStatusColor(fine.status)}>
                      {t(fine.status)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>{fine.payment_deadline}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon variant="light" onClick={() => {
                        setSelectedFine(fine)
                        setEditModalOpen(true)
                      }}>
                        <IconEye size={16} />
                      </ActionIcon>
                      <ActionIcon variant="light" color="blue" onClick={() => {
                        setSelectedFine(fine)
                        setEditModalOpen(true)
                      }}>
                        <IconEdit size={16} />
                      </ActionIcon>
                      <ActionIcon variant="light" color="red">
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>

        {/* Add Fine Modal */}
        <Modal
          opened={addModalOpen}
          onClose={handleCloseAddModal}
          title={t('addTrafficFine')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('vehicle')}
                  placeholder={t('selectVehicle')}
                  data={[
                    { value: 'ABC-123', label: 'ABC-123 - Toyota Camry 2023' },
                    { value: 'XYZ-789', label: 'XYZ-789 - Honda Accord 2022' }
                  ]}
                  value={formData.vehicle_plate}
                  onChange={(value) => setFormData(prev => ({ ...prev, vehicle_plate: value || '' }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('violationType')}
                  placeholder={t('selectViolationType')}
                  data={[
                    { value: 'Speeding', label: 'Speeding' },
                    { value: 'Red Light Violation', label: 'Red Light Violation' },
                    { value: 'Parking Violation', label: 'Parking Violation' },
                    { value: 'Reckless Driving', label: 'Reckless Driving' }
                  ]}
                  value={formData.violation_type}
                  onChange={(value) => setFormData(prev => ({ ...prev, violation_type: value || '' }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('violationDate')}
                  type="date"
                  value={formData.violation_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, violation_date: e.target.value }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('fineAmount')}
                  placeholder="0"
                  min={0}
                  prefix="AED "
                  value={formData.fine_amount}
                  onChange={(value) => setFormData(prev => ({ ...prev, fine_amount: value || 0 }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <TextInput
                  label={t('location')}
                  placeholder={t('enterViolationLocation')}
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('referenceNumber')}
                  placeholder={t('enterReferenceNumber')}
                  value={formData.reference_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, reference_number: e.target.value }))}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('paymentDeadline')}
                  type="date"
                  value={formData.payment_deadline}
                  onChange={(e) => setFormData(prev => ({ ...prev, payment_deadline: e.target.value }))}
                  required
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('notes')}
                  placeholder={t('additionalNotes')}
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid.Col>
            </Grid>
            <Group justify="flex-end">
              <Button variant="light" onClick={handleCloseAddModal}>
                {t('cancel')}
              </Button>
              <Button onClick={handleAddFine}>
                {t('addFine')}
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}
