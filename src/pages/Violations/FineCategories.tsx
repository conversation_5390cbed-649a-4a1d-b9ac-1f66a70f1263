import { Container, Title, Text, Card, Group, Badge, Button, Table, ActionIcon, Stack, Grid, NumberInput, TextInput, Modal, Textarea, Switch } from '@mantine/core'
import { IconPlus, IconEdit, IconTrash, IconCategory, IconCurrencyDollar, IconAlertTriangle, IconSettings } from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

interface FineCategory {
  id: string
  name: string
  description: string
  base_amount: number
  severity_level: 'low' | 'medium' | 'high' | 'critical'
  points: number
  is_active: boolean
  created_at: string
}

export function FineCategories() {
  const { t } = useTranslation()
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<FineCategory | null>(null)

  // Mock data
  const fineCategories: FineCategory[] = [
    {
      id: '1',
      name: 'Speeding',
      description: 'Exceeding speed limit violations',
      base_amount: 600,
      severity_level: 'medium',
      points: 4,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      id: '2',
      name: 'Red Light Violation',
      description: 'Running red traffic lights',
      base_amount: 1000,
      severity_level: 'high',
      points: 12,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      id: '3',
      name: 'Parking Violation',
      description: 'Illegal parking violations',
      base_amount: 200,
      severity_level: 'low',
      points: 0,
      is_active: true,
      created_at: '2024-01-01'
    },
    {
      id: '4',
      name: 'Reckless Driving',
      description: 'Dangerous and reckless driving behavior',
      base_amount: 2000,
      severity_level: 'critical',
      points: 23,
      is_active: true,
      created_at: '2024-01-01'
    }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'green'
      case 'medium': return 'yellow'
      case 'high': return 'orange'
      case 'critical': return 'red'
      default: return 'gray'
    }
  }

  const totalCategories = fineCategories.length
  const activeCategories = fineCategories.filter(c => c.is_active).length
  const averageAmount = fineCategories.reduce((sum, c) => sum + c.base_amount, 0) / fineCategories.length
  const totalPoints = fineCategories.reduce((sum, c) => sum + c.points, 0)

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={2}>{t('fineCategories')}</Title>
            <Text c="dimmed">{t('manageFineTypesAndCategories')}</Text>
          </div>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
            {t('addCategory')}
          </Button>
        </Group>

        {/* Stats Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalCategories')}</Text>
                  <Text fw={700} size="xl">{totalCategories}</Text>
                </div>
                <IconCategory size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('activeCategories')}</Text>
                  <Text fw={700} size="xl">{activeCategories}</Text>
                </div>
                <IconSettings size={24} color="green" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('averageAmount')}</Text>
                  <Text fw={700} size="xl">AED {Math.round(averageAmount)}</Text>
                </div>
                <IconCurrencyDollar size={24} color="orange" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalPoints')}</Text>
                  <Text fw={700} size="xl">{totalPoints}</Text>
                </div>
                <IconAlertTriangle size={24} color="red" />
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Fine Categories Table */}
        <Card withBorder>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>{t('category')}</Table.Th>
                <Table.Th>{t('baseAmount')}</Table.Th>
                <Table.Th>{t('severity')}</Table.Th>
                <Table.Th>{t('points')}</Table.Th>
                <Table.Th>{t('status')}</Table.Th>
                <Table.Th>{t('actions')}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {fineCategories.map((category) => (
                <Table.Tr key={category.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{category.name}</Text>
                      <Text size="sm" c="dimmed">{category.description}</Text>
                    </div>
                  </Table.Td>
                  <Table.Td>AED {category.base_amount.toLocaleString()}</Table.Td>
                  <Table.Td>
                    <Badge color={getSeverityColor(category.severity_level)}>
                      {t(category.severity_level)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>{category.points}</Table.Td>
                  <Table.Td>
                    <Badge color={category.is_active ? 'green' : 'gray'}>
                      {category.is_active ? t('active') : t('inactive')}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon variant="light" color="blue" onClick={() => {
                        setSelectedCategory(category)
                        setEditModalOpen(true)
                      }}>
                        <IconEdit size={16} />
                      </ActionIcon>
                      <ActionIcon variant="light" color="red">
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>

        {/* Add Category Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('addFineCategory')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={12}>
                <TextInput
                  label={t('categoryName')}
                  placeholder={t('enterCategoryName')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={12}>
                <Textarea
                  label={t('description')}
                  placeholder={t('enterCategoryDescription')}
                  rows={3}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('baseAmount')}
                  placeholder="0"
                  min={0}
                  prefix="AED "
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('points')}
                  placeholder="0"
                  min={0}
                  max={24}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('severityLevel')}
                  placeholder={t('selectSeverity')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label={t('active')}
                  description={t('enableThisCategory')}
                  defaultChecked
                />
              </Grid.Col>
            </Grid>
            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button onClick={() => setAddModalOpen(false)}>
                {t('addCategory')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* Edit Category Modal */}
        <Modal
          opened={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          title={t('editFineCategory')}
          size="lg"
        >
          {selectedCategory && (
            <Stack>
              <Grid>
                <Grid.Col span={12}>
                  <TextInput
                    label={t('categoryName')}
                    defaultValue={selectedCategory.name}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    label={t('description')}
                    defaultValue={selectedCategory.description}
                    rows={3}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <NumberInput
                    label={t('baseAmount')}
                    defaultValue={selectedCategory.base_amount}
                    min={0}
                    prefix="AED "
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <NumberInput
                    label={t('points')}
                    defaultValue={selectedCategory.points}
                    min={0}
                    max={24}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <TextInput
                    label={t('severityLevel')}
                    defaultValue={selectedCategory.severity_level}
                    required
                  />
                </Grid.Col>
                <Grid.Col span={6}>
                  <Switch
                    label={t('active')}
                    description={t('enableThisCategory')}
                    defaultChecked={selectedCategory.is_active}
                  />
                </Grid.Col>
              </Grid>
              <Group justify="flex-end">
                <Button variant="light" onClick={() => setEditModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button onClick={() => setEditModalOpen(false)}>
                  {t('updateCategory')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Stack>
    </Container>
  )
}
