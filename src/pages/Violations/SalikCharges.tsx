import { Container, Title, Text, Card, Group, Badge, Button, Table, ActionIcon, Stack, Grid, NumberInput, Select, TextInput, Modal, Switch } from '@mantine/core'
import { IconPlus, IconEdit, IconTrash, IconEye, IconDownload, IconSearch, IconFilter, IconRoad, IconCurrencyDollar, IconCalendar, IconMapPin } from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

interface SalikCharge {
  id: string
  vehicle_plate: string
  vehicle_model: string
  charge_date: string
  location: string
  amount: number
  status: 'pending' | 'charged_to_customer' | 'paid_by_company'
  reference_number: string
  customer_name?: string
  contract_id?: string
  charged_to_customer: boolean
}

export function SalikCharges() {
  const { t } = useTranslation()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedCharge, setSelectedCharge] = useState<SalikCharge | null>(null)

  // Mock data
  const salikCharges: SalikCharge[] = [
    {
      id: '1',
      vehicle_plate: 'ABC-123',
      vehicle_model: 'Toyota Camry 2023',
      charge_date: '2024-01-15',
      location: 'Al Garhoud Bridge',
      amount: 4,
      status: 'charged_to_customer',
      reference_number: 'SK-2024-001',
      customer_name: 'Ahmed Al-Rashid',
      contract_id: 'RC-2024-001',
      charged_to_customer: true
    },
    {
      id: '2',
      vehicle_plate: 'XYZ-789',
      vehicle_model: 'Honda Accord 2022',
      charge_date: '2024-01-20',
      location: 'Sheikh Zayed Road',
      amount: 4,
      status: 'paid_by_company',
      reference_number: 'SK-2024-002',
      charged_to_customer: false
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'charged_to_customer': return 'green'
      case 'pending': return 'orange'
      case 'paid_by_company': return 'blue'
      default: return 'gray'
    }
  }

  const filteredCharges = salikCharges.filter(charge => {
    const matchesSearch = charge.vehicle_plate.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         charge.location.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || charge.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const totalCharges = salikCharges.length
  const pendingCharges = salikCharges.filter(c => c.status === 'pending').length
  const customerCharges = salikCharges.filter(c => c.charged_to_customer).length
  const totalAmount = salikCharges.reduce((sum, c) => sum + c.amount, 0)

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={2}>{t('salikCharges')}</Title>
            <Text c="dimmed">{t('manageSalikTollCharges')}</Text>
          </div>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
            {t('addCharge')}
          </Button>
        </Group>

        {/* Stats Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalCharges')}</Text>
                  <Text fw={700} size="xl">{totalCharges}</Text>
                </div>
                <IconRoad size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('pendingCharges')}</Text>
                  <Text fw={700} size="xl">{pendingCharges}</Text>
                </div>
                <IconRoad size={24} color="orange" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('customerCharges')}</Text>
                  <Text fw={700} size="xl">{customerCharges}</Text>
                </div>
                <IconRoad size={24} color="green" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalAmount')}</Text>
                  <Text fw={700} size="xl">AED {totalAmount.toLocaleString()}</Text>
                </div>
                <IconCurrencyDollar size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Search and Filters */}
        <Card withBorder p="md">
          <Group>
            <TextInput
              placeholder={t('searchCharges')}
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ flex: 1 }}
            />
            <Select
              placeholder={t('filterByStatus')}
              leftSection={<IconFilter size={16} />}
              data={[
                { value: '', label: t('allStatuses') },
                { value: 'pending', label: t('pending') },
                { value: 'charged_to_customer', label: t('chargedToCustomer') },
                { value: 'paid_by_company', label: t('paidByCompany') }
              ]}
              value={statusFilter}
              onChange={(value) => setStatusFilter(value || '')}
              clearable
            />
            <Button leftSection={<IconDownload size={16} />} variant="light">
              {t('export')}
            </Button>
          </Group>
        </Card>

        {/* Salik Charges Table */}
        <Card withBorder>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>{t('vehicle')}</Table.Th>
                <Table.Th>{t('date')}</Table.Th>
                <Table.Th>{t('location')}</Table.Th>
                <Table.Th>{t('amount')}</Table.Th>
                <Table.Th>{t('status')}</Table.Th>
                <Table.Th>{t('customer')}</Table.Th>
                <Table.Th>{t('actions')}</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {filteredCharges.map((charge) => (
                <Table.Tr key={charge.id}>
                  <Table.Td>
                    <div>
                      <Text fw={500}>{charge.vehicle_plate}</Text>
                      <Text size="sm" c="dimmed">{charge.vehicle_model}</Text>
                    </div>
                  </Table.Td>
                  <Table.Td>{charge.charge_date}</Table.Td>
                  <Table.Td>{charge.location}</Table.Td>
                  <Table.Td>AED {charge.amount.toLocaleString()}</Table.Td>
                  <Table.Td>
                    <Badge color={getStatusColor(charge.status)}>
                      {t(charge.status)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>{charge.customer_name || '-'}</Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      <ActionIcon variant="light" onClick={() => {
                        setSelectedCharge(charge)
                        setEditModalOpen(true)
                      }}>
                        <IconEye size={16} />
                      </ActionIcon>
                      <ActionIcon variant="light" color="blue" onClick={() => {
                        setSelectedCharge(charge)
                        setEditModalOpen(true)
                      }}>
                        <IconEdit size={16} />
                      </ActionIcon>
                      <ActionIcon variant="light" color="red">
                        <IconTrash size={16} />
                      </ActionIcon>
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>
        </Card>

        {/* Add Charge Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('addSalikCharge')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('vehicle')}
                  placeholder={t('selectVehicle')}
                  data={[
                    { value: 'ABC-123', label: 'ABC-123 - Toyota Camry 2023' },
                    { value: 'XYZ-789', label: 'XYZ-789 - Honda Accord 2022' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('chargeDate')}
                  type="date"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('location')}
                  placeholder={t('selectLocation')}
                  data={[
                    { value: 'al_garhoud', label: 'Al Garhoud Bridge' },
                    { value: 'sheikh_zayed', label: 'Sheikh Zayed Road' },
                    { value: 'al_maktoum', label: 'Al Maktoum Bridge' },
                    { value: 'business_bay', label: 'Business Bay Crossing' }
                  ]}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('amount')}
                  placeholder="4"
                  min={0}
                  prefix="AED "
                  defaultValue={4}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('referenceNumber')}
                  placeholder={t('enterReferenceNumber')}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Switch
                  label={t('chargeToCustomer')}
                  description={t('shouldThisChargeBePassedToCustomer')}
                />
              </Grid.Col>
            </Grid>
            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button onClick={() => setAddModalOpen(false)}>
                {t('addCharge')}
              </Button>
            </Group>
          </Stack>
        </Modal>
      </Stack>
    </Container>
  )
}
