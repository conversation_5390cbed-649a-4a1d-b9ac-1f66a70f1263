import { Container, Title, Text, Card, Group, Badge, Button, Table, Stack, Grid, Select, TextInput, Timeline, Alert } from '@mantine/core'
import { IconDownload, IconSearch, IconFilter, IconAlertTriangle, IconCurrencyDollar, IconCalendar, IconHistory, IconCar, IconMapPin } from '@tabler/icons-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

interface ViolationHistoryItem {
  id: string
  type: 'traffic_fine' | 'salik_charge'
  vehicle_plate: string
  vehicle_model: string
  date: string
  location: string
  amount: number
  status: string
  description: string
  customer_name?: string
}

export function ViolationHistory() {
  const { t } = useTranslation()
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('')
  const [vehicleFilter, setVehicleFilter] = useState<string>('')

  // Mock data
  const violationHistory: ViolationHistoryItem[] = [
    {
      id: '1',
      type: 'traffic_fine',
      vehicle_plate: 'ABC-123',
      vehicle_model: 'Toyota Camry 2023',
      date: '2024-01-15',
      location: 'Sheikh Zayed Road, Dubai',
      amount: 600,
      status: 'paid',
      description: 'Speeding violation - 80 km/h in 60 km/h zone',
      customer_name: 'Ahmed Al-Rashid'
    },
    {
      id: '2',
      type: 'salik_charge',
      vehicle_plate: 'ABC-123',
      vehicle_model: 'Toyota Camry 2023',
      date: '2024-01-14',
      location: 'Al Garhoud Bridge',
      amount: 4,
      status: 'charged_to_customer',
      description: 'Salik toll charge',
      customer_name: 'Ahmed Al-Rashid'
    },
    {
      id: '3',
      type: 'traffic_fine',
      vehicle_plate: 'XYZ-789',
      vehicle_model: 'Honda Accord 2022',
      date: '2024-01-10',
      location: 'Al Wasl Road, Dubai',
      amount: 1000,
      status: 'pending',
      description: 'Red light violation'
    }
  ]

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'traffic_fine': return 'red'
      case 'salik_charge': return 'blue'
      default: return 'gray'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'green'
      case 'charged_to_customer': return 'green'
      case 'pending': return 'orange'
      case 'disputed': return 'blue'
      case 'cancelled': return 'gray'
      default: return 'gray'
    }
  }

  const filteredHistory = violationHistory.filter(item => {
    const matchesSearch = item.vehicle_plate.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.location.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesType = !typeFilter || item.type === typeFilter
    const matchesVehicle = !vehicleFilter || item.vehicle_plate === vehicleFilter
    return matchesSearch && matchesType && matchesVehicle
  })

  const totalViolations = violationHistory.length
  const trafficFines = violationHistory.filter(v => v.type === 'traffic_fine').length
  const salikCharges = violationHistory.filter(v => v.type === 'salik_charge').length
  const totalAmount = violationHistory.reduce((sum, v) => sum + v.amount, 0)

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <div>
            <Title order={2}>{t('violationHistory')}</Title>
            <Text c="dimmed">{t('viewCompleteViolationHistory')}</Text>
          </div>
          <Button leftSection={<IconDownload size={16} />} variant="light">
            {t('exportHistory')}
          </Button>
        </Group>

        {/* Stats Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalViolations')}</Text>
                  <Text fw={700} size="xl">{totalViolations}</Text>
                </div>
                <IconHistory size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('trafficFines')}</Text>
                  <Text fw={700} size="xl">{trafficFines}</Text>
                </div>
                <IconAlertTriangle size={24} color="red" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('salikCharges')}</Text>
                  <Text fw={700} size="xl">{salikCharges}</Text>
                </div>
                <IconCar size={24} color="blue" />
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" tt="uppercase" fw={700} c="dimmed">{t('totalAmount')}</Text>
                  <Text fw={700} size="xl">AED {totalAmount.toLocaleString()}</Text>
                </div>
                <IconCurrencyDollar size={24} color="green" />
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Search and Filters */}
        <Card withBorder p="md">
          <Group>
            <TextInput
              placeholder={t('searchHistory')}
              leftSection={<IconSearch size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ flex: 1 }}
            />
            <Select
              placeholder={t('filterByType')}
              leftSection={<IconFilter size={16} />}
              data={[
                { value: '', label: t('allTypes') },
                { value: 'traffic_fine', label: t('trafficFines') },
                { value: 'salik_charge', label: t('salikCharges') }
              ]}
              value={typeFilter}
              onChange={(value) => setTypeFilter(value || '')}
              clearable
            />
            <Select
              placeholder={t('filterByVehicle')}
              leftSection={<IconCar size={16} />}
              data={[
                { value: '', label: t('allVehicles') },
                { value: 'ABC-123', label: 'ABC-123' },
                { value: 'XYZ-789', label: 'XYZ-789' }
              ]}
              value={vehicleFilter}
              onChange={(value) => setVehicleFilter(value || '')}
              clearable
            />
          </Group>
        </Card>

        {/* Violation History Timeline */}
        <Card withBorder>
          <Title order={4} mb="md">{t('violationTimeline')}</Title>
          <Timeline active={-1} bulletSize={24} lineWidth={2}>
            {filteredHistory.map((item) => (
              <Timeline.Item
                key={item.id}
                bullet={item.type === 'traffic_fine' ? <IconAlertTriangle size={12} /> : <IconCar size={12} />}
                title={
                  <Group>
                    <Badge color={getTypeColor(item.type)}>
                      {t(item.type)}
                    </Badge>
                    <Badge color={getStatusColor(item.status)}>
                      {t(item.status)}
                    </Badge>
                    <Text fw={500}>AED {item.amount}</Text>
                  </Group>
                }
              >
                <Text c="dimmed" size="sm">
                  {item.date} • {item.vehicle_plate} • {item.location}
                </Text>
                <Text size="sm" mt="xs">
                  {item.description}
                </Text>
                {item.customer_name && (
                  <Text size="xs" c="dimmed" mt="xs">
                    Customer: {item.customer_name}
                  </Text>
                )}
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>

        {/* Summary Alert */}
        <Alert icon={<IconHistory size={16} />} title={t('historySummary')} color="blue">
          <Text size="sm">
            {t('totalViolationsRecorded', { count: totalViolations })} {t('withTotalAmount')} AED {totalAmount.toLocaleString()}.
            {trafficFines > 0 && ` ${t('trafficFinesCount', { count: trafficFines })}.`}
            {salikCharges > 0 && ` ${t('salikChargesCount', { count: salikCharges })}.`}
          </Text>
        </Alert>
      </Stack>
    </Container>
  )
}
