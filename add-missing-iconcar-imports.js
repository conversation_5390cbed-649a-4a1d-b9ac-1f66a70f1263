#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function addMissingIconCarImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Check if file uses IconCar but doesn't import it
    if (content.includes('IconCar') && !content.includes('import') && !content.includes('IconCar')) {
      return; // Skip files without imports
    }

    // Check if IconCar is used but not imported
    const usesIconCar = content.includes('icon: IconCar') || content.includes('<IconCar');
    const importsIconCar = content.match(/import\s*{[^}]*IconCar[^}]*}\s*from\s*['"]@tabler\/icons-react['"]/);

    if (usesIconCar && !importsIconCar) {
      // Find the @tabler/icons-react import
      const importPattern = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@tabler\/icons-react['"]/;
      const match = content.match(importPattern);
      
      if (match) {
        const imports = match[1];
        // Clean up imports and add IconCar
        const importList = imports.split(',').map(imp => imp.trim()).filter(imp => imp);
        
        if (!importList.includes('IconCar')) {
          importList.push('IconCar');
          importList.sort(); // Sort alphabetically
          
          const newImports = importList.join(',\n  ');
          const newImportStatement = `import {\n  ${newImports}\n} from '@tabler/icons-react'`;
          
          content = content.replace(importPattern, newImportStatement);
          modified = true;
        }
      }
    }

    // Fix IconX usage in BlacklistBasic
    if (filePath.includes('BlacklistBasic') && content.includes('icon: IconX')) {
      const importPattern = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@tabler\/icons-react['"]/;
      const match = content.match(importPattern);
      
      if (match) {
        const imports = match[1];
        const importList = imports.split(',').map(imp => imp.trim()).filter(imp => imp);
        
        if (!importList.includes('IconX')) {
          importList.push('IconX');
          importList.sort();
          
          const newImports = importList.join(',\n  ');
          const newImportStatement = `import {\n  ${newImports}\n} from '@tabler/icons-react'`;
          
          content = content.replace(importPattern, newImportStatement);
          modified = true;
        }
      }
    }

    // Remove unused imports
    const unusedImports = ['IconCurrencyDollar', 'IconTriangle'];
    unusedImports.forEach(unusedImport => {
      if (content.includes(`import`) && content.includes(unusedImport) && !content.includes(`<${unusedImport}`) && !content.includes(`icon: ${unusedImport}`)) {
        // Remove from import statement
        const importPattern = new RegExp(`\\s*,?\\s*${unusedImport}\\s*,?`, 'g');
        content = content.replace(importPattern, '');
        
        // Clean up any double commas or leading/trailing commas
        content = content.replace(/,\s*,/g, ',');
        content = content.replace(/{\s*,/g, '{');
        content = content.replace(/,\s*}/g, '}');
        
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Added missing IconCar import to: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDirectory(filePath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      addMissingIconCarImports(filePath);
    }
  });
}

// Start processing from src directory
const srcDir = path.join(__dirname, 'src');
if (fs.existsSync(srcDir)) {
  console.log('🔧 Adding missing IconCar imports...');
  processDirectory(srcDir);
  console.log('✅ Finished adding missing IconCar imports');
} else {
  console.error('❌ src directory not found');
}
