#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Common unused imports to remove
const UNUSED_IMPORTS = [
  'Alert', 'Tabs', 'Avatar', 'Tooltip', 'IconUser', 'IconMapPin', 'IconAlertTriangle',
  'DatePickerInput', 'Checkbox', 'IconEdit', 'IconTrash', 'IconCreditCard', 'IconPrinter',
  'IconCar', 'IconX', 'IconPhone', 'Divider', 'IconCheck', 'IconCalendar', 'IconKey',
  'IconMail', 'IconPlus', 'IconClock', 'IconFileText', 'Progress', 'IconGasStation',
  'IconTrendingUp', 'Image', 'List', 'Box', 'Center', 'IconShield', 'Modal',
  'IconClipboardCheck', 'IconRoad', 'IconUpload', 'IconPackage', 'IconFilter'
];

function removeUnusedImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Remove unused imports from import statements
    UNUSED_IMPORTS.forEach(importName => {
      // Pattern to match import in import statement
      const patterns = [
        new RegExp(`\\s*${importName},\\s*`, 'g'), // middle of import list
        new RegExp(`\\s*,\\s*${importName}\\s*`, 'g'), // end of import list
        new RegExp(`\\s*${importName}\\s*`, 'g'), // only import
      ];

      patterns.forEach(pattern => {
        if (content.includes(importName) && !content.includes(`<${importName}`)) {
          const newContent = content.replace(pattern, '');
          if (newContent !== content) {
            content = newContent;
            modified = true;
          }
        }
      });
    });

    // Clean up empty import lines
    content = content.replace(/import\s*{\s*}\s*from\s*['"][^'"]*['"]\s*\n?/g, '');
    
    // Clean up trailing commas in imports
    content = content.replace(/,(\s*})/g, '$1');
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDirectory(filePath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      removeUnusedImports(filePath);
    }
  });
}

// Start processing from src directory
const srcDir = path.join(__dirname, 'src');
if (fs.existsSync(srcDir)) {
  processDirectory(srcDir);
  console.log('Finished removing unused imports');
} else {
  console.error('src directory not found');
}
