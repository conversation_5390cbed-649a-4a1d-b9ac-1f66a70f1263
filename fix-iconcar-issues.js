#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixIconCarIssues(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix JSX usage of IconCar where it's used as <IconCar size={16} />
    const iconCarJSXPattern = /<IconCar size=\{16\} \/>/g;
    if (content.match(iconCarJSXPattern)) {
      // Check if IconCar is imported
      if (!content.includes('IconCar')) {
        // Add IconCar to imports
        const importPattern = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@tabler\/icons-react['"]/;
        const match = content.match(importPattern);
        if (match) {
          const imports = match[1];
          const newImports = imports.includes('IconCar') ? imports : imports + ',\n  IconCar';
          content = content.replace(importPattern, `import {\n  ${newImports.replace(/,\s*/g, ',\n  ')}\n} from '@tabler/icons-react'`);
          modified = true;
        }
      }
    }

    // Fix stat.icon usage - convert JSX elements to icon components
    const statIconJSXPattern = /icon:\s*<IconCar size=\{16\} \/>/g;
    if (content.match(statIconJSXPattern)) {
      content = content.replace(statIconJSXPattern, 'icon: IconCar');
      modified = true;
    }

    // Fix broken icon names
    const brokenIconPatterns = [
      { from: 'IconPrinterer', to: 'IconPrinter' },
      { from: 'IconDownloadX', to: 'IconDownload' },
      { from: 'IconClipboardIconBook', to: 'IconClipboardList' },
      { from: 'IconWorlds', to: 'IconWorld' },
      { from: 'IconBook', to: 'IconClipboardList' }
    ];

    brokenIconPatterns.forEach(({ from, to }) => {
      const pattern = new RegExp(from, 'g');
      if (content.includes(from)) {
        content = content.replace(pattern, to);
        modified = true;
      }
    });

    // Fix broken JSX component usage like <stat.icon>
    const statIconComponentPattern = /<stat\.icon\s+size=\{24\}[^>]*>/g;
    if (content.match(statIconComponentPattern)) {
      // This needs manual fixing, but we can at least identify the files
      console.log(`⚠️  Manual fix needed for stat.icon usage in: ${filePath}`);
    }

    // Fix broken variable references like 'X' instead of 'IconX'
    const brokenVarPattern = /icon:\s*X\s*}/g;
    if (content.match(brokenVarPattern)) {
      content = content.replace(brokenVarPattern, 'icon: IconX }');
      modified = true;
    }

    // Clean up import formatting
    content = content.replace(/,\s*,/g, ','); // Remove double commas
    content = content.replace(/{\s*,/g, '{'); // Remove leading commas
    content = content.replace(/,\s*}/g, '\n}'); // Clean trailing commas

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed IconCar issues in: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

function processDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      processDirectory(filePath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fixIconCarIssues(filePath);
    }
  });
}

// Start processing from src directory
const srcDir = path.join(__dirname, 'src');
if (fs.existsSync(srcDir)) {
  console.log('🔧 Starting IconCar fixes...');
  processDirectory(srcDir);
  console.log('✅ Finished fixing IconCar issues');
} else {
  console.error('❌ src directory not found');
}
