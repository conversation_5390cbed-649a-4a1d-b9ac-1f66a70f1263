# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := locate_sqlite3
### Rules for action "copy_builtin_sqlite3":
quiet_cmd_deps_sqlite3_gyp_locate_sqlite3_target_copy_builtin_sqlite3 = ACTION deps_sqlite3_gyp_locate_sqlite3_target_copy_builtin_sqlite3 $@
cmd_deps_sqlite3_gyp_locate_sqlite3_target_copy_builtin_sqlite3 = LD_LIBRARY_PATH=$(builddir)/lib.host:$(builddir)/lib.target:$$LD_LIBRARY_PATH; export LD_LIBRARY_PATH; cd $(srcdir)/deps; mkdir -p $(obj)/gen/sqlite3; node copy.js "$(obj)/gen/sqlite3" ""

$(obj)/gen/sqlite3/sqlite3.c: obj := $(abs_obj)
$(obj)/gen/sqlite3/sqlite3.c: builddir := $(abs_builddir)
$(obj)/gen/sqlite3/sqlite3.c: export BUILT_FRAMEWORKS_DIR := ${abs_builddir}
$(obj)/gen/sqlite3/sqlite3.c: export BUILT_PRODUCTS_DIR := ${abs_builddir}
$(obj)/gen/sqlite3/sqlite3.c: export CONFIGURATION := ${BUILDTYPE}
$(obj)/gen/sqlite3/sqlite3.c: export PRODUCT_NAME := locate_sqlite3
$(obj)/gen/sqlite3/sqlite3.c: export SDKROOT := /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
$(obj)/gen/sqlite3/sqlite3.c: export SRCROOT := ${abs_srcdir}/deps
$(obj)/gen/sqlite3/sqlite3.c: export SOURCE_ROOT := ${SRCROOT}
$(obj)/gen/sqlite3/sqlite3.c: export TARGET_BUILD_DIR := ${abs_builddir}
$(obj)/gen/sqlite3/sqlite3.c: export TEMP_DIR := ${TMPDIR}
$(obj)/gen/sqlite3/sqlite3.c: export XCODE_VERSION_ACTUAL := 1420
$(obj)/gen/sqlite3/sqlite3.c: TOOLSET := $(TOOLSET)
$(obj)/gen/sqlite3/sqlite3.c $(obj)/gen/sqlite3/sqlite3.h $(obj)/gen/sqlite3/sqlite3ext.h: ba23eeee118cd63e16015df367567cb043fed872.intermediate
	@:
.INTERMEDIATE: ba23eeee118cd63e16015df367567cb043fed872.intermediate
ba23eeee118cd63e16015df367567cb043fed872.intermediate: $(srcdir)/deps/sqlite3/sqlite3.c $(srcdir)/deps/sqlite3/sqlite3.h $(srcdir)/deps/sqlite3/sqlite3ext.h FORCE_DO_CMD
	$(call do_cmd,touch)
	$(call do_cmd,deps_sqlite3_gyp_locate_sqlite3_target_copy_builtin_sqlite3)

all_deps += $(obj)/gen/sqlite3/sqlite3.c $(obj)/gen/sqlite3/sqlite3.h $(obj)/gen/sqlite3/sqlite3ext.h
action_deps_sqlite3_gyp_locate_sqlite3_target_copy_builtin_sqlite3_outputs := $(obj)/gen/sqlite3/sqlite3.c $(obj)/gen/sqlite3/sqlite3.h $(obj)/gen/sqlite3/sqlite3ext.h


### Rules for final target.
# Build our special outputs first.
$(obj).target/deps/locate_sqlite3.stamp: | $(action_deps_sqlite3_gyp_locate_sqlite3_target_copy_builtin_sqlite3_outputs)

# Preserve order dependency of special output on deps.
$(action_deps_sqlite3_gyp_locate_sqlite3_target_copy_builtin_sqlite3_outputs): | 

$(obj).target/deps/locate_sqlite3.stamp: TOOLSET := $(TOOLSET)
$(obj).target/deps/locate_sqlite3.stamp:  FORCE_DO_CMD
	$(call do_cmd,touch)

all_deps += $(obj).target/deps/locate_sqlite3.stamp
# Add target alias
.PHONY: locate_sqlite3
locate_sqlite3: $(obj).target/deps/locate_sqlite3.stamp

# Add target alias to "all" target.
.PHONY: all
all: locate_sqlite3

