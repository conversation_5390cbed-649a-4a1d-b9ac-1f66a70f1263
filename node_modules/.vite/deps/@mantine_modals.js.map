{"version": 3, "sources": ["../../@mantine/modals/src/context.ts", "../../@mantine/modals/src/use-modals/use-modals.ts", "../../@mantine/modals/src/ConfirmModal.tsx", "../../@mantine/modals/src/events.ts", "../../@mantine/modals/src/reducer.ts", "../../@mantine/modals/src/ModalsProvider.tsx"], "sourcesContent": ["import { createContext, ReactNode } from 'react';\nimport { ModalProps } from '@mantine/core';\nimport type { ConfirmModalProps } from './ConfirmModal';\n\nexport type ModalSettings = Partial<Omit<ModalProps, 'opened'>> & { modalId?: string };\n\nexport type ConfirmLabels = Record<'confirm' | 'cancel', ReactNode>;\n\nexport interface OpenConfirmModal extends ModalSettings, ConfirmModalProps {}\nexport interface OpenContextModal<CustomProps extends Record<string, any> = {}>\n  extends ModalSettings {\n  innerProps: CustomProps;\n}\n\nexport interface ContextModalProps<T extends Record<string, any> = {}> {\n  context: ModalsContextProps;\n  innerProps: T;\n  id: string;\n}\n\nexport type ModalState =\n  | { id: string; props: ModalSettings; type: 'content' }\n  | { id: string; props: OpenConfirmModal; type: 'confirm' }\n  | { id: string; props: OpenContextModal; type: 'context'; ctx: string };\n\nexport interface ModalsContextProps {\n  modalProps: ModalSettings;\n  modals: ModalState[];\n  openModal: (props: ModalSettings) => string;\n  openConfirmModal: (props: OpenConfirmModal) => string;\n  openContextModal: <TKey extends MantineModal>(\n    modal: TKey,\n    props: OpenContextModal<Parameters<MantineModals[TKey]>[0]['innerProps']>\n  ) => string;\n  closeModal: (id: string, canceled?: boolean) => void;\n  closeContextModal: <TKey extends MantineModal>(id: TKey, canceled?: boolean) => void;\n  closeAll: () => void;\n  updateModal: (payload: { modalId: string } & Partial<OpenConfirmModal>) => void;\n  updateContextModal: (payload: { modalId: string } & Partial<OpenContextModal<any>>) => void;\n}\n\nexport interface MantineModalsOverride {}\n\nexport type MantineModalsOverwritten = MantineModalsOverride extends {\n  modals: Record<string, React.FC<ContextModalProps<any>>>;\n}\n  ? MantineModalsOverride\n  : {\n      modals: Record<string, React.FC<ContextModalProps<any>>>;\n    };\n\nexport type MantineModals = MantineModalsOverwritten['modals'];\n\nexport type MantineModal = keyof MantineModals;\n\nexport const ModalsContext = createContext<ModalsContextProps>(null as any);\nModalsContext.displayName = '@mantine/modals/ModalsContext';\n", "import { useContext } from 'react';\nimport { ModalsContext } from '../context';\n\nexport function useModals() {\n  const ctx = useContext(ModalsContext);\n\n  if (!ctx) {\n    throw new Error(\n      '[@mantine/modals] useModals hook was called outside of context, wrap your app with ModalsProvider component'\n    );\n  }\n\n  return ctx;\n}\n", "import { Box, Button, ButtonProps, Group, GroupProps } from '@mantine/core';\nimport { ConfirmLabels } from './context';\nimport { useModals } from './use-modals/use-modals';\n\nexport interface ConfirmModalProps {\n  id?: string;\n  children?: React.ReactNode;\n  onCancel?: () => void;\n  onConfirm?: () => void;\n  closeOnConfirm?: boolean;\n  closeOnCancel?: boolean;\n  cancelProps?: ButtonProps &\n    React.ComponentPropsWithoutRef<'button'> &\n    Record<`data-${string}`, string>;\n  confirmProps?: ButtonProps &\n    React.ComponentPropsWithoutRef<'button'> &\n    Record<`data-${string}`, string>;\n  groupProps?: GroupProps;\n  labels?: ConfirmLabels;\n}\n\nexport function ConfirmModal({\n  id,\n  cancelProps,\n  confirmProps,\n  labels = { cancel: '', confirm: '' },\n  closeOnConfirm = true,\n  closeOnCancel = true,\n  groupProps,\n  onCancel,\n  onConfirm,\n  children,\n}: ConfirmModalProps) {\n  const { cancel: cancelLabel, confirm: confirmLabel } = labels;\n  const ctx = useModals();\n\n  const handleCancel = (event: React.MouseEvent<HTMLButtonElement>) => {\n    typeof cancelProps?.onClick === 'function' && cancelProps?.onClick(event);\n    typeof onCancel === 'function' && onCancel();\n    closeOnCancel && ctx.closeModal(id!);\n  };\n\n  const handleConfirm = (event: React.MouseEvent<HTMLButtonElement>) => {\n    typeof confirmProps?.onClick === 'function' && confirmProps?.onClick(event);\n    typeof onConfirm === 'function' && onConfirm();\n    closeOnConfirm && ctx.closeModal(id!);\n  };\n\n  return (\n    <>\n      {children && <Box mb=\"md\">{children}</Box>}\n\n      <Group mt={children ? 0 : 'md'} justify=\"flex-end\" {...groupProps}>\n        <Button variant=\"default\" {...cancelProps} onClick={handleCancel}>\n          {cancelProps?.children || cancelLabel}\n        </Button>\n\n        <Button {...confirmProps} onClick={handleConfirm}>\n          {confirmProps?.children || confirmLabel}\n        </Button>\n      </Group>\n    </>\n  );\n}\n", "import { createUseExternalEvents } from '@mantine/core';\nimport { randomId } from '@mantine/hooks';\nimport {\n  MantineModal,\n  MantineModals,\n  ModalSettings,\n  OpenConfirmModal,\n  OpenContextModal,\n} from './context';\n\ntype ModalsEvents = {\n  openModal: (payload: ModalSettings) => string;\n  openConfirmModal: (payload: OpenConfirmModal) => string;\n  openContextModal: <TKey extends MantineModal>(\n    payload: OpenContextModal<Parameters<MantineModals[TKey]>[0]['innerProps']> & { modal: TKey }\n  ) => string;\n  closeModal: (id: string) => void;\n  closeContextModal: <TKey extends MantineModal>(id: TKey) => void;\n  closeAllModals: () => void;\n  updateModal: (payload: { modalId: string } & Partial<ModalSettings>) => void;\n  updateContextModal: (payload: { modalId: string } & Partial<OpenContextModal<any>>) => void;\n};\n\nexport const [useModalsEvents, createEvent] =\n  createUseExternalEvents<ModalsEvents>('mantine-modals');\n\nexport const openModal: ModalsEvents['openModal'] = (payload) => {\n  const id = payload.modalId || randomId();\n  createEvent('openModal')({ ...payload, modalId: id });\n  return id;\n};\n\nexport const openConfirmModal: ModalsEvents['openConfirmModal'] = (payload) => {\n  const id = payload.modalId || randomId();\n  createEvent('openConfirmModal')({ ...payload, modalId: id });\n  return id;\n};\n\nexport const openContextModal: ModalsEvents['openContextModal'] = <TKey extends MantineModal>(\n  payload: OpenContextModal<Parameters<MantineModals[TKey]>[0]['innerProps']> & { modal: TKey }\n) => {\n  const id = payload.modalId || randomId();\n  createEvent('openContextModal')({ ...payload, modalId: id });\n  return id;\n};\n\nexport const closeModal = createEvent('closeModal');\n\nexport const closeContextModal: ModalsEvents['closeContextModal'] = <TKey extends MantineModal>(\n  id: TKey\n) => createEvent('closeContextModal')(id);\n\nexport const closeAllModals = createEvent('closeAllModals');\n\nexport const updateModal = (payload: { modalId: string } & Partial<ModalSettings>) =>\n  createEvent('updateModal')(payload);\n\nexport const updateContextModal = (payload: { modalId: string } & Partial<OpenContextModal<any>>) =>\n  createEvent('updateContextModal')(payload);\n\nexport const modals: {\n  open: ModalsEvents['openModal'];\n  close: ModalsEvents['closeModal'];\n  closeAll: ModalsEvents['closeAllModals'];\n  openConfirmModal: ModalsEvents['openConfirmModal'];\n  openContextModal: ModalsEvents['openContextModal'];\n  updateModal: ModalsEvents['updateModal'];\n  updateContextModal: ModalsEvents['updateContextModal'];\n} = {\n  open: openModal,\n  close: closeModal,\n  closeAll: closeAllModals,\n  openConfirmModal,\n  openContextModal,\n  updateModal,\n  updateContextModal,\n};\n", "import { ModalSettings, ModalState, OpenContextModal } from './context';\n\ninterface ModalsState {\n  modals: ModalState[];\n\n  /**\n   * Modal that is currently open or was the last open one.\n   * Keeping the last one is necessary for providing a clean exit transition.\n   */\n  current: ModalState | null;\n}\n\ninterface OpenAction {\n  type: 'OPEN';\n  modal: ModalState;\n}\n\ninterface CloseAction {\n  type: 'CLOSE';\n  modalId: string;\n  canceled?: boolean;\n}\n\ninterface CloseAllAction {\n  type: 'CLOSE_ALL';\n  canceled?: boolean;\n}\n\ninterface UpdateAction {\n  type: 'UPDATE';\n  modalId: string;\n  newProps: Partial<ModalSettings>;\n}\n\nfunction handleCloseModal(modal: ModalState, canceled?: boolean) {\n  if (canceled && modal.type === 'confirm') {\n    modal.props.onCancel?.();\n  }\n\n  modal.props.onClose?.();\n}\n\nexport function modalsReducer(\n  state: ModalsState,\n  action: OpenAction | CloseAction | CloseAllAction | UpdateAction\n): ModalsState {\n  switch (action.type) {\n    case 'OPEN': {\n      return {\n        current: action.modal,\n        modals: [...state.modals, action.modal],\n      };\n    }\n    case 'CLOSE': {\n      const modal = state.modals.find((m) => m.id === action.modalId);\n      if (!modal) {\n        return state;\n      }\n\n      handleCloseModal(modal, action.canceled);\n\n      const remainingModals = state.modals.filter((m) => m.id !== action.modalId);\n\n      return {\n        current: remainingModals[remainingModals.length - 1] || state.current,\n        modals: remainingModals,\n      };\n    }\n    case 'CLOSE_ALL': {\n      if (!state.modals.length) {\n        return state;\n      }\n\n      // Resolve modal stack from top to bottom\n      state.modals\n        .concat()\n        .reverse()\n        .forEach((modal) => {\n          handleCloseModal(modal, action.canceled);\n        });\n\n      return {\n        current: state.current,\n        modals: [],\n      };\n    }\n    case 'UPDATE': {\n      const { modalId, newProps } = action;\n\n      const updatedModals = state.modals.map((modal) => {\n        if (modal.id !== modalId) {\n          return modal;\n        }\n\n        if (modal.type === 'content' || modal.type === 'confirm') {\n          return {\n            ...modal,\n            props: {\n              ...modal.props,\n              ...newProps,\n            },\n          };\n        }\n\n        if (modal.type === 'context') {\n          return {\n            ...modal,\n            props: {\n              ...modal.props,\n              ...newProps,\n              innerProps: {\n                ...modal.props.innerProps,\n                ...(newProps as Partial<OpenContextModal<any>>).innerProps,\n              },\n            },\n          };\n        }\n\n        return modal;\n      });\n\n      const currentModal =\n        state.current?.id === modalId\n          ? updatedModals.find((modal) => modal.id === modalId) || state.current\n          : state.current;\n\n      return {\n        ...state,\n        modals: updatedModals,\n        current: currentModal,\n      };\n    }\n    default: {\n      return state;\n    }\n  }\n}\n", "import { useCallback, useReducer, useRef } from 'react';\nimport { getDefaultZIndex, Modal } from '@mantine/core';\nimport { randomId } from '@mantine/hooks';\nimport { ConfirmModal } from './ConfirmModal';\nimport {\n  ConfirmLabels,\n  ContextModalProps,\n  ModalsContext,\n  ModalsContextProps,\n  ModalSettings,\n  OpenConfirmModal,\n  OpenContextModal,\n} from './context';\nimport { useModalsEvents } from './events';\nimport { modalsReducer } from './reducer';\n\nexport interface ModalsProviderProps {\n  /** Your app */\n  children?: React.ReactNode;\n\n  /** Predefined modals */\n  modals?: Record<string, React.FC<ContextModalProps<any>>>;\n\n  /** Shared Modal component props, applied for every modal */\n  modalProps?: ModalSettings;\n\n  /** Confirm modal labels */\n  labels?: ConfirmLabels;\n}\n\nfunction separateConfirmModalProps(props: OpenConfirmModal) {\n  if (!props) {\n    return { confirmProps: {}, modalProps: {} };\n  }\n\n  const {\n    id,\n    children,\n    onCancel,\n    onConfirm,\n    closeOnConfirm,\n    closeOnCancel,\n    cancelProps,\n    confirmProps,\n    groupProps,\n    labels,\n    ...others\n  } = props;\n\n  return {\n    confirmProps: {\n      id,\n      children,\n      onCancel,\n      onConfirm,\n      closeOnConfirm,\n      closeOnCancel,\n      cancelProps,\n      confirmProps,\n      groupProps,\n      labels,\n    },\n    modalProps: {\n      id,\n      ...others,\n    },\n  };\n}\n\nexport function ModalsProvider({ children, modalProps, labels, modals }: ModalsProviderProps) {\n  const [state, dispatch] = useReducer(modalsReducer, { modals: [], current: null });\n  const stateRef = useRef(state);\n  stateRef.current = state;\n\n  const closeAll = useCallback(\n    (canceled?: boolean) => {\n      dispatch({ type: 'CLOSE_ALL', canceled });\n    },\n    [stateRef, dispatch]\n  );\n\n  const openModal = useCallback(\n    ({ modalId, ...props }: ModalSettings) => {\n      const id = modalId || randomId();\n\n      dispatch({\n        type: 'OPEN',\n        modal: {\n          id,\n          type: 'content',\n          props,\n        },\n      });\n      return id;\n    },\n    [dispatch]\n  );\n\n  const openConfirmModal = useCallback(\n    ({ modalId, ...props }: OpenConfirmModal) => {\n      const id = modalId || randomId();\n      dispatch({\n        type: 'OPEN',\n        modal: {\n          id,\n          type: 'confirm',\n          props,\n        },\n      });\n      return id;\n    },\n    [dispatch]\n  );\n\n  const openContextModal = useCallback(\n    (modal: string, { modalId, ...props }: OpenContextModal) => {\n      const id = modalId || randomId();\n      dispatch({\n        type: 'OPEN',\n        modal: {\n          id,\n          type: 'context',\n          props,\n          ctx: modal,\n        },\n      });\n      return id;\n    },\n    [dispatch]\n  );\n\n  const closeModal = useCallback(\n    (id: string, canceled?: boolean) => {\n      dispatch({ type: 'CLOSE', modalId: id, canceled });\n    },\n    [stateRef, dispatch]\n  );\n\n  const updateModal = useCallback(\n    ({ modalId, ...newProps }: Partial<ModalSettings> & { modalId: string }) => {\n      dispatch({\n        type: 'UPDATE',\n        modalId,\n        newProps,\n      });\n    },\n    [dispatch]\n  );\n\n  const updateContextModal = useCallback(\n    ({ modalId, ...newProps }: { modalId: string } & Partial<OpenContextModal<any>>) => {\n      dispatch({ type: 'UPDATE', modalId, newProps });\n    },\n    [dispatch]\n  );\n\n  useModalsEvents({\n    openModal,\n    openConfirmModal,\n    openContextModal: ({ modal, ...payload }: any) => openContextModal(modal, payload),\n    closeModal,\n    closeContextModal: closeModal,\n    closeAllModals: closeAll,\n    updateModal,\n    updateContextModal,\n  });\n\n  const ctx: ModalsContextProps = {\n    modalProps: modalProps || {},\n    modals: state.modals,\n    openModal,\n    openConfirmModal,\n    openContextModal,\n    closeModal,\n    closeContextModal: closeModal,\n    closeAll,\n    updateModal,\n    updateContextModal,\n  };\n\n  const getCurrentModal = () => {\n    const currentModal = stateRef.current.current;\n    switch (currentModal?.type) {\n      case 'context': {\n        const { innerProps, ...rest } = currentModal.props;\n        const ContextModal = modals![currentModal.ctx];\n\n        return {\n          modalProps: rest,\n          content: <ContextModal innerProps={innerProps} context={ctx} id={currentModal.id} />,\n        };\n      }\n      case 'confirm': {\n        const { modalProps: separatedModalProps, confirmProps: separatedConfirmProps } =\n          separateConfirmModalProps(currentModal.props);\n\n        return {\n          modalProps: separatedModalProps,\n          content: (\n            <ConfirmModal\n              {...separatedConfirmProps}\n              id={currentModal.id}\n              labels={currentModal.props.labels || labels}\n            />\n          ),\n        };\n      }\n      case 'content': {\n        const { children: currentModalChildren, ...rest } = currentModal.props;\n\n        return {\n          modalProps: rest,\n          content: currentModalChildren,\n        };\n      }\n      default: {\n        return {\n          modalProps: {},\n          content: null,\n        };\n      }\n    }\n  };\n\n  const { modalProps: currentModalProps, content } = getCurrentModal();\n\n  return (\n    <ModalsContext.Provider value={ctx}>\n      <Modal\n        zIndex={getDefaultZIndex('modal') + 1}\n        {...modalProps}\n        {...currentModalProps}\n        opened={state.modals.length > 0}\n        onClose={() => closeModal(state.current?.id as any)}\n      >\n        {content}\n      </Modal>\n\n      {children}\n    </ModalsContext.Provider>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDa,IAAA,oBAAgB,4BAAkC,IAAW;AAC1E,cAAc,cAAc;;;ACrDrB,SAAS,YAAY;AACpB,QAAA,UAAM,0BAAW,aAAa;AAEpC,MAAI,CAAC,KAAK;AACR,UAAM,IAAI;MACR;IACF;EAAA;AAGK,SAAA;AACT;;;ACQO,SAAS,aAAa;EAC3B;EACA;EACA;EACA,SAAS,EAAE,QAAQ,IAAI,SAAS,GAAG;EACnC,iBAAiB;EACjB,gBAAgB;EAChB;EACA;EACA;EACA;AACF,GAAsB;AACpB,QAAM,EAAE,QAAQ,aAAa,SAAS,aAAiB,IAAA;AACvD,QAAM,MAAM,UAAU;AAEhB,QAAA,eAAe,CAAC,UAA+C;AACnE,YAAO,2CAAa,aAAY,eAAc,2CAAa,QAAQ;AAC5D,WAAA,aAAa,cAAc,SAAS;AAC1B,qBAAA,IAAI,WAAW,EAAG;EACrC;AAEM,QAAA,gBAAgB,CAAC,UAA+C;AACpE,YAAO,6CAAc,aAAY,eAAc,6CAAc,QAAQ;AAC9D,WAAA,cAAc,cAAc,UAAU;AAC3B,sBAAA,IAAI,WAAW,EAAG;EACtC;AAEA,aAEK,yBAAA,6BAAA,EAAA,UAAA;IAAA,gBAAa,wBAAA,KAAA,EAAI,IAAG,MAAM,SAAS,CAAA;QAEpC,yBAAC,OAAA,EAAM,IAAI,WAAW,IAAI,MAAM,SAAQ,YAAY,GAAG,YACrD,UAAA;UAAC,wBAAA,QAAA,EAAO,SAAQ,WAAW,GAAG,aAAa,SAAS,cACjD,WAAa,2CAAA,aAAY,YAC5B,CAAA;UAEA,wBAAC,QAAA,EAAQ,GAAG,cAAc,SAAS,eAChC,WAAA,6CAAc,aAAY,aAC7B,CAAA;IAAA,EACF,CAAA;EAAA,EACF,CAAA;AAEJ;;;ACxCO,IAAM,CAAC,iBAAiB,WAAW,IACxC,wBAAsC,gBAAgB;AAE3C,IAAA,YAAuC,CAAC,YAAY;AACzD,QAAA,KAAK,QAAQ,WAAW,SAAS;AACvC,cAAY,WAAW,EAAE,EAAE,GAAG,SAAS,SAAS,GAAA,CAAI;AAC7C,SAAA;AACT;AAEa,IAAA,mBAAqD,CAAC,YAAY;AACvE,QAAA,KAAK,QAAQ,WAAW,SAAS;AACvC,cAAY,kBAAkB,EAAE,EAAE,GAAG,SAAS,SAAS,GAAA,CAAI;AACpD,SAAA;AACT;AAEa,IAAA,mBAAqD,CAChE,YACG;AACG,QAAA,KAAK,QAAQ,WAAW,SAAS;AACvC,cAAY,kBAAkB,EAAE,EAAE,GAAG,SAAS,SAAS,GAAA,CAAI;AACpD,SAAA;AACT;AAEa,IAAA,aAAa,YAAY,YAAY;AAMrC,IAAA,iBAAiB,YAAY,gBAAgB;AAEnD,IAAM,cAAc,CAAC,YAC1B,YAAY,aAAa,EAAE,OAAO;AAE7B,IAAM,qBAAqB,CAAC,YACjC,YAAY,oBAAoB,EAAE,OAAO;AAEpC,IAAM,SAQT;EACF,MAAM;EACN,OAAO;EACP,UAAU;EACV;EACA;EACA;EACA;AACF;;;AC1CA,SAAS,iBAAiB,OAAmB,UAAoB;;AAC3D,MAAA,YAAY,MAAM,SAAS,WAAW;AACxC,sBAAM,OAAM,aAAZ;EAAuB;AAGzB,oBAAM,OAAM,YAAZ;AACF;AAEgB,SAAA,cACd,OACA,QACa;;AACb,UAAQ,OAAO,MAAM;IACnB,KAAK,QAAQ;AACJ,aAAA;QACL,SAAS,OAAO;QAChB,QAAQ,CAAC,GAAG,MAAM,QAAQ,OAAO,KAAK;MACxC;IAAA;IAEF,KAAK,SAAS;AACN,YAAA,QAAQ,MAAM,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,OAAO;AAC9D,UAAI,CAAC,OAAO;AACH,eAAA;MAAA;AAGQ,uBAAA,OAAO,OAAO,QAAQ;AAEjC,YAAA,kBAAkB,MAAM,OAAO,OAAO,CAAC,MAAM,EAAE,OAAO,OAAO,OAAO;AAEnE,aAAA;QACL,SAAS,gBAAgB,gBAAgB,SAAS,CAAC,KAAK,MAAM;QAC9D,QAAQ;MACV;IAAA;IAEF,KAAK,aAAa;AACZ,UAAA,CAAC,MAAM,OAAO,QAAQ;AACjB,eAAA;MAAA;AAIT,YAAM,OACH,OAAO,EACP,QAAA,EACA,QAAQ,CAAC,UAAU;AACD,yBAAA,OAAO,OAAO,QAAQ;MAAA,CACxC;AAEI,aAAA;QACL,SAAS,MAAM;QACf,QAAQ,CAAA;MACV;IAAA;IAEF,KAAK,UAAU;AACP,YAAA,EAAE,SAAS,SAAA,IAAa;AAE9B,YAAM,gBAAgB,MAAM,OAAO,IAAI,CAAC,UAAU;AAC5C,YAAA,MAAM,OAAO,SAAS;AACjB,iBAAA;QAAA;AAGT,YAAI,MAAM,SAAS,aAAa,MAAM,SAAS,WAAW;AACjD,iBAAA;YACL,GAAG;YACH,OAAO;cACL,GAAG,MAAM;cACT,GAAG;YAAA;UAEP;QAAA;AAGE,YAAA,MAAM,SAAS,WAAW;AACrB,iBAAA;YACL,GAAG;YACH,OAAO;cACL,GAAG,MAAM;cACT,GAAG;cACH,YAAY;gBACV,GAAG,MAAM,MAAM;gBACf,GAAI,SAA4C;cAAA;YAClD;UAEJ;QAAA;AAGK,eAAA;MAAA,CACR;AAED,YAAM,iBACJ,WAAM,YAAN,mBAAe,QAAO,UAClB,cAAc,KAAK,CAAC,UAAU,MAAM,OAAO,OAAO,KAAK,MAAM,UAC7D,MAAM;AAEL,aAAA;QACL,GAAG;QACH,QAAQ;QACR,SAAS;MACX;IAAA;IAEF,SAAS;AACA,aAAA;IAAA;EACT;AAEJ;;;AC1GA,SAAS,0BAA0B,OAAyB;AAC1D,MAAI,CAAC,OAAO;AACV,WAAO,EAAE,cAAc,CAAA,GAAI,YAAY,CAAA,EAAG;EAAA;AAGtC,QAAA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EAAA,IACD;AAEG,SAAA;IACL,cAAc;MACZ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;IACA,YAAY;MACV;MACA,GAAG;IAAA;EAEP;AACF;AAEO,SAAS,eAAe,EAAE,UAAU,YAAY,QAAQ,QAAAA,QAAA,GAA+B;AAC5F,QAAM,CAAC,OAAO,QAAQ,QAAI,0BAAW,eAAe,EAAE,QAAQ,CAAC,GAAG,SAAS,KAAA,CAAM;AAC3E,QAAA,eAAW,sBAAO,KAAK;AAC7B,WAAS,UAAU;AAEnB,QAAM,eAAW;IACf,CAAC,aAAuB;AACtB,eAAS,EAAE,MAAM,aAAa,SAAA,CAAU;IAC1C;IACA,CAAC,UAAU,QAAQ;EACrB;AAEA,QAAMC,iBAAY;IAChB,CAAC,EAAE,SAAS,GAAG,MAAA,MAA2B;AAClC,YAAA,KAAK,WAAW,SAAS;AAEtB,eAAA;QACP,MAAM;QACN,OAAO;UACL;UACA,MAAM;UACN;QAAA;MACF,CACD;AACM,aAAA;IACT;IACA,CAAC,QAAQ;EACX;AAEA,QAAMC,wBAAmB;IACvB,CAAC,EAAE,SAAS,GAAG,MAAA,MAA8B;AACrC,YAAA,KAAK,WAAW,SAAS;AACtB,eAAA;QACP,MAAM;QACN,OAAO;UACL;UACA,MAAM;UACN;QAAA;MACF,CACD;AACM,aAAA;IACT;IACA,CAAC,QAAQ;EACX;AAEA,QAAMC,wBAAmB;IACvB,CAAC,OAAe,EAAE,SAAS,GAAG,MAAA,MAA8B;AACpD,YAAA,KAAK,WAAW,SAAS;AACtB,eAAA;QACP,MAAM;QACN,OAAO;UACL;UACA,MAAM;UACN;UACA,KAAK;QAAA;MACP,CACD;AACM,aAAA;IACT;IACA,CAAC,QAAQ;EACX;AAEA,QAAMC,kBAAa;IACjB,CAAC,IAAY,aAAuB;AAClC,eAAS,EAAE,MAAM,SAAS,SAAS,IAAI,SAAA,CAAU;IACnD;IACA,CAAC,UAAU,QAAQ;EACrB;AAEA,QAAMC,mBAAc;IAClB,CAAC,EAAE,SAAS,GAAG,SAAA,MAA6D;AACjE,eAAA;QACP,MAAM;QACN;QACA;MAAA,CACD;IACH;IACA,CAAC,QAAQ;EACX;AAEA,QAAMC,0BAAqB;IACzB,CAAC,EAAE,SAAS,GAAG,SAAA,MAAqE;AAClF,eAAS,EAAE,MAAM,UAAU,SAAS,SAAA,CAAU;IAChD;IACA,CAAC,QAAQ;EACX;AAEgB,kBAAA;IACd,WAAAL;IACA,kBAAAC;IACA,kBAAkB,CAAC,EAAE,OAAO,GAAG,QAAQ,MAAWC,kBAAiB,OAAO,OAAO;IACjF,YAAAC;IACA,mBAAmBA;IACnB,gBAAgB;IAChB,aAAAC;IACA,oBAAAC;EAAA,CACD;AAED,QAAM,MAA0B;IAC9B,YAAY,cAAc,CAAC;IAC3B,QAAQ,MAAM;IACd,WAAAL;IACA,kBAAAC;IACA,kBAAAC;IACA,YAAAC;IACA,mBAAmBA;IACnB;IACA,aAAAC;IACA,oBAAAC;EACF;AAEA,QAAM,kBAAkB,MAAM;AACtB,UAAA,eAAe,SAAS,QAAQ;AACtC,YAAQ,6CAAc,MAAM;MAC1B,KAAK,WAAW;AACd,cAAM,EAAE,YAAY,GAAG,KAAA,IAAS,aAAa;AACvC,cAAA,eAAeN,QAAQ,aAAa,GAAG;AAEtC,eAAA;UACL,YAAY;UACZ,aAAA,yBAAU,cAAa,EAAA,YAAwB,SAAS,KAAK,IAAI,aAAa,GAAI,CAAA;QACpF;MAAA;MAEF,KAAK,WAAW;AACR,cAAA,EAAE,YAAY,qBAAqB,cAAc,sBAAA,IACrD,0BAA0B,aAAa,KAAK;AAEvC,eAAA;UACL,YAAY;UACZ,aACE;YAAC;YAAA;cACE,GAAG;cACJ,IAAI,aAAa;cACjB,QAAQ,aAAa,MAAM,UAAU;YAAA;UAAA;QAG3C;MAAA;MAEF,KAAK,WAAW;AACd,cAAM,EAAE,UAAU,sBAAsB,GAAG,KAAA,IAAS,aAAa;AAE1D,eAAA;UACL,YAAY;UACZ,SAAS;QACX;MAAA;MAEF,SAAS;AACA,eAAA;UACL,YAAY,CAAC;UACb,SAAS;QACX;MAAA;IACF;EAEJ;AAEA,QAAM,EAAE,YAAY,mBAAmB,QAAA,IAAY,gBAAgB;AAEnE,aACG,0BAAA,cAAc,UAAd,EAAuB,OAAO,KAC7B,UAAA;QAAA;MAAC;MAAA;QACC,QAAQ,iBAAiB,OAAO,IAAI;QACnC,GAAG;QACH,GAAG;QACJ,QAAQ,MAAM,OAAO,SAAS;QAC9B,SAAS,MAAA;;AAAM,iBAAAI,aAAW,WAAM,YAAN,mBAAe,EAAS;;QAEjD,UAAA;MAAA;IACH;IAEC;EAAA,EACH,CAAA;AAEJ;", "names": ["modals", "openModal", "openConfirmModal", "openContextModal", "closeModal", "updateModal", "updateContextModal"]}