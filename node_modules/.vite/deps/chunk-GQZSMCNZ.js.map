{"version": 3, "sources": ["../../@mantine/hooks/src/utils/clamp/clamp.ts", "../../@mantine/hooks/src/utils/lower-first/lower-first.ts", "../../@mantine/hooks/src/utils/random-id/random-id.ts", "../../@mantine/hooks/src/utils/range/range.ts", "../../@mantine/hooks/src/utils/shallow-equal/shallow-equal.ts", "../../@mantine/hooks/src/utils/upper-first/upper-first.ts", "../../@mantine/hooks/src/use-callback-ref/use-callback-ref.ts", "../../@mantine/hooks/src/use-debounced-callback/use-debounced-callback.ts", "../../@mantine/hooks/src/use-click-outside/use-click-outside.ts", "../../@mantine/hooks/src/use-clipboard/use-clipboard.ts", "../../@mantine/hooks/src/use-media-query/use-media-query.ts", "../../@mantine/hooks/src/use-color-scheme/use-color-scheme.ts", "../../@mantine/hooks/src/use-counter/use-counter.ts", "../../@mantine/hooks/src/use-debounced-state/use-debounced-state.ts", "../../@mantine/hooks/src/use-debounced-value/use-debounced-value.ts", "../../@mantine/hooks/src/use-isomorphic-effect/use-isomorphic-effect.ts", "../../@mantine/hooks/src/use-document-title/use-document-title.ts", "../../@mantine/hooks/src/use-document-visibility/use-document-visibility.ts", "../../@mantine/hooks/src/use-did-update/use-did-update.ts", "../../@mantine/hooks/src/use-focus-return/use-focus-return.ts", "../../@mantine/hooks/src/use-focus-trap/tabbable.ts", "../../@mantine/hooks/src/use-focus-trap/scope-tab.ts", "../../@mantine/hooks/src/use-focus-trap/use-focus-trap.ts", "../../@mantine/hooks/src/use-force-update/use-force-update.ts", "../../@mantine/hooks/src/use-id/use-react-id.ts", "../../@mantine/hooks/src/use-id/use-id.ts", "../../@mantine/hooks/src/use-idle/use-idle.ts", "../../@mantine/hooks/src/use-interval/use-interval.ts", "../../@mantine/hooks/src/use-list-state/use-list-state.ts", "../../@mantine/hooks/src/use-window-event/use-window-event.ts", "../../@mantine/hooks/src/use-local-storage/create-storage.ts", "../../@mantine/hooks/src/use-local-storage/use-local-storage.ts", "../../@mantine/hooks/src/use-session-storage/use-session-storage.ts", "../../@mantine/hooks/src/use-merged-ref/use-merged-ref.ts", "../../@mantine/hooks/src/use-mouse/use-mouse.ts", "../../@mantine/hooks/src/use-move/use-move.ts", "../../@mantine/hooks/src/use-uncontrolled/use-uncontrolled.ts", "../../@mantine/hooks/src/use-pagination/use-pagination.ts", "../../@mantine/hooks/src/use-queue/use-queue.ts", "../../@mantine/hooks/src/use-page-leave/use-page-leave.ts", "../../@mantine/hooks/src/use-reduced-motion/use-reduced-motion.ts", "../../@mantine/hooks/src/use-scroll-into-view/utils/ease-in-out-quad.ts", "../../@mantine/hooks/src/use-scroll-into-view/utils/get-relative-position.ts", "../../@mantine/hooks/src/use-scroll-into-view/utils/get-scroll-start.ts", "../../@mantine/hooks/src/use-scroll-into-view/utils/set-scroll-param.ts", "../../@mantine/hooks/src/use-scroll-into-view/use-scroll-into-view.ts", "../../@mantine/hooks/src/use-resize-observer/use-resize-observer.ts", "../../@mantine/hooks/src/use-shallow-effect/use-shallow-effect.ts", "../../@mantine/hooks/src/use-toggle/use-toggle.ts", "../../@mantine/hooks/src/use-viewport-size/use-viewport-size.ts", "../../@mantine/hooks/src/use-window-scroll/use-window-scroll.ts", "../../@mantine/hooks/src/use-intersection/use-intersection.ts", "../../@mantine/hooks/src/use-hash/use-hash.ts", "../../@mantine/hooks/src/use-hotkeys/parse-hotkey.ts", "../../@mantine/hooks/src/use-hotkeys/use-hotkeys.ts", "../../@mantine/hooks/src/use-fullscreen/use-fullscreen.ts", "../../@mantine/hooks/src/use-logger/use-logger.ts", "../../@mantine/hooks/src/use-hover/use-hover.ts", "../../@mantine/hooks/src/use-validated-state/use-validated-state.ts", "../../@mantine/hooks/src/use-os/use-os.ts", "../../@mantine/hooks/src/use-set-state/use-set-state.ts", "../../@mantine/hooks/src/use-input-state/use-input-state.ts", "../../@mantine/hooks/src/use-event-listener/use-event-listener.ts", "../../@mantine/hooks/src/use-disclosure/use-disclosure.ts", "../../@mantine/hooks/src/use-focus-within/use-focus-within.ts", "../../@mantine/hooks/src/use-network/use-network.ts", "../../@mantine/hooks/src/use-timeout/use-timeout.ts", "../../@mantine/hooks/src/use-text-selection/use-text-selection.ts", "../../@mantine/hooks/src/use-previous/use-previous.ts", "../../@mantine/hooks/src/use-favicon/use-favicon.ts", "../../@mantine/hooks/src/use-headroom/use-headroom.ts", "../../@mantine/hooks/src/use-eye-dropper/use-eye-dropper.ts", "../../@mantine/hooks/src/use-in-viewport/use-in-viewport.ts", "../../@mantine/hooks/src/use-mutation-observer/use-mutation-observer.ts", "../../@mantine/hooks/src/use-mounted/use-mounted.ts", "../../@mantine/hooks/src/use-state-history/use-state-history.ts", "../../@mantine/hooks/src/use-map/use-map.ts", "../../@mantine/hooks/src/use-set/use-set.ts", "../../@mantine/hooks/src/use-throttled-callback/use-throttled-callback.ts", "../../@mantine/hooks/src/use-throttled-state/use-throttled-state.ts", "../../@mantine/hooks/src/use-throttled-value/use-throttled-value.ts", "../../@mantine/hooks/src/use-is-first-render/use-is-first-render.ts", "../../@mantine/hooks/src/use-orientation/use-orientation.ts", "../../@mantine/hooks/src/use-fetch/use-fetch.ts", "../../@mantine/hooks/src/use-radial-move/use-radial-move.ts", "../../@mantine/hooks/src/use-scroll-spy/use-scroll-spy.ts", "../../@mantine/hooks/src/use-file-dialog/use-file-dialog.ts"], "sourcesContent": ["export function clamp(value: number, min: number | undefined, max: number | undefined) {\n  if (min === undefined && max === undefined) {\n    return value;\n  }\n\n  if (min !== undefined && max === undefined) {\n    return Math.max(value, min);\n  }\n\n  if (min === undefined && max !== undefined) {\n    return Math.min(value, max);\n  }\n\n  return Math.min(Math.max(value, min!), max!);\n}\n", "export function lowerFirst(value: string) {\n  return typeof value !== 'string' ? '' : value.charAt(0).toLowerCase() + value.slice(1);\n}\n", "export function randomId(prefix = 'mantine-'): string {\n  return `${prefix}${Math.random().toString(36).slice(2, 11)}`;\n}\n", "export function range(start: number, end: number) {\n  const length = Math.abs(end - start) + 1;\n  const reversed = start > end;\n\n  if (!reversed) {\n    return Array.from({ length }, (_, index) => index + start);\n  }\n\n  return Array.from({ length }, (_, index) => start - index);\n}\n", "export function shallowEqual(a: any, b: any) {\n  if (a === b) {\n    return true;\n  }\n\n  if (Number.isNaN(a) && Number.isNaN(b)) {\n    return true;\n  }\n\n  if (!(a instanceof Object) || !(b instanceof Object)) {\n    return false;\n  }\n\n  const keys = Object.keys(a);\n  const { length } = keys;\n\n  if (length !== Object.keys(b).length) {\n    return false;\n  }\n\n  for (let i = 0; i < length; i += 1) {\n    const key = keys[i];\n\n    if (!(key in b)) {\n      return false;\n    }\n\n    if (a[key] !== b[key] && !(Number.isNaN(a[key]) && Number.isNaN(b[key]))) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "export function upperFirst(value: string) {\n  return typeof value !== 'string' ? '' : value.charAt(0).toUpperCase() + value.slice(1);\n}\n", "import { useEffect, useMemo, useRef } from 'react';\n\nexport function useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = useRef(callback);\n\n  useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  return useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n", "import { useCallback, useEffect, useRef } from 'react';\nimport { useCallbackRef } from '../use-callback-ref/use-callback-ref';\n\nexport function useDebouncedCallback<T extends (...args: any[]) => any>(\n  callback: T,\n  options: number | { delay: number; flushOnUnmount?: boolean }\n) {\n  const delay = typeof options === 'number' ? options : options.delay;\n  const flushOnUnmount = typeof options === 'number' ? false : options.flushOnUnmount;\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = useRef(0);\n  const flushRef = useRef(() => {});\n\n  const lastCallback = Object.assign(\n    useCallback(\n      (...args: Parameters<T>) => {\n        window.clearTimeout(debounceTimerRef.current);\n        const flush = () => {\n          if (debounceTimerRef.current !== 0) {\n            debounceTimerRef.current = 0;\n            handleCallback(...args);\n          }\n        };\n        flushRef.current = flush;\n        lastCallback.flush = flush;\n        debounceTimerRef.current = window.setTimeout(flush, delay);\n      },\n      [handleCallback, delay]\n    ),\n    { flush: flushRef.current }\n  );\n\n  useEffect(\n    () => () => {\n      window.clearTimeout(debounceTimerRef.current);\n      if (flushOnUnmount) {\n        lastCallback.flush();\n      }\n    },\n    [lastCallback, flushOnUnmount]\n  );\n\n  return lastCallback;\n}\n", "import { useEffect, useRef } from 'react';\n\nconst DEFAULT_EVENTS = ['mousedown', 'touchstart'];\n\nexport function useClickOutside<T extends HTMLElement = any>(\n  handler: () => void,\n  events?: string[] | null,\n  nodes?: (HTMLElement | null)[]\n) {\n  const ref = useRef<T>(null);\n\n  useEffect(() => {\n    const listener = (event: any) => {\n      const { target } = event ?? {};\n      if (Array.isArray(nodes)) {\n        const shouldIgnore =\n          target?.hasAttribute('data-ignore-outside-clicks') ||\n          (!document.body.contains(target) && target.tagName !== 'HTML');\n        const shouldTrigger = nodes.every((node) => !!node && !event.composedPath().includes(node));\n        shouldTrigger && !shouldIgnore && handler();\n      } else if (ref.current && !ref.current.contains(target)) {\n        handler();\n      }\n    };\n\n    (events || DEFAULT_EVENTS).forEach((fn) => document.addEventListener(fn, listener));\n\n    return () => {\n      (events || DEFAULT_EVENTS).forEach((fn) => document.removeEventListener(fn, listener));\n    };\n  }, [ref, handler, nodes]);\n\n  return ref;\n}\n", "import { useState } from 'react';\n\nexport function useClipboard({ timeout = 2000 } = {}) {\n  const [error, setError] = useState<Error | null>(null);\n  const [copied, setCopied] = useState(false);\n  const [copyTimeout, setCopyTimeout] = useState<number | null>(null);\n\n  const handleCopyResult = (value: boolean) => {\n    window.clearTimeout(copyTimeout!);\n    setCopyTimeout(window.setTimeout(() => setCopied(false), timeout));\n    setCopied(value);\n  };\n\n  const copy = (valueToCopy: any) => {\n    if ('clipboard' in navigator) {\n      navigator.clipboard\n        .writeText(valueToCopy)\n        .then(() => handleCopyResult(true))\n        .catch((err) => setError(err));\n    } else {\n      setError(new Error('useClipboard: navigator.clipboard is not supported'));\n    }\n  };\n\n  const reset = () => {\n    setCopied(false);\n    setError(null);\n    window.clearTimeout(copyTimeout!);\n  };\n\n  return { copy, reset, error, copied };\n}\n", "import { useEffect, useRef, useState } from 'react';\n\nexport interface UseMediaQueryOptions {\n  getInitialValueInEffect: boolean;\n}\n\ntype MediaQueryCallback = (event: { matches: boolean; media: string }) => void;\n\n/**\n * Older versions of Safari (shipped withCatalina and before) do not support addEventListener on matchMedia\n * https://stackoverflow.com/questions/56466261/matchmedia-addlistener-marked-as-deprecated-addeventlistener-equivalent\n * */\nfunction attachMediaListener(query: MediaQueryList, callback: MediaQueryCallback) {\n  try {\n    query.addEventListener('change', callback);\n    return () => query.removeEventListener('change', callback);\n  } catch (e) {\n    query.addListener(callback);\n    return () => query.removeListener(callback);\n  }\n}\n\nfunction getInitialValue(query: string, initialValue?: boolean) {\n  if (typeof initialValue === 'boolean') {\n    return initialValue;\n  }\n\n  if (typeof window !== 'undefined' && 'matchMedia' in window) {\n    return window.matchMedia(query).matches;\n  }\n\n  return false;\n}\n\nexport function useMediaQuery(\n  query: string,\n  initialValue?: boolean,\n  { getInitialValueInEffect }: UseMediaQueryOptions = {\n    getInitialValueInEffect: true,\n  }\n) {\n  const [matches, setMatches] = useState(\n    getInitialValueInEffect ? initialValue : getInitialValue(query)\n  );\n  const queryRef = useRef<MediaQueryList>(null);\n\n  useEffect(() => {\n    if ('matchMedia' in window) {\n      queryRef.current = window.matchMedia(query);\n      setMatches(queryRef.current.matches);\n      return attachMediaListener(queryRef.current, (event) => setMatches(event.matches));\n    }\n\n    return undefined;\n  }, [query]);\n\n  return matches;\n}\n", "import { useMediaQuery, UseMediaQueryOptions } from '../use-media-query/use-media-query';\n\nexport function useColorScheme(initialValue?: 'dark' | 'light', options?: UseMediaQueryOptions) {\n  return useMediaQuery('(prefers-color-scheme: dark)', initialValue === 'dark', options)\n    ? 'dark'\n    : 'light';\n}\n", "import { useState } from 'react';\nimport { clamp } from '../utils';\n\nconst DEFAULT_OPTIONS = {\n  min: -Infinity,\n  max: Infinity,\n};\n\nexport function useCounter(initialValue = 0, options?: Partial<{ min: number; max: number }>) {\n  const { min, max } = { ...DEFAULT_OPTIONS, ...options };\n  const [count, setCount] = useState<number>(clamp(initialValue, min, max));\n\n  const increment = () => setCount((current) => clamp(current + 1, min, max));\n  const decrement = () => setCount((current) => clamp(current - 1, min, max));\n  const set = (value: number) => setCount(clamp(value, min, max));\n  const reset = () => setCount(clamp(initialValue, min, max));\n\n  return [count, { increment, decrement, set, reset }] as const;\n}\n", "import { SetStateAction, useCallback, useEffect, useRef, useState } from 'react';\n\nexport function useDebouncedState<T = any>(\n  defaultValue: T,\n  wait: number,\n  options = { leading: false }\n) {\n  const [value, setValue] = useState(defaultValue);\n  const timeoutRef = useRef<number | null>(null);\n  const leadingRef = useRef(true);\n\n  const clearTimeout = () => window.clearTimeout(timeoutRef.current!);\n  useEffect(() => clearTimeout, []);\n\n  const debouncedSetValue = useCallback(\n    (newValue: SetStateAction<T>) => {\n      clearTimeout();\n      if (leadingRef.current && options.leading) {\n        setValue(newValue);\n      } else {\n        timeoutRef.current = window.setTimeout(() => {\n          leadingRef.current = true;\n          setValue(newValue);\n        }, wait);\n      }\n      leadingRef.current = false;\n    },\n    [options.leading]\n  );\n\n  return [value, debouncedSetValue] as const;\n}\n", "import { useEffect, useRef, useState } from 'react';\n\nexport function useDebouncedValue<T = any>(value: T, wait: number, options = { leading: false }) {\n  const [_value, setValue] = useState(value);\n  const mountedRef = useRef(false);\n  const timeoutRef = useRef<number | null>(null);\n  const cooldownRef = useRef(false);\n\n  const cancel = () => window.clearTimeout(timeoutRef.current!);\n\n  useEffect(() => {\n    if (mountedRef.current) {\n      if (!cooldownRef.current && options.leading) {\n        cooldownRef.current = true;\n        setValue(value);\n      } else {\n        cancel();\n        timeoutRef.current = window.setTimeout(() => {\n          cooldownRef.current = false;\n          setValue(value);\n        }, wait);\n      }\n    }\n  }, [value, options.leading, wait]);\n\n  useEffect(() => {\n    mountedRef.current = true;\n    return cancel;\n  }, []);\n\n  return [_value, cancel] as const;\n}\n", "import { useEffect, useLayoutEffect } from 'react';\n\n// UseLayoutEffect will show warning if used during ssr, e.g. with Next.js\n// UseIsomorphicEffect removes it by replacing useLayoutEffect with useEffect during ssr\nexport const useIsomorphicEffect = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n", "import { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\n\nexport function useDocumentTitle(title: string) {\n  useIsomorphicEffect(() => {\n    if (typeof title === 'string' && title.trim().length > 0) {\n      document.title = title.trim();\n    }\n  }, [title]);\n}\n", "import { useEffect, useState } from 'react';\n\nexport function useDocumentVisibility(): DocumentVisibilityState {\n  const [documentVisibility, setDocumentVisibility] = useState<DocumentVisibilityState>('visible');\n\n  useEffect(() => {\n    const listener = () => setDocumentVisibility(document.visibilityState);\n    document.addEventListener('visibilitychange', listener);\n    return () => document.removeEventListener('visibilitychange', listener);\n  }, []);\n\n  return documentVisibility;\n}\n", "import { DependencyList, EffectCallback, useEffect, useRef } from 'react';\n\nexport function useDidUpdate(fn: EffectCallback, dependencies?: DependencyList) {\n  const mounted = useRef(false);\n\n  useEffect(\n    () => () => {\n      mounted.current = false;\n    },\n    []\n  );\n\n  useEffect(() => {\n    if (mounted.current) {\n      return fn();\n    }\n\n    mounted.current = true;\n    return undefined;\n  }, dependencies);\n}\n", "import { useRef } from 'react';\nimport { useDidUpdate } from '../use-did-update/use-did-update';\n\ninterface UseFocusReturn {\n  opened: boolean;\n  shouldReturnFocus?: boolean;\n}\n\n/** Returns focus to last active element, used in Modal and Drawer */\nexport function useFocusReturn({ opened, shouldReturnFocus = true }: UseFocusReturn) {\n  const lastActiveElement = useRef<HTMLElement>(null);\n  const returnFocus = () => {\n    if (\n      lastActiveElement.current &&\n      'focus' in lastActiveElement.current &&\n      typeof lastActiveElement.current.focus === 'function'\n    ) {\n      lastActiveElement.current?.focus({ preventScroll: true });\n    }\n  };\n\n  useDidUpdate(() => {\n    let timeout = -1;\n\n    const clearFocusTimeout = (event: KeyboardEvent) => {\n      if (event.key === 'Tab') {\n        window.clearTimeout(timeout);\n      }\n    };\n\n    document.addEventListener('keydown', clearFocusTimeout);\n\n    if (opened) {\n      lastActiveElement.current = document.activeElement as HTMLElement;\n    } else if (shouldReturnFocus) {\n      timeout = window.setTimeout(returnFocus, 10);\n    }\n\n    return () => {\n      window.clearTimeout(timeout);\n      document.removeEventListener('keydown', clearFocusTimeout);\n    };\n  }, [opened, shouldReturnFocus]);\n\n  return returnFocus;\n}\n", "const TABBABLE_NODES = /input|select|textarea|button|object/;\nexport const FOCUS_SELECTOR = 'a, input, select, textarea, button, object, [tabindex]';\n\nfunction hidden(element: HTMLElement) {\n  if (process.env.NODE_ENV === 'test') {\n    return false;\n  }\n\n  return element.style.display === 'none';\n}\n\nfunction visible(element: HTMLElement) {\n  const isHidden =\n    element.getAttribute('aria-hidden') ||\n    element.getAttribute('hidden') ||\n    element.getAttribute('type') === 'hidden';\n\n  if (isHidden) {\n    return false;\n  }\n\n  let parentElement: HTMLElement = element;\n  while (parentElement) {\n    if (parentElement === document.body || parentElement.nodeType === 11) {\n      break;\n    }\n\n    if (hidden(parentElement)) {\n      return false;\n    }\n\n    parentElement = parentElement.parentNode as HTMLElement;\n  }\n\n  return true;\n}\n\nfunction getElementTabIndex(element: HTMLElement) {\n  let tabIndex: string | null | undefined = element.getAttribute('tabindex');\n  if (tabIndex === null) {\n    tabIndex = undefined;\n  }\n  return parseInt(tabIndex as string, 10);\n}\n\nexport function focusable(element: HTMLElement) {\n  const nodeName = element.nodeName.toLowerCase();\n  const isTabIndexNotNaN = !Number.isNaN(getElementTabIndex(element));\n  const res =\n    // @ts-expect-error function accepts any html element but if it is a button, it should not be disabled to trigger the condition\n    (TABBABLE_NODES.test(nodeName) && !element.disabled) ||\n    (element instanceof HTMLAnchorElement ? element.href || isTabIndexNotNaN : isTabIndexNotNaN);\n\n  return res && visible(element);\n}\n\nexport function tabbable(element: HTMLElement) {\n  const tabIndex = getElementTabIndex(element);\n  const isTabIndexNaN = Number.isNaN(tabIndex);\n  return (isTabIndexNaN || tabIndex >= 0) && focusable(element);\n}\n\nexport function findTabbableDescendants(element: HTMLElement): HTMLElement[] {\n  return Array.from(element.querySelectorAll<HTMLElement>(FOCUS_SELECTOR)).filter(tabbable);\n}\n", "import { findTabbableDescendants } from './tabbable';\n\nexport function scopeTab(node: HTMLElement, event: KeyboardEvent) {\n  const tabbable = findTabbableDescendants(node);\n  if (!tabbable.length) {\n    event.preventDefault();\n    return;\n  }\n  const finalTabbable = tabbable[event.shiftKey ? 0 : tabbable.length - 1];\n  const root = node.getRootNode() as unknown as DocumentOrShadowRoot;\n  let leavingFinalTabbable = finalTabbable === root.activeElement || node === root.activeElement;\n\n  const activeElement = root.activeElement as Element;\n  const activeElementIsRadio =\n    activeElement.tagName === 'INPUT' && activeElement.getAttribute('type') === 'radio';\n  if (activeElementIsRadio) {\n    const activeRadioGroup = tabbable.filter(\n      (element) =>\n        element.getAttribute('type') === 'radio' &&\n        element.getAttribute('name') === activeElement.getAttribute('name')\n    );\n    leavingFinalTabbable = activeRadioGroup.includes(finalTabbable);\n  }\n\n  if (!leavingFinalTabbable) {\n    return;\n  }\n\n  event.preventDefault();\n\n  const target = tabbable[event.shiftKey ? tabbable.length - 1 : 0];\n\n  if (target) {\n    target.focus();\n  }\n}\n", "import { useCallback, useEffect, useRef } from 'react';\nimport { scopeTab } from './scope-tab';\nimport { FOCUS_SELECTOR, focusable, tabbable } from './tabbable';\n\nexport function useFocusTrap(active = true): (instance: HTMLElement | null) => void {\n  const ref = useRef<HTMLElement>(null);\n\n  const focusNode = (node: HTMLElement) => {\n    let focusElement: HTMLElement | null = node.querySelector('[data-autofocus]');\n\n    if (!focusElement) {\n      const children = Array.from<HTMLElement>(node.querySelectorAll(FOCUS_SELECTOR));\n      focusElement = children.find(tabbable) || children.find(focusable) || null;\n      if (!focusElement && focusable(node)) {\n        focusElement = node;\n      }\n    }\n\n    if (focusElement) {\n      focusElement.focus({ preventScroll: true });\n    } else if (process.env.NODE_ENV === 'development') {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[@mantine/hooks/use-focus-trap] Failed to find focusable element within provided node',\n        node\n      );\n    }\n  };\n\n  const setRef = useCallback(\n    (node: HTMLElement | null) => {\n      if (!active) {\n        return;\n      }\n\n      if (node === null) {\n        return;\n      }\n\n      if (ref.current === node) {\n        return;\n      }\n\n      if (node) {\n        // Delay processing the HTML node by a frame. This ensures focus is assigned correctly.\n        setTimeout(() => {\n          if (node.getRootNode()) {\n            focusNode(node);\n          } else if (process.env.NODE_ENV === 'development') {\n            // eslint-disable-next-line no-console\n            console.warn('[@mantine/hooks/use-focus-trap] Ref node is not part of the dom', node);\n          }\n        });\n\n        ref.current = node;\n      } else {\n        ref.current = null;\n      }\n    },\n    [active]\n  );\n\n  useEffect(() => {\n    if (!active) {\n      return undefined;\n    }\n\n    ref.current && setTimeout(() => focusNode(ref.current!));\n\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Tab' && ref.current) {\n        scopeTab(ref.current, event);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [active]);\n\n  return setRef;\n}\n", "import { useReducer } from 'react';\n\nconst reducer = (value: number) => (value + 1) % 1000000;\n\nexport function useForceUpdate(): () => void {\n  const [, update] = useReducer(reducer, 0);\n  return update;\n}\n", "import React from 'react';\n\nconst __useId: () => string | undefined = (React as any)['useId'.toString()] || (() => undefined);\n\nexport function useReactId() {\n  const id = __useId();\n  return id ? `mantine-${id.replace(/:/g, '')}` : '';\n}\n", "import { useState } from 'react';\nimport { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\nimport { randomId } from '../utils';\nimport { useReactId } from './use-react-id';\n\nexport function useId(staticId?: string) {\n  const reactId = useReactId();\n  const [uuid, setUuid] = useState(reactId);\n\n  useIsomorphicEffect(() => {\n    setUuid(randomId());\n  }, []);\n\n  if (typeof staticId === 'string') {\n    return staticId;\n  }\n\n  if (typeof window === 'undefined') {\n    return reactId;\n  }\n\n  return uuid;\n}\n", "import { useEffect, useRef, useState } from 'react';\n\nconst DEFAULT_EVENTS: (keyof DocumentEventMap)[] = [\n  'keydown',\n  'mousemove',\n  'touchmove',\n  'click',\n  'scroll',\n  'wheel',\n];\nconst DEFAULT_OPTIONS = {\n  events: DEFAULT_EVENTS,\n  initialState: true,\n};\n\nexport function useIdle(\n  timeout: number,\n  options?: Partial<{ events: (keyof DocumentEventMap)[]; initialState: boolean }>\n) {\n  const { events, initialState } = { ...DEFAULT_OPTIONS, ...options };\n  const [idle, setIdle] = useState<boolean>(initialState);\n  const timer = useRef<number>(-1);\n\n  useEffect(() => {\n    const handleEvents = () => {\n      setIdle(false);\n\n      if (timer.current) {\n        window.clearTimeout(timer.current);\n      }\n\n      timer.current = window.setTimeout(() => {\n        setIdle(true);\n      }, timeout);\n    };\n\n    events.forEach((event) => document.addEventListener(event, handleEvents));\n\n    // Start the timer immediately instead of waiting for the first event to happen\n    timer.current = window.setTimeout(() => {\n      setIdle(true);\n    }, timeout);\n\n    return () => {\n      events.forEach((event) => document.removeEventListener(event, handleEvents));\n      window.clearTimeout(timer.current);\n      timer.current = -1;\n    };\n  }, [timeout]);\n\n  return idle;\n}\n", "import { useEffect, useRef, useState } from 'react';\n\ninterface UseIntervalOptions {\n  /** If set, the interval will start automatically when the component is mounted, `false` by default */\n  autoInvoke?: boolean;\n}\n\nexport function useInterval(\n  fn: () => void,\n  interval: number,\n  { autoInvoke = false }: UseIntervalOptions = {}\n) {\n  const [active, setActive] = useState(false);\n  const intervalRef = useRef<number | null>(null);\n  const fnRef = useRef<() => void>(null);\n\n  const start = () => {\n    setActive((old) => {\n      if (!old && (!intervalRef.current || intervalRef.current === -1)) {\n        intervalRef.current = window.setInterval(fnRef.current!, interval);\n      }\n      return true;\n    });\n  };\n\n  const stop = () => {\n    setActive(false);\n    window.clearInterval(intervalRef.current || -1);\n    intervalRef.current = -1;\n  };\n\n  const toggle = () => {\n    if (active) {\n      stop();\n    } else {\n      start();\n    }\n  };\n\n  useEffect(() => {\n    fnRef.current = fn;\n    active && start();\n    return stop;\n  }, [fn, active, interval]);\n\n  useEffect(() => {\n    if (autoInvoke) {\n      start();\n    }\n  }, []);\n\n  return { start, stop, toggle, active };\n}\n", "import { useState } from 'react';\n\nexport interface UseListStateHandlers<T> {\n  setState: React.Dispatch<React.SetStateAction<T[]>>;\n  append: (...items: T[]) => void;\n  prepend: (...items: T[]) => void;\n  insert: (index: number, ...items: T[]) => void;\n  pop: () => void;\n  shift: () => void;\n  apply: (fn: (item: T, index?: number) => T) => void;\n  applyWhere: (\n    condition: (item: T, index: number) => boolean,\n    fn: (item: T, index?: number) => T\n  ) => void;\n  remove: (...indices: number[]) => void;\n  reorder: ({ from, to }: { from: number; to: number }) => void;\n  swap: ({ from, to }: { from: number; to: number }) => void;\n  setItem: (index: number, item: T) => void;\n  setItemProp: <K extends keyof T, U extends T[K]>(index: number, prop: K, value: U) => void;\n  filter: (fn: (item: T, i: number) => boolean) => void;\n}\n\nexport type UseListState<T> = [T[], UseListStateHandlers<T>];\n\nexport function useListState<T>(initialValue: T[] = []): UseListState<T> {\n  const [state, setState] = useState(initialValue);\n\n  const append = (...items: T[]) => setState((current) => [...current, ...items]);\n  const prepend = (...items: T[]) => setState((current) => [...items, ...current]);\n\n  const insert = (index: number, ...items: T[]) =>\n    setState((current) => [...current.slice(0, index), ...items, ...current.slice(index)]);\n\n  const apply = (fn: (item: T, index?: number) => T) =>\n    setState((current) => current.map((item, index) => fn(item, index)));\n\n  const remove = (...indices: number[]) =>\n    setState((current) => current.filter((_, index) => !indices.includes(index)));\n\n  const pop = () =>\n    setState((current) => {\n      const cloned = [...current];\n      cloned.pop();\n      return cloned;\n    });\n\n  const shift = () =>\n    setState((current) => {\n      const cloned = [...current];\n      cloned.shift();\n      return cloned;\n    });\n\n  const reorder = ({ from, to }: { from: number; to: number }) =>\n    setState((current) => {\n      const cloned = [...current];\n      const item = current[from];\n\n      cloned.splice(from, 1);\n      cloned.splice(to, 0, item);\n\n      return cloned;\n    });\n\n  const swap = ({ from, to }: { from: number; to: number }) =>\n    setState((current) => {\n      const cloned = [...current];\n      const fromItem = cloned[from];\n      const toItem = cloned[to];\n\n      cloned.splice(to, 1, fromItem);\n      cloned.splice(from, 1, toItem);\n\n      return cloned;\n    });\n\n  const setItem = (index: number, item: T) =>\n    setState((current) => {\n      const cloned = [...current];\n      cloned[index] = item;\n      return cloned;\n    });\n\n  const setItemProp = <K extends keyof T, U extends T[K]>(index: number, prop: K, value: U) =>\n    setState((current) => {\n      const cloned = [...current];\n      cloned[index] = { ...cloned[index], [prop]: value };\n      return cloned;\n    });\n\n  const applyWhere = (\n    condition: (item: T, index: number) => boolean,\n    fn: (item: T, index?: number) => T\n  ) =>\n    setState((current) =>\n      current.map((item, index) => (condition(item, index) ? fn(item, index) : item))\n    );\n\n  const filter = (fn: (item: T, i: number) => boolean) => {\n    setState((current) => current.filter(fn));\n  };\n\n  return [\n    state,\n    {\n      setState,\n      append,\n      prepend,\n      insert,\n      pop,\n      shift,\n      apply,\n      applyWhere,\n      remove,\n      reorder,\n      swap,\n      setItem,\n      setItemProp,\n      filter,\n    },\n  ];\n}\n", "import { useEffect } from 'react';\n\nexport function useWindowEvent<K extends string>(\n  type: K,\n  listener: K extends keyof WindowEventMap\n    ? (this: Window, ev: WindowEventMap[K]) => void\n    : (this: Window, ev: CustomEvent) => void,\n  options?: boolean | AddEventListenerOptions\n) {\n  useEffect(() => {\n    window.addEventListener(type as any, listener, options);\n    return () => window.removeEventListener(type as any, listener, options);\n  }, [type, listener]);\n}\n", "/* eslint-disable no-console */\nimport { useCallback, useEffect, useState } from 'react';\nimport { useWindowEvent } from '../use-window-event/use-window-event';\n\nexport type StorageType = 'localStorage' | 'sessionStorage';\n\nexport interface StorageProperties<T> {\n  /** Storage key */\n  key: string;\n\n  /** Default value that will be set if value is not found in storage */\n  defaultValue?: T;\n\n  /** If set to true, value will be updated in useEffect after mount. Default value is true. */\n  getInitialValueInEffect?: boolean;\n\n  /** Determines whether the value must be synced between browser tabs, `true` by default */\n  sync?: boolean;\n\n  /** Function to serialize value into string to be save in storage */\n  serialize?: (value: T) => string;\n\n  /** Function to deserialize string value from storage to value */\n  deserialize?: (value: string | undefined) => T;\n}\n\nfunction serializeJSON<T>(value: T, hookName: string = 'use-local-storage') {\n  try {\n    return JSON.stringify(value);\n  } catch (error) {\n    throw new Error(`@mantine/hooks ${hookName}: Failed to serialize the value`);\n  }\n}\n\nfunction deserializeJSON(value: string | undefined) {\n  try {\n    return value && JSON.parse(value);\n  } catch {\n    return value;\n  }\n}\n\nfunction createStorageHandler(type: StorageType) {\n  const getItem = (key: string) => {\n    try {\n      return window[type].getItem(key);\n    } catch (error) {\n      console.warn('use-local-storage: Failed to get value from storage, localStorage is blocked');\n      return null;\n    }\n  };\n\n  const setItem = (key: string, value: string) => {\n    try {\n      window[type].setItem(key, value);\n    } catch (error) {\n      console.warn('use-local-storage: Failed to set value to storage, localStorage is blocked');\n    }\n  };\n\n  const removeItem = (key: string) => {\n    try {\n      window[type].removeItem(key);\n    } catch (error) {\n      console.warn(\n        'use-local-storage: Failed to remove value from storage, localStorage is blocked'\n      );\n    }\n  };\n\n  return { getItem, setItem, removeItem };\n}\n\nexport function createStorage<T>(type: StorageType, hookName: string) {\n  const eventName = type === 'localStorage' ? 'mantine-local-storage' : 'mantine-session-storage';\n  const { getItem, setItem, removeItem } = createStorageHandler(type);\n\n  return function useStorage({\n    key,\n    defaultValue,\n    getInitialValueInEffect = true,\n    sync = true,\n    deserialize = deserializeJSON,\n    serialize = (value: T) => serializeJSON(value, hookName),\n  }: StorageProperties<T>) {\n    const readStorageValue = useCallback(\n      (skipStorage?: boolean): T => {\n        let storageBlockedOrSkipped;\n\n        try {\n          storageBlockedOrSkipped =\n            typeof window === 'undefined' ||\n            !(type in window) ||\n            window[type] === null ||\n            !!skipStorage;\n        } catch (_e) {\n          storageBlockedOrSkipped = true;\n        }\n\n        if (storageBlockedOrSkipped) {\n          return defaultValue as T;\n        }\n\n        const storageValue = getItem(key);\n        return storageValue !== null ? deserialize(storageValue) : (defaultValue as T);\n      },\n      [key, defaultValue]\n    );\n\n    const [value, setValue] = useState<T>(readStorageValue(getInitialValueInEffect));\n\n    const setStorageValue = useCallback(\n      (val: T | ((prevState: T) => T)) => {\n        if (val instanceof Function) {\n          setValue((current) => {\n            const result = val(current);\n            setItem(key, serialize(result));\n            window.dispatchEvent(\n              new CustomEvent(eventName, { detail: { key, value: val(current) } })\n            );\n            return result;\n          });\n        } else {\n          setItem(key, serialize(val));\n          window.dispatchEvent(new CustomEvent(eventName, { detail: { key, value: val } }));\n          setValue(val);\n        }\n      },\n      [key]\n    );\n\n    const removeStorageValue = useCallback(() => {\n      removeItem(key);\n      window.dispatchEvent(new CustomEvent(eventName, { detail: { key, value: defaultValue } }));\n    }, []);\n\n    useWindowEvent('storage', (event) => {\n      if (sync) {\n        if (event.storageArea === window[type] && event.key === key) {\n          setValue(deserialize(event.newValue ?? undefined));\n        }\n      }\n    });\n\n    useWindowEvent(eventName, (event) => {\n      if (sync) {\n        if (event.detail.key === key) {\n          setValue(event.detail.value);\n        }\n      }\n    });\n\n    useEffect(() => {\n      if (defaultValue !== undefined && value === undefined) {\n        setStorageValue(defaultValue);\n      }\n    }, [defaultValue, value, setStorageValue]);\n\n    useEffect(() => {\n      const val = readStorageValue();\n      val !== undefined && setStorageValue(val);\n    }, [key]);\n\n    return [value === undefined ? defaultValue : value, setStorageValue, removeStorageValue] as [\n      T,\n      (val: T | ((prevState: T) => T)) => void,\n      () => void,\n    ];\n  };\n}\n\nexport function readValue(type: StorageType) {\n  const { getItem } = createStorageHandler(type);\n\n  return function read<T>({\n    key,\n    defaultValue,\n    deserialize = deserializeJSON,\n  }: StorageProperties<T>) {\n    let storageBlockedOrSkipped;\n\n    try {\n      storageBlockedOrSkipped =\n        typeof window === 'undefined' || !(type in window) || window[type] === null;\n    } catch (_e) {\n      storageBlockedOrSkipped = true;\n    }\n\n    if (storageBlockedOrSkipped) {\n      return defaultValue as T;\n    }\n\n    const storageValue = getItem(key);\n    return storageValue !== null ? deserialize(storageValue) : (defaultValue as T);\n  };\n}\n", "import { createStorage, readValue, StorageProperties } from './create-storage';\n\nexport function useLocalStorage<T = string>(props: StorageProperties<T>) {\n  return createStorage<T>('localStorage', 'use-local-storage')(props);\n}\n\nexport const readLocalStorageValue = readValue('localStorage');\n", "import { createStorage, readValue, StorageProperties } from '../use-local-storage/create-storage';\n\nexport function useSessionStorage<T = string>(props: StorageProperties<T>) {\n  return createStorage<T>('sessionStorage', 'use-session-storage')(props);\n}\n\nexport const readSessionStorageValue = readValue('sessionStorage');\n", "import { Ref, useCallback, type RefCallback } from 'react';\n\ntype PossibleRef<T> = Ref<T> | undefined;\n\ntype RefCleanup<T> = ReturnType<RefCallback<T>>;\n\nexport function assignRef<T>(ref: PossibleRef<T>, value: T): RefCleanup<T> {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (typeof ref === 'object' && ref !== null && 'current' in ref) {\n    ref.current = value;\n  }\n}\n\nexport function mergeRefs<T>(...refs: PossibleRef<T>[]) {\n  const cleanupMap = new Map<PossibleRef<T>, Exclude<RefCleanup<T>, void>>();\n\n  return (node: T | null) => {\n    refs.forEach((ref) => {\n      const cleanup = assignRef(ref, node);\n      if (cleanup) {\n        cleanupMap.set(ref, cleanup);\n      }\n    });\n\n    if (cleanupMap.size > 0) {\n      return () => {\n        refs.forEach((ref) => {\n          const cleanup = cleanupMap.get(ref);\n          if (cleanup) {\n            cleanup();\n          } else {\n            assignRef(ref, null);\n          }\n        });\n        cleanupMap.clear();\n      };\n    }\n  };\n}\n\nexport function useMergedRef<T>(...refs: PossibleRef<T>[]) {\n  return useCallback(mergeRefs(...refs), refs);\n}\n", "import { MouseEvent, useEffect, useRef, useState } from 'react';\n\nexport function useMouse<T extends HTMLElement = any>(\n  options: { resetOnExit?: boolean } = { resetOnExit: false }\n) {\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n\n  const ref = useRef<T>(null);\n\n  const setMousePosition = (event: MouseEvent<HTMLElement>) => {\n    if (ref.current) {\n      const rect = event.currentTarget.getBoundingClientRect();\n\n      const x = Math.max(\n        0,\n        Math.round(event.pageX - rect.left - (window.pageXOffset || window.scrollX))\n      );\n\n      const y = Math.max(\n        0,\n        Math.round(event.pageY - rect.top - (window.pageYOffset || window.scrollY))\n      );\n\n      setPosition({ x, y });\n    } else {\n      setPosition({ x: event.clientX, y: event.clientY });\n    }\n  };\n\n  const resetMousePosition = () => setPosition({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const element = ref?.current ? ref.current : document;\n    element.addEventListener('mousemove', setMousePosition as any);\n    if (options.resetOnExit) {\n      element.addEventListener('mouseleave', resetMousePosition as any);\n    }\n\n    return () => {\n      element.removeEventListener('mousemove', setMousePosition as any);\n      if (options.resetOnExit) {\n        element.removeEventListener('mouseleave', resetMousePosition as any);\n      }\n    };\n  }, [ref.current]);\n\n  return { ref, ...position };\n}\n", "import { useEffect, useRef, useState } from 'react';\nimport { clamp } from '../utils';\n\nexport interface UseMovePosition {\n  x: number;\n  y: number;\n}\n\nexport function clampUseMovePosition(position: UseMovePosition) {\n  return {\n    x: clamp(position.x, 0, 1),\n    y: clamp(position.y, 0, 1),\n  };\n}\n\ninterface useMoveHandlers {\n  onScrubStart?: () => void;\n  onScrubEnd?: () => void;\n}\n\nexport function useMove<T extends HTMLElement = any>(\n  onChange: (value: UseMovePosition) => void,\n  handlers?: useMoveHandlers,\n  dir: 'ltr' | 'rtl' = 'ltr'\n) {\n  const ref = useRef<T>(null);\n  const mounted = useRef<boolean>(false);\n  const isSliding = useRef(false);\n  const frame = useRef(0);\n  const [active, setActive] = useState(false);\n\n  useEffect(() => {\n    mounted.current = true;\n  }, []);\n\n  useEffect(() => {\n    const node = ref.current;\n\n    const onScrub = ({ x, y }: UseMovePosition) => {\n      cancelAnimationFrame(frame.current);\n\n      frame.current = requestAnimationFrame(() => {\n        if (mounted.current && node) {\n          node.style.userSelect = 'none';\n          const rect = node.getBoundingClientRect();\n\n          if (rect.width && rect.height) {\n            const _x = clamp((x - rect.left) / rect.width, 0, 1);\n            onChange({\n              x: dir === 'ltr' ? _x : 1 - _x,\n              y: clamp((y - rect.top) / rect.height, 0, 1),\n            });\n          }\n        }\n      });\n    };\n\n    const bindEvents = () => {\n      document.addEventListener('mousemove', onMouseMove);\n      document.addEventListener('mouseup', stopScrubbing);\n      document.addEventListener('touchmove', onTouchMove);\n      document.addEventListener('touchend', stopScrubbing);\n    };\n\n    const unbindEvents = () => {\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', stopScrubbing);\n      document.removeEventListener('touchmove', onTouchMove);\n      document.removeEventListener('touchend', stopScrubbing);\n    };\n\n    const startScrubbing = () => {\n      if (!isSliding.current && mounted.current) {\n        isSliding.current = true;\n        typeof handlers?.onScrubStart === 'function' && handlers.onScrubStart();\n        setActive(true);\n        bindEvents();\n      }\n    };\n\n    const stopScrubbing = () => {\n      if (isSliding.current && mounted.current) {\n        isSliding.current = false;\n        setActive(false);\n        unbindEvents();\n        setTimeout(() => {\n          typeof handlers?.onScrubEnd === 'function' && handlers.onScrubEnd();\n        }, 0);\n      }\n    };\n\n    const onMouseDown = (event: MouseEvent) => {\n      startScrubbing();\n      event.preventDefault();\n      onMouseMove(event);\n    };\n\n    const onMouseMove = (event: MouseEvent) => onScrub({ x: event.clientX, y: event.clientY });\n\n    const onTouchStart = (event: TouchEvent) => {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n\n      startScrubbing();\n      onTouchMove(event);\n    };\n\n    const onTouchMove = (event: TouchEvent) => {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n\n      onScrub({ x: event.changedTouches[0].clientX, y: event.changedTouches[0].clientY });\n    };\n\n    node?.addEventListener('mousedown', onMouseDown);\n    node?.addEventListener('touchstart', onTouchStart, { passive: false });\n\n    return () => {\n      if (node) {\n        node.removeEventListener('mousedown', onMouseDown);\n        node.removeEventListener('touchstart', onTouchStart);\n      }\n    };\n  }, [dir, onChange]);\n\n  return { ref, active };\n}\n", "import { useState } from 'react';\n\ninterface UseUncontrolledInput<T> {\n  /** Value for controlled state */\n  value?: T;\n\n  /** Initial value for uncontrolled state */\n  defaultValue?: T;\n\n  /** Final value for uncontrolled state when value and defaultValue are not provided */\n  finalValue?: T;\n\n  /** Controlled state onChange handler */\n  onChange?: (value: T, ...payload: any[]) => void;\n}\n\nexport function useUncontrolled<T>({\n  value,\n  defaultValue,\n  finalValue,\n  onChange = () => {},\n}: UseUncontrolledInput<T>): [T, (value: T, ...payload: any[]) => void, boolean] {\n  const [uncontrolledValue, setUncontrolledValue] = useState(\n    defaultValue !== undefined ? defaultValue : finalValue\n  );\n\n  const handleUncontrolledChange = (val: T, ...payload: any[]) => {\n    setUncontrolledValue(val);\n    onChange?.(val, ...payload);\n  };\n\n  if (value !== undefined) {\n    return [value as T, onChange, true];\n  }\n\n  return [uncontrolledValue as T, handleUncontrolledChange, false];\n}\n", "import { useMemo } from 'react';\nimport { useUncontrolled } from '../use-uncontrolled/use-uncontrolled';\n\nfunction range(start: number, end: number) {\n  const length = end - start + 1;\n  return Array.from({ length }, (_, index) => index + start);\n}\n\nexport const DOTS = 'dots';\n\nexport interface PaginationParams {\n  /** Page selected on initial render, defaults to 1 */\n  initialPage?: number;\n\n  /** Controlled active page number */\n  page?: number;\n\n  /** Total amount of pages */\n  total: number;\n\n  /** Siblings amount on left/right side of selected page, defaults to 1 */\n  siblings?: number;\n\n  /** Amount of elements visible on left/right edges, defaults to 1  */\n  boundaries?: number;\n\n  /** Callback fired after change of each page */\n  onChange?: (page: number) => void;\n}\n\nexport function usePagination({\n  total,\n  siblings = 1,\n  boundaries = 1,\n  page,\n  initialPage = 1,\n  onChange,\n}: PaginationParams) {\n  const _total = Math.max(Math.trunc(total), 0);\n  const [activePage, setActivePage] = useUncontrolled({\n    value: page,\n    onChange,\n    defaultValue: initialPage,\n    finalValue: initialPage,\n  });\n\n  const setPage = (pageNumber: number) => {\n    if (pageNumber <= 0) {\n      setActivePage(1);\n    } else if (pageNumber > _total) {\n      setActivePage(_total);\n    } else {\n      setActivePage(pageNumber);\n    }\n  };\n\n  const next = () => setPage(activePage + 1);\n  const previous = () => setPage(activePage - 1);\n  const first = () => setPage(1);\n  const last = () => setPage(_total);\n\n  const paginationRange = useMemo((): (number | 'dots')[] => {\n    const totalPageNumbers = siblings * 2 + 3 + boundaries * 2;\n    if (totalPageNumbers >= _total) {\n      return range(1, _total);\n    }\n\n    const leftSiblingIndex = Math.max(activePage - siblings, boundaries);\n    const rightSiblingIndex = Math.min(activePage + siblings, _total - boundaries);\n\n    const shouldShowLeftDots = leftSiblingIndex > boundaries + 2;\n    const shouldShowRightDots = rightSiblingIndex < _total - (boundaries + 1);\n\n    if (!shouldShowLeftDots && shouldShowRightDots) {\n      const leftItemCount = siblings * 2 + boundaries + 2;\n      return [...range(1, leftItemCount), DOTS, ...range(_total - (boundaries - 1), _total)];\n    }\n\n    if (shouldShowLeftDots && !shouldShowRightDots) {\n      const rightItemCount = boundaries + 1 + 2 * siblings;\n      return [...range(1, boundaries), DOTS, ...range(_total - rightItemCount, _total)];\n    }\n\n    return [\n      ...range(1, boundaries),\n      DOTS,\n      ...range(leftSiblingIndex, rightSiblingIndex),\n      DOTS,\n      ...range(_total - boundaries + 1, _total),\n    ];\n  }, [_total, siblings, activePage]);\n\n  return {\n    range: paginationRange,\n    active: activePage,\n    setPage,\n    next,\n    previous,\n    first,\n    last,\n  };\n}\n", "import { useState } from 'react';\n\nexport function useQueue<T>({ initialValues = [], limit }: { initialValues?: T[]; limit: number }) {\n  const [state, setState] = useState({\n    state: initialValues.slice(0, limit),\n    queue: initialValues.slice(limit),\n  });\n\n  const add = (...items: T[]) =>\n    setState((current) => {\n      const results = [...current.state, ...current.queue, ...items];\n\n      return {\n        state: results.slice(0, limit),\n        queue: results.slice(limit),\n      };\n    });\n\n  const update = (fn: (state: T[]) => T[]) =>\n    setState((current) => {\n      const results = fn([...current.state, ...current.queue]);\n\n      return {\n        state: results.slice(0, limit),\n        queue: results.slice(limit),\n      };\n    });\n\n  const cleanQueue = () => setState((current) => ({ state: current.state, queue: [] }));\n\n  return {\n    state: state.state,\n    queue: state.queue,\n    add,\n    update,\n    cleanQueue,\n  };\n}\n", "import { useEffect } from 'react';\n\nexport function usePageLeave(onPageLeave: () => void) {\n  useEffect(() => {\n    document.documentElement.addEventListener('mouseleave', onPageLeave);\n    return () => document.documentElement.removeEventListener('mouseleave', onPageLeave);\n  }, []);\n}\n", "import { useMediaQuery, UseMediaQueryOptions } from '../use-media-query/use-media-query';\n\nexport function useReducedMotion(initialValue?: boolean, options?: UseMediaQueryOptions) {\n  return useMediaQuery('(prefers-reduced-motion: reduce)', initialValue, options);\n}\n", "export const easeInOutQuad = (t: number) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t);\n", "export const getRelativePosition = ({\n  axis,\n  target,\n  parent,\n  alignment,\n  offset,\n  isList,\n}: any): number => {\n  if (!target || (!parent && typeof document === 'undefined')) {\n    return 0;\n  }\n  const isCustomParent = !!parent;\n  const parentElement = parent || document.body;\n  const parentPosition = parentElement.getBoundingClientRect();\n  const targetPosition = target.getBoundingClientRect();\n\n  const getDiff = (property: 'top' | 'left'): number =>\n    targetPosition[property] - parentPosition[property];\n\n  if (axis === 'y') {\n    const diff = getDiff('top');\n\n    if (diff === 0) {\n      return 0;\n    }\n\n    if (alignment === 'start') {\n      const distance = diff - offset;\n      const shouldScroll = distance <= targetPosition.height * (isList ? 0 : 1) || !isList;\n\n      return shouldScroll ? distance : 0;\n    }\n\n    const parentHeight = isCustomParent ? parentPosition.height : window.innerHeight;\n\n    if (alignment === 'end') {\n      const distance = diff + offset - parentHeight + targetPosition.height;\n      const shouldScroll = distance >= -targetPosition.height * (isList ? 0 : 1) || !isList;\n\n      return shouldScroll ? distance : 0;\n    }\n\n    if (alignment === 'center') {\n      return diff - parentHeight / 2 + targetPosition.height / 2;\n    }\n\n    return 0;\n  }\n\n  if (axis === 'x') {\n    const diff = getDiff('left');\n\n    if (diff === 0) {\n      return 0;\n    }\n\n    if (alignment === 'start') {\n      const distance = diff - offset;\n      const shouldScroll = distance <= targetPosition.width || !isList;\n\n      return shouldScroll ? distance : 0;\n    }\n\n    const parentWidth = isCustomParent ? parentPosition.width : window.innerWidth;\n\n    if (alignment === 'end') {\n      const distance = diff + offset - parentWidth + targetPosition.width;\n      const shouldScroll = distance >= -targetPosition.width || !isList;\n\n      return shouldScroll ? distance : 0;\n    }\n\n    if (alignment === 'center') {\n      return diff - parentWidth / 2 + targetPosition.width / 2;\n    }\n\n    return 0;\n  }\n\n  return 0;\n};\n", "export const getScrollStart = ({ axis, parent }: any) => {\n  if (!parent && typeof document === 'undefined') {\n    return 0;\n  }\n\n  const method = axis === 'y' ? 'scrollTop' : 'scrollLeft';\n\n  if (parent) {\n    return parent[method];\n  }\n\n  const { body, documentElement } = document;\n\n  // While one of it has a value the second is equal 0\n  return body[method] + documentElement[method];\n};\n", "export const setScrollParam = ({ axis, parent, distance }: any) => {\n  if (!parent && typeof document === 'undefined') {\n    return;\n  }\n\n  const method = axis === 'y' ? 'scrollTop' : 'scrollLeft';\n\n  if (parent) {\n    parent[method] = distance;\n  } else {\n    const { body, documentElement } = document;\n    body[method] = distance;\n    documentElement[method] = distance;\n  }\n};\n", "import { useCallback, useEffect, useRef } from 'react';\nimport { useReducedMotion } from '../use-reduced-motion/use-reduced-motion';\nimport { useWindowEvent } from '../use-window-event/use-window-event';\nimport { easeInOutQuad } from './utils/ease-in-out-quad';\nimport { getRelativePosition } from './utils/get-relative-position';\nimport { getScrollStart } from './utils/get-scroll-start';\nimport { setScrollParam } from './utils/set-scroll-param';\n\ninterface ScrollIntoViewAnimation {\n  /** Target element alignment relatively to parent based on current axis */\n  alignment?: 'start' | 'end' | 'center';\n}\n\ninterface ScrollIntoViewParams {\n  /** Callback fired after scroll */\n  onScrollFinish?: () => void;\n\n  /** Duration of scroll in milliseconds */\n  duration?: number;\n\n  /** Axis of scroll */\n  axis?: 'x' | 'y';\n\n  /** Custom mathematical easing function */\n  easing?: (t: number) => number;\n\n  /** Additional distance between nearest edge and element */\n  offset?: number;\n\n  /** Indicator if animation may be interrupted by user scrolling */\n  cancelable?: boolean;\n\n  /** Prevents content jumping in scrolling lists with multiple targets */\n  isList?: boolean;\n}\n\ninterface ScrollIntoViewReturnType<\n  Target extends HTMLElement = any,\n  Parent extends HTMLElement | null = null,\n> {\n  scrollableRef: React.RefObject<Parent>;\n  targetRef: React.RefObject<Target>;\n  scrollIntoView: (params?: ScrollIntoViewAnimation) => void;\n  cancel: () => void;\n}\n\nexport function useScrollIntoView<\n  Target extends HTMLElement = any,\n  Parent extends HTMLElement | null = null,\n>({\n  duration = 1250,\n  axis = 'y',\n  onScrollFinish,\n  easing = easeInOutQuad,\n  offset = 0,\n  cancelable = true,\n  isList = false,\n}: ScrollIntoViewParams = {}) {\n  const frameID = useRef(0);\n  const startTime = useRef(0);\n  const shouldStop = useRef(false);\n\n  const scrollableRef = useRef<Parent>(null);\n  const targetRef = useRef<Target>(null);\n\n  const reducedMotion = useReducedMotion();\n\n  const cancel = (): void => {\n    if (frameID.current) {\n      cancelAnimationFrame(frameID.current);\n    }\n  };\n\n  const scrollIntoView = useCallback(\n    ({ alignment = 'start' }: ScrollIntoViewAnimation = {}) => {\n      shouldStop.current = false;\n\n      if (frameID.current) {\n        cancel();\n      }\n\n      const start = getScrollStart({ parent: scrollableRef.current, axis }) ?? 0;\n\n      const change =\n        getRelativePosition({\n          parent: scrollableRef.current,\n          target: targetRef.current,\n          axis,\n          alignment,\n          offset,\n          isList,\n        }) - (scrollableRef.current ? 0 : start);\n\n      function animateScroll() {\n        if (startTime.current === 0) {\n          startTime.current = performance.now();\n        }\n\n        const now = performance.now();\n        const elapsed = now - startTime.current;\n\n        // Easing timing progress\n        const t = reducedMotion || duration === 0 ? 1 : elapsed / duration;\n\n        const distance = start + change * easing(t);\n\n        setScrollParam({\n          parent: scrollableRef.current,\n          axis,\n          distance,\n        });\n\n        if (!shouldStop.current && t < 1) {\n          frameID.current = requestAnimationFrame(animateScroll);\n        } else {\n          typeof onScrollFinish === 'function' && onScrollFinish();\n          startTime.current = 0;\n          frameID.current = 0;\n          cancel();\n        }\n      }\n      animateScroll();\n    },\n    [axis, duration, easing, isList, offset, onScrollFinish, reducedMotion]\n  );\n\n  const handleStop = () => {\n    if (cancelable) {\n      shouldStop.current = true;\n    }\n  };\n\n  /**\n   * Detection of one of these events stops scroll animation\n   * wheel - mouse wheel / touch pad\n   * touchmove - any touchable device\n   */\n\n  useWindowEvent('wheel', handleStop, {\n    passive: true,\n  });\n\n  useWindowEvent('touchmove', handleStop, {\n    passive: true,\n  });\n\n  // Cleanup requestAnimationFrame\n  useEffect(() => cancel, []);\n\n  return {\n    scrollableRef,\n    targetRef,\n    scrollIntoView,\n    cancel,\n  } as ScrollIntoViewReturnType<Target, Parent>;\n}\n", "import { useEffect, useMemo, useRef, useState } from 'react';\n\ntype ObserverRect = Omit<DOMRectReadOnly, 'toJSON'>;\n\nconst defaultState: ObserverRect = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  top: 0,\n  left: 0,\n  bottom: 0,\n  right: 0,\n};\n\nexport function useResizeObserver<T extends HTMLElement = any>(options?: ResizeObserverOptions) {\n  const frameID = useRef(0);\n  const ref = useRef<T>(null);\n\n  const [rect, setRect] = useState<ObserverRect>(defaultState);\n\n  const observer = useMemo(\n    () =>\n      typeof window !== 'undefined'\n        ? new ResizeObserver((entries) => {\n            const entry = entries[0];\n\n            if (entry) {\n              cancelAnimationFrame(frameID.current);\n\n              frameID.current = requestAnimationFrame(() => {\n                if (ref.current) {\n                  const boxSize = entry.borderBoxSize?.[0] || entry.contentBoxSize?.[0];\n                  if (boxSize) {\n                    const width = boxSize.inlineSize;\n                    const height = boxSize.blockSize;\n\n                    setRect({\n                      width,\n                      height,\n                      x: entry.contentRect.x,\n                      y: entry.contentRect.y,\n                      top: entry.contentRect.top,\n                      left: entry.contentRect.left,\n                      bottom: entry.contentRect.bottom,\n                      right: entry.contentRect.right,\n                    });\n                  } else {\n                    setRect(entry.contentRect);\n                  }\n                }\n              });\n            }\n          })\n        : null,\n    []\n  );\n\n  useEffect(() => {\n    if (ref.current) {\n      observer?.observe(ref.current, options);\n    }\n\n    return () => {\n      observer?.disconnect();\n\n      if (frameID.current) {\n        cancelAnimationFrame(frameID.current);\n      }\n    };\n  }, [ref.current]);\n\n  return [ref, rect] as const;\n}\n\nexport function useElementSize<T extends HTMLElement = any>(options?: ResizeObserverOptions) {\n  const [ref, { width, height }] = useResizeObserver<T>(options);\n  return { ref, width, height };\n}\n", "import { useEffect, useRef } from 'react';\nimport { shallowEqual } from '../utils/shallow-equal/shallow-equal';\n\nfunction shallowCompare(prevValue?: React.DependencyList | null, currValue?: React.DependencyList) {\n  if (!prevValue || !currValue) {\n    return false;\n  }\n\n  if (prevValue === currValue) {\n    return true;\n  }\n\n  if (prevValue.length !== currValue.length) {\n    return false;\n  }\n\n  for (let i = 0; i < prevValue.length; i += 1) {\n    if (!shallowEqual(prevValue[i], currValue[i])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction useShallowCompare(dependencies?: React.DependencyList) {\n  const ref = useRef<React.DependencyList | null | undefined>([]);\n  const updateRef = useRef<number>(0);\n\n  if (!shallowCompare(ref.current, dependencies)) {\n    ref.current = dependencies;\n    updateRef.current += 1;\n  }\n\n  return [updateRef.current];\n}\n\nexport function useShallowEffect(cb: () => void, dependencies?: React.DependencyList): void {\n  useEffect(cb, useShallowCompare(dependencies));\n}\n", "import { useReducer } from 'react';\n\nexport function useToggle<T = boolean>(options: readonly T[] = [false, true] as any) {\n  const [[option], toggle] = useReducer((state: T[], action: React.SetStateAction<T>) => {\n    const value = action instanceof Function ? action(state[0]) : action;\n    const index = Math.abs(state.indexOf(value));\n\n    return state.slice(index).concat(state.slice(0, index));\n  }, options as T[]);\n\n  return [option, toggle as (value?: React.SetStateAction<T>) => void] as const;\n}\n", "import { useCallback, useEffect, useState } from 'react';\nimport { useWindowEvent } from '../use-window-event/use-window-event';\n\nconst eventListerOptions = {\n  passive: true,\n};\n\nexport function useViewportSize() {\n  const [windowSize, setWindowSize] = useState({\n    width: 0,\n    height: 0,\n  });\n\n  const setSize = useCallback(() => {\n    setWindowSize({ width: window.innerWidth || 0, height: window.innerHeight || 0 });\n  }, []);\n\n  useWindowEvent('resize', setSize, eventListerOptions);\n  useWindowEvent('orientationchange', setSize, eventListerOptions);\n  useEffect(setSize, []);\n\n  return windowSize;\n}\n", "import { useEffect, useState } from 'react';\nimport { useWindowEvent } from '../use-window-event/use-window-event';\n\ninterface ScrollPosition {\n  x: number;\n  y: number;\n}\n\nfunction getScrollPosition(): ScrollPosition {\n  return typeof window !== 'undefined'\n    ? { x: window.pageXOffset, y: window.pageYOffset }\n    : { x: 0, y: 0 };\n}\n\nfunction scrollTo({ x, y }: Partial<ScrollPosition>) {\n  if (typeof window !== 'undefined') {\n    const scrollOptions: ScrollToOptions = { behavior: 'smooth' };\n\n    if (typeof x === 'number') {\n      scrollOptions.left = x;\n    }\n\n    if (typeof y === 'number') {\n      scrollOptions.top = y;\n    }\n\n    window.scrollTo(scrollOptions);\n  }\n}\n\nexport function useWindowScroll() {\n  const [position, setPosition] = useState<ScrollPosition>({ x: 0, y: 0 });\n\n  useWindowEvent('scroll', () => setPosition(getScrollPosition()));\n  useWindowEvent('resize', () => setPosition(getScrollPosition()));\n\n  useEffect(() => {\n    setPosition(getScrollPosition());\n  }, []);\n\n  return [position, scrollTo] as const;\n}\n", "import { useCallback, useRef, useState } from 'react';\n\nexport function useIntersection<T extends HTMLElement = any>(\n  options?: ConstructorParameters<typeof IntersectionObserver>[1]\n) {\n  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);\n\n  const observer = useRef<IntersectionObserver | null>(null);\n\n  const ref = useCallback(\n    (element: T | null) => {\n      if (observer.current) {\n        observer.current.disconnect();\n        observer.current = null;\n      }\n\n      if (element === null) {\n        setEntry(null);\n        return;\n      }\n\n      observer.current = new IntersectionObserver(([_entry]) => {\n        setEntry(_entry);\n      }, options);\n\n      observer.current.observe(element);\n    },\n    [options?.rootMargin, options?.root, options?.threshold]\n  );\n\n  return { ref, entry };\n}\n", "import { useEffect, useState } from 'react';\nimport { useWindowEvent } from '../use-window-event/use-window-event';\n\ninterface UseHashOptions {\n  getInitialValueInEffect?: boolean;\n}\n\nexport function useHash({ getInitialValueInEffect = true }: UseHashOptions = {}) {\n  const [hash, setHash] = useState<string>(\n    getInitialValueInEffect ? '' : window.location.hash || ''\n  );\n\n  const setHashHandler = (value: string) => {\n    const valueWithHash = value.startsWith('#') ? value : `#${value}`;\n    window.location.hash = valueWithHash;\n    setHash(valueWithHash);\n  };\n\n  useWindowEvent('hashchange', () => {\n    const newHash = window.location.hash;\n    if (hash !== newHash) {\n      setHash(newHash);\n    }\n  });\n\n  useEffect(() => {\n    if (getInitialValueInEffect) {\n      setHash(window.location.hash);\n    }\n  }, []);\n\n  return [hash, setHashHandler] as const;\n}\n", "export type KeyboardModifiers = {\n  alt: boolean;\n  ctrl: boolean;\n  meta: boolean;\n  mod: boolean;\n  shift: boolean;\n  plus: boolean;\n};\n\nexport type Hotkey = KeyboardModifiers & {\n  key?: string;\n};\n\ntype CheckHotkeyMatch = (event: KeyboardEvent) => boolean;\n\nconst keyNameMap: Record<string, string> = {\n  ' ': 'space',\n  ArrowLeft: 'arrowleft',\n  ArrowRight: 'arrowright',\n  ArrowUp: 'arrowup',\n  ArrowDown: 'arrowdown',\n  Escape: 'esc',\n  Esc: 'esc',\n  Enter: 'enter',\n  Tab: 'tab',\n  Backspace: 'backspace',\n  Delete: 'delete',\n  Insert: 'insert',\n  Home: 'home',\n  End: 'end',\n  PageUp: 'pageup',\n  PageDown: 'pagedown',\n  '+': 'plus',\n  '-': 'minus',\n  '*': 'asterisk',\n  '/': 'slash',\n};\n\nfunction normalizeKey(key: string): string {\n  const lowerKey = key.replace('Key', '').toLowerCase();\n  return keyNameMap[key] || lowerKey;\n}\n\nexport function parseHotkey(hotkey: string): Hotkey {\n  const keys = hotkey\n    .toLowerCase()\n    .split('+')\n    .map((part) => part.trim());\n\n  const modifiers: KeyboardModifiers = {\n    alt: keys.includes('alt'),\n    ctrl: keys.includes('ctrl'),\n    meta: keys.includes('meta'),\n    mod: keys.includes('mod'),\n    shift: keys.includes('shift'),\n    plus: keys.includes('[plus]'),\n  };\n\n  const reservedKeys = ['alt', 'ctrl', 'meta', 'shift', 'mod'];\n\n  const freeKey = keys.find((key) => !reservedKeys.includes(key));\n\n  return {\n    ...modifiers,\n    key: freeKey === '[plus]' ? '+' : freeKey,\n  };\n}\n\nfunction isExactHotkey(hotkey: Hotkey, event: KeyboardEvent, usePhysicalKeys?: boolean): boolean {\n  const { alt, ctrl, meta, mod, shift, key } = hotkey;\n  const { altKey, ctrlKey, metaKey, shiftKey, key: pressedKey, code: pressedCode } = event;\n\n  if (alt !== altKey) {\n    return false;\n  }\n\n  if (mod) {\n    if (!ctrlKey && !metaKey) {\n      return false;\n    }\n  } else {\n    if (ctrl !== ctrlKey) {\n      return false;\n    }\n    if (meta !== metaKey) {\n      return false;\n    }\n  }\n  if (shift !== shiftKey) {\n    return false;\n  }\n\n  if (\n    key &&\n    (usePhysicalKeys\n      ? normalizeKey(pressedCode) === normalizeKey(key)\n      : normalizeKey(pressedKey ?? pressedCode) === normalizeKey(key))\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\nexport function getHotkeyMatcher(hotkey: string, usePhysicalKeys?: boolean): CheckHotkeyMatch {\n  return (event) => isExactHotkey(parseHotkey(hotkey), event, usePhysicalKeys);\n}\n\nexport interface HotkeyItemOptions {\n  preventDefault?: boolean;\n  usePhysicalKeys?: boolean;\n}\n\ntype HotkeyItem = [string, (event: any) => void, HotkeyItemOptions?];\n\nexport function getHotkeyHandler(hotkeys: HotkeyItem[]) {\n  return (event: React.KeyboardEvent<HTMLElement> | KeyboardEvent) => {\n    const _event = 'nativeEvent' in event ? event.nativeEvent : event;\n    hotkeys.forEach(\n      ([hotkey, handler, options = { preventDefault: true, usePhysicalKeys: false }]) => {\n        if (getHotkeyMatcher(hotkey, options.usePhysicalKeys)(_event)) {\n          if (options.preventDefault) {\n            event.preventDefault();\n          }\n\n          handler(_event);\n        }\n      }\n    );\n  };\n}\n", "import { useEffect } from 'react';\nimport { getHotkey<PERSON><PERSON><PERSON>, getHotkeyMatcher, HotkeyItemOptions } from './parse-hotkey';\n\nexport type { HotkeyItemOptions };\nexport { getHotkeyHandler };\n\nexport type HotkeyItem = [string, (event: KeyboardEvent) => void, HotkeyItemOptions?];\n\nfunction shouldFireEvent(\n  event: KeyboardEvent,\n  tagsToIgnore: string[],\n  triggerOnContentEditable = false\n) {\n  if (event.target instanceof HTMLElement) {\n    if (triggerOnContentEditable) {\n      return !tagsToIgnore.includes(event.target.tagName);\n    }\n\n    return !event.target.isContentEditable && !tagsToIgnore.includes(event.target.tagName);\n  }\n\n  return true;\n}\n\nexport function useHotkeys(\n  hotkeys: HotkeyItem[],\n  tagsToIgnore: string[] = ['INPUT', 'TEXTAREA', 'SELECT'],\n  triggerOnContentEditable = false\n) {\n  useEffect(() => {\n    const keydownListener = (event: KeyboardEvent) => {\n      hotkeys.forEach(\n        ([hotkey, handler, options = { preventDefault: true, usePhysicalKeys: false }]) => {\n          if (\n            getHotkeyMatcher(hotkey, options.usePhysicalKeys)(event) &&\n            shouldFireEvent(event, tagsToIgnore, triggerOnContentEditable)\n          ) {\n            if (options.preventDefault) {\n              event.preventDefault();\n            }\n\n            handler(event);\n          }\n        }\n      );\n    };\n\n    document.documentElement.addEventListener('keydown', keydownListener);\n    return () => document.documentElement.removeEventListener('keydown', keydownListener);\n  }, [hotkeys]);\n}\n", "import { useCallback, useEffect, useRef, useState } from 'react';\n\nfunction getFullscreenElement(): HTMLElement | null {\n  const _document = window.document as any;\n\n  const fullscreenElement =\n    _document.fullscreenElement ||\n    _document.webkitFullscreenElement ||\n    _document.mozFullScreenElement ||\n    _document.msFullscreenElement;\n\n  return fullscreenElement;\n}\n\nfunction exitFullscreen() {\n  const _document = window.document as any;\n\n  if (typeof _document.exitFullscreen === 'function') {\n    return _document.exitFullscreen();\n  }\n  if (typeof _document.msExitFullscreen === 'function') {\n    return _document.msExitFullscreen();\n  }\n  if (typeof _document.webkitExitFullscreen === 'function') {\n    return _document.webkitExitFullscreen();\n  }\n  if (typeof _document.mozCancelFullScreen === 'function') {\n    return _document.mozCancelFullScreen();\n  }\n\n  return null;\n}\n\nfunction enterFullScreen(element: HTMLElement) {\n  const _element = element as any;\n\n  return (\n    _element.requestFullscreen?.() ||\n    _element.msRequestFullscreen?.() ||\n    _element.webkitEnterFullscreen?.() ||\n    _element.webkitRequestFullscreen?.() ||\n    _element.mozRequestFullscreen?.()\n  );\n}\n\nconst prefixes = ['', 'webkit', 'moz', 'ms'];\n\nfunction addEvents(\n  element: HTMLElement,\n  {\n    onFullScreen,\n    onError,\n  }: { onFullScreen: (event: Event) => void; onError: (event: Event) => void }\n) {\n  prefixes.forEach((prefix) => {\n    element.addEventListener(`${prefix}fullscreenchange`, onFullScreen);\n    element.addEventListener(`${prefix}fullscreenerror`, onError);\n  });\n\n  return () => {\n    prefixes.forEach((prefix) => {\n      element.removeEventListener(`${prefix}fullscreenchange`, onFullScreen);\n      element.removeEventListener(`${prefix}fullscreenerror`, onError);\n    });\n  };\n}\n\nexport function useFullscreen<T extends HTMLElement = any>() {\n  const [fullscreen, setFullscreen] = useState<boolean>(false);\n\n  const _ref = useRef<T>(null);\n\n  const handleFullscreenChange = useCallback(\n    (event: Event) => {\n      setFullscreen(event.target === getFullscreenElement());\n    },\n    [setFullscreen]\n  );\n\n  const handleFullscreenError = useCallback(\n    (event: Event) => {\n      setFullscreen(false);\n      // eslint-disable-next-line no-console\n      console.error(\n        `[@mantine/hooks] use-fullscreen: Error attempting full-screen mode method: ${event} (${event.target})`\n      );\n    },\n    [setFullscreen]\n  );\n\n  const toggle = useCallback(async () => {\n    if (!getFullscreenElement()) {\n      await enterFullScreen(_ref.current!);\n    } else {\n      await exitFullscreen();\n    }\n  }, []);\n\n  const ref = useCallback((element: T | null) => {\n    if (element === null) {\n      _ref.current = window.document.documentElement as T;\n    } else {\n      _ref.current = element;\n    }\n  }, []);\n\n  useEffect(() => {\n    if (!_ref.current && window.document) {\n      _ref.current = window.document.documentElement as T;\n      return addEvents(_ref.current, {\n        onFullScreen: handleFullscreenChange,\n        onError: handleFullscreenError,\n      });\n    }\n\n    if (_ref.current) {\n      return addEvents(_ref.current, {\n        onFullScreen: handleFullscreenChange,\n        onError: handleFullscreenError,\n      });\n    }\n\n    return undefined;\n  }, [_ref.current]);\n\n  return { ref, toggle, fullscreen } as const;\n}\n", "/* eslint-disable no-console */\nimport { useEffect } from 'react';\nimport { useDidUpdate } from '../use-did-update/use-did-update';\n\nexport function useLogger(componentName: string, props: any[]) {\n  useEffect(() => {\n    console.log(`${componentName} mounted`, ...props);\n    return () => console.log(`${componentName} unmounted`);\n  }, []);\n\n  useDidUpdate(() => {\n    console.log(`${componentName} updated`, ...props);\n  }, props);\n\n  return null;\n}\n", "import { useCallback, useEffect, useRef, useState } from 'react';\n\nexport function useHover<T extends HTMLElement = any>() {\n  const [hovered, setHovered] = useState(false);\n  const ref = useRef<T>(null);\n  const onMouseEnter = useCallback(() => setHovered(true), []);\n  const onMouseLeave = useCallback(() => setHovered(false), []);\n\n  useEffect(() => {\n    const node = ref.current;\n\n    if (node) {\n      node.addEventListener('mouseenter', onMouseEnter);\n      node.addEventListener('mouseleave', onMouseLeave);\n\n      return () => {\n        node?.removeEventListener('mouseenter', onMouseEnter);\n        node?.removeEventListener('mouseleave', onMouseLeave);\n      };\n    }\n\n    return undefined;\n  }, [ref.current]);\n\n  return { ref, hovered };\n}\n", "import { useState } from 'react';\n\nexport function useValidatedState<T>(\n  initialValue: T,\n  validation: (value: T) => boolean,\n  initialValidationState?: boolean\n) {\n  const [value, setValue] = useState<T>(initialValue);\n  const [lastValidValue, setLastValidValue] = useState<T | undefined>(\n    validation(initialValue) ? initialValue : undefined\n  );\n  const [valid, setValid] = useState<boolean>(\n    typeof initialValidationState === 'boolean' ? initialValidationState : validation(initialValue)\n  );\n\n  const onChange = (val: T) => {\n    if (validation(val)) {\n      setLastValidValue(val);\n      setValid(true);\n    } else {\n      setValid(false);\n    }\n\n    setValue(val);\n  };\n\n  return [{ value, lastValidValue, valid }, onChange] as const;\n}\n", "import { useState } from 'react';\nimport { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\n\nexport type OS = 'undetermined' | 'macos' | 'ios' | 'windows' | 'android' | 'linux';\n\nfunction isMacOS(userAgent: string): boolean {\n  const macosPattern = /(Macintosh)|(MacIntel)|(MacPPC)|(Mac68K)/i;\n\n  return macosPattern.test(userAgent);\n}\n\nfunction isIOS(userAgent: string): boolean {\n  const iosPattern = /(iPhone)|(iPad)|(iPod)/i;\n\n  return iosPattern.test(userAgent);\n}\n\nfunction isWindows(userAgent: string): boolean {\n  const windowsPattern = /(Win32)|(Win64)|(Windows)|(WinCE)/i;\n\n  return windowsPattern.test(userAgent);\n}\n\nfunction isAndroid(userAgent: string): boolean {\n  const androidPattern = /Android/i;\n\n  return androidPattern.test(userAgent);\n}\n\nfunction isLinux(userAgent: string): boolean {\n  const linuxPattern = /Linux/i;\n\n  return linuxPattern.test(userAgent);\n}\n\nfunction getOS(): OS {\n  if (typeof window === 'undefined') {\n    return 'undetermined';\n  }\n\n  const { userAgent } = window.navigator;\n\n  if (isIOS(userAgent) || (isMacOS(userAgent) && 'ontouchend' in document)) {\n    return 'ios';\n  }\n  if (isMacOS(userAgent)) {\n    return 'macos';\n  }\n  if (isWindows(userAgent)) {\n    return 'windows';\n  }\n  if (isAndroid(userAgent)) {\n    return 'android';\n  }\n  if (isLinux(userAgent)) {\n    return 'linux';\n  }\n\n  return 'undetermined';\n}\n\ninterface UseOsOptions {\n  getValueInEffect: boolean;\n}\n\nexport function useOs(options: UseOsOptions = { getValueInEffect: true }): OS {\n  const [value, setValue] = useState<OS>(options.getValueInEffect ? 'undetermined' : getOS());\n\n  useIsomorphicEffect(() => {\n    if (options.getValueInEffect) {\n      setValue(getOS);\n    }\n  }, []);\n\n  return value;\n}\n", "import { useCallback, useState } from 'react';\n\nexport function useSetState<T extends Record<string, any>>(initialState: T) {\n  const [state, setState] = useState(initialState);\n\n  const _setState = useCallback(\n    (statePartial: Partial<T> | ((currentState: T) => Partial<T>)) =>\n      setState((current) => ({\n        ...current,\n        ...(typeof statePartial === 'function' ? statePartial(current) : statePartial),\n      })),\n    []\n  );\n\n  return [state, _setState] as const;\n}\n", "import { useState } from 'react';\n\nexport function getInputOnChange<T>(\n  setValue: (value: null | undefined | T | ((current: T) => T)) => void\n) {\n  return (val: null | undefined | T | React.ChangeEvent<any> | ((current: T) => T)) => {\n    if (!val) {\n      setValue(val as T);\n    } else if (typeof val === 'function') {\n      setValue(val);\n    } else if (typeof val === 'object' && 'nativeEvent' in val) {\n      const { currentTarget } = val;\n\n      if (currentTarget.type === 'checkbox') {\n        setValue((currentTarget as any).checked as any);\n      } else {\n        setValue(currentTarget.value as any);\n      }\n    } else {\n      setValue(val);\n    }\n  };\n}\n\nexport function useInputState<T>(initialState: T) {\n  const [value, setValue] = useState<T>(initialState);\n  return [value, getInputOnChange<T>(setValue as any)] as [\n    T,\n    (value: null | undefined | T | React.ChangeEvent<any>) => void,\n  ];\n}\n", "import { useEffect, useRef } from 'react';\n\nexport function useEventListener<K extends keyof HTMLElementEventMap, T extends HTMLElement = any>(\n  type: K,\n  listener: (this: HTMLDivElement, ev: HTMLElementEventMap[K]) => any,\n  options?: boolean | AddEventListenerOptions\n) {\n  const ref = useRef<T>(null);\n\n  useEffect(() => {\n    const node = ref.current;\n\n    if (node) {\n      node.addEventListener(type, listener as any, options);\n      return () => node?.removeEventListener(type, listener as any, options);\n    }\n    return undefined;\n  }, [listener, options]);\n\n  return ref;\n}\n", "import { useCallback, useState } from 'react';\n\nexport function useDisclosure(\n  initialState = false,\n  callbacks?: { onOpen?: () => void; onClose?: () => void }\n) {\n  const { onOpen, onClose } = callbacks || {};\n  const [opened, setOpened] = useState(initialState);\n\n  const open = useCallback(() => {\n    setOpened((isOpened) => {\n      if (!isOpened) {\n        onOpen?.();\n        return true;\n      }\n      return isOpened;\n    });\n  }, [onOpen]);\n\n  const close = useCallback(() => {\n    setOpened((isOpened) => {\n      if (isOpened) {\n        onClose?.();\n        return false;\n      }\n      return isOpened;\n    });\n  }, [onClose]);\n\n  const toggle = useCallback(() => {\n    opened ? close() : open();\n  }, [close, open, opened]);\n\n  return [opened, { open, close, toggle }] as const;\n}\n", "import { useEffect, useRef, useState } from 'react';\n\nexport interface UseFocusWithinOptions {\n  onFocus?: (event: FocusEvent) => void;\n  onBlur?: (event: FocusEvent) => void;\n}\n\nfunction containsRelatedTarget(event: FocusEvent) {\n  if (event.currentTarget instanceof HTMLElement && event.relatedTarget instanceof HTMLElement) {\n    return event.currentTarget.contains(event.relatedTarget);\n  }\n\n  return false;\n}\n\nexport function useFocusWithin<T extends HTMLElement = any>({\n  onBlur,\n  onFocus,\n}: UseFocusWithinOptions = {}): { ref: React.RefObject<T>; focused: boolean } {\n  const ref = useRef<T>(null);\n  const [focused, setFocused] = useState(false);\n  const focusedRef = useRef(false);\n\n  const _setFocused = (value: boolean) => {\n    setFocused(value);\n    focusedRef.current = value;\n  };\n\n  const handleFocusIn = (event: FocusEvent) => {\n    if (!focusedRef.current) {\n      _setFocused(true);\n      onFocus?.(event);\n    }\n  };\n\n  const handleFocusOut = (event: FocusEvent) => {\n    if (focusedRef.current && !containsRelatedTarget(event)) {\n      _setFocused(false);\n      onBlur?.(event);\n    }\n  };\n\n  useEffect(() => {\n    const node = ref.current;\n\n    if (node) {\n      node.addEventListener('focusin', handleFocusIn);\n      node.addEventListener('focusout', handleFocusOut);\n\n      return () => {\n        node?.removeEventListener('focusin', handleFocusIn);\n        node?.removeEventListener('focusout', handleFocusOut);\n      };\n    }\n\n    return undefined;\n  }, [handleFocusIn, handleFocusOut]);\n\n  return { ref: ref as React.RefObject<T>, focused };\n}\n", "import { useCallback, useEffect, useState } from 'react';\nimport { useWindowEvent } from '../use-window-event/use-window-event';\n\ninterface NetworkStatus {\n  downlink?: number;\n  downlinkMax?: number;\n  effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';\n  rtt?: number;\n  saveData?: boolean;\n  type?: 'bluetooth' | 'cellular' | 'ethernet' | 'wifi' | 'wimax' | 'none' | 'other' | 'unknown';\n}\n\nfunction getConnection(): NetworkStatus {\n  if (typeof navigator === 'undefined') {\n    return {};\n  }\n\n  const _navigator = navigator as any;\n  const connection: any =\n    _navigator.connection || _navigator.mozConnection || _navigator.webkitConnection;\n\n  if (!connection) {\n    return {};\n  }\n\n  return {\n    downlink: connection?.downlink,\n    downlinkMax: connection?.downlinkMax,\n    effectiveType: connection?.effectiveType,\n    rtt: connection?.rtt,\n    saveData: connection?.saveData,\n    type: connection?.type,\n  };\n}\n\nexport function useNetwork() {\n  const [status, setStatus] = useState<{ online: boolean } & NetworkStatus>({\n    online: true,\n  });\n  const handleConnectionChange = useCallback(\n    () => setStatus((current) => ({ ...current, ...getConnection() })),\n    []\n  );\n\n  useWindowEvent('online', () => setStatus({ online: true, ...getConnection() }));\n  useWindowEvent('offline', () => setStatus({ online: false, ...getConnection() }));\n\n  useEffect(() => {\n    const _navigator = navigator as any;\n\n    if (_navigator.connection) {\n      setStatus({ online: _navigator.onLine, ...getConnection() });\n      _navigator.connection.addEventListener('change', handleConnectionChange);\n      return () => _navigator.connection.removeEventListener('change', handleConnectionChange);\n    }\n\n    if (typeof _navigator.onLine === 'boolean') {\n      // Required for Firefox and other browsers that don't support navigator.connection\n      setStatus((current) => ({ ...current, online: _navigator.onLine }));\n    }\n\n    return undefined;\n  }, []);\n\n  return status;\n}\n", "import { useCallback, useEffect, useRef } from 'react';\n\nexport function useTimeout(\n  callback: (...callbackParams: any[]) => void,\n  delay: number,\n  options: { autoInvoke: boolean } = { autoInvoke: false }\n) {\n  const timeoutRef = useRef<number | null>(null);\n\n  const start = useCallback(\n    (...callbackParams: any[]) => {\n      if (!timeoutRef.current) {\n        timeoutRef.current = window.setTimeout(() => {\n          callback(callbackParams);\n          timeoutRef.current = null;\n        }, delay);\n      }\n    },\n    [delay]\n  );\n\n  const clear = useCallback(() => {\n    if (timeoutRef.current) {\n      window.clearTimeout(timeoutRef.current);\n      timeoutRef.current = null;\n    }\n  }, []);\n\n  useEffect(() => {\n    if (options.autoInvoke) {\n      start();\n    }\n\n    return clear;\n  }, [clear, start]);\n\n  return { start, clear };\n}\n", "import { useEffect, useState } from 'react';\nimport { useForceUpdate } from '../use-force-update/use-force-update';\n\nexport function useTextSelection(): Selection | null {\n  const forceUpdate = useForceUpdate();\n  const [selection, setSelection] = useState<Selection | null>(null);\n\n  const handleSelectionChange = () => {\n    setSelection(document.getSelection());\n    forceUpdate();\n  };\n\n  useEffect(() => {\n    setSelection(document.getSelection());\n    document.addEventListener('selectionchange', handleSelectionChange);\n    return () => document.removeEventListener('selectionchange', handleSelectionChange);\n  }, []);\n\n  return selection;\n}\n", "import { useEffect, useRef } from 'react';\n\nexport function usePrevious<T>(value: T): T | undefined {\n  const ref = useRef<T>(undefined);\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n", "import { useRef } from 'react';\nimport { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\n\nconst MIME_TYPES: Record<string, string> = {\n  ico: 'image/x-icon',\n  png: 'image/png',\n  svg: 'image/svg+xml',\n  gif: 'image/gif',\n};\n\nexport function useFavicon(url: string) {\n  const link = useRef<HTMLLinkElement>(null);\n\n  useIsomorphicEffect(() => {\n    if (!url) {\n      return;\n    }\n\n    if (!link.current) {\n      const existingElements = document.querySelectorAll<HTMLLinkElement>('link[rel*=\"icon\"]');\n      existingElements.forEach((element) => document.head.removeChild(element));\n\n      const element = document.createElement('link');\n      element.rel = 'shortcut icon';\n      link.current = element;\n      document.querySelector('head')!.appendChild(element);\n    }\n\n    const splittedUrl = url.split('.');\n    link.current.setAttribute(\n      'type',\n      MIME_TYPES[splittedUrl[splittedUrl.length - 1].toLowerCase()]\n    );\n    link.current.setAttribute('href', url);\n  }, [url]);\n}\n", "import { useEffect, useRef, useState } from 'react';\nimport { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\nimport { useWindowScroll } from '../use-window-scroll/use-window-scroll';\n\nexport const isFixed = (current: number, fixedAt: number) => current <= fixedAt;\nexport const isPinned = (current: number, previous: number) => current <= previous;\nexport const isReleased = (current: number, previous: number, fixedAt: number) =>\n  !isPinned(current, previous) && !isFixed(current, fixedAt);\n\nexport const isPinnedOrReleased = (\n  current: number,\n  fixedAt: number,\n  isCurrentlyPinnedRef: React.MutableRefObject<boolean>,\n  isScrollingUp: boolean,\n  onPin?: () => void,\n  onRelease?: () => void\n) => {\n  const isInFixedPosition = isFixed(current, fixedAt);\n  if (isInFixedPosition && !isCurrentlyPinnedRef.current) {\n    isCurrentlyPinnedRef.current = true;\n    onPin?.();\n  } else if (!isInFixedPosition && isScrollingUp && !isCurrentlyPinnedRef.current) {\n    isCurrentlyPinnedRef.current = true;\n    onPin?.();\n  } else if (!isInFixedPosition && isCurrentlyPinnedRef.current) {\n    isCurrentlyPinnedRef.current = false;\n    onRelease?.();\n  }\n};\n\nexport const useScrollDirection = () => {\n  const [lastScrollTop, setLastScrollTop] = useState(0);\n  const [isScrollingUp, setIsScrollingUp] = useState(false);\n  const [isResizing, setIsResizing] = useState(false);\n\n  useEffect(() => {\n    let resizeTimer: NodeJS.Timeout | undefined;\n\n    const onResize = () => {\n      setIsResizing(true);\n      clearTimeout(resizeTimer);\n      resizeTimer = setTimeout(() => {\n        setIsResizing(false);\n      }, 300); // Reset the resizing flag after a timeout\n    };\n\n    const onScroll = () => {\n      if (isResizing) {\n        return; // Skip scroll events if resizing is in progress\n      }\n      const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      setIsScrollingUp(currentScrollTop < lastScrollTop);\n      setLastScrollTop(currentScrollTop);\n    };\n\n    window.addEventListener('scroll', onScroll);\n    window.addEventListener('resize', onResize);\n\n    return () => {\n      window.removeEventListener('scroll', onScroll);\n      window.removeEventListener('resize', onResize);\n    };\n  }, [lastScrollTop, isResizing]);\n\n  return isScrollingUp;\n};\n\ninterface UseHeadroomInput {\n  /** Number in px at which element should be fixed */\n  fixedAt?: number;\n\n  /** Called when element is pinned */\n  onPin?: () => void;\n\n  /** Called when element is at fixed position */\n  onFix?: () => void;\n\n  /** Called when element is unpinned */\n  onRelease?: () => void;\n}\n\nexport function useHeadroom({ fixedAt = 0, onPin, onFix, onRelease }: UseHeadroomInput = {}) {\n  const isCurrentlyPinnedRef = useRef(false);\n  const isScrollingUp = useScrollDirection();\n  const [{ y: scrollPosition }] = useWindowScroll();\n\n  useIsomorphicEffect(() => {\n    isPinnedOrReleased(\n      scrollPosition,\n      fixedAt,\n      isCurrentlyPinnedRef,\n      isScrollingUp,\n      onPin,\n      onRelease\n    );\n  }, [scrollPosition]);\n\n  useIsomorphicEffect(() => {\n    if (isFixed(scrollPosition, fixedAt)) {\n      onFix?.();\n    }\n  }, [scrollPosition, fixedAt, onFix]);\n\n  if (isFixed(scrollPosition, fixedAt) || isScrollingUp) {\n    return true;\n  }\n\n  return false;\n}\n", "import { useCallback, useState } from 'react';\nimport { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\n\ninterface EyeDropperOpenOptions {\n  signal?: AbortSignal;\n}\n\nexport interface EyeDropperOpenReturnType {\n  sRGBHex: string;\n}\n\nfunction isOpera() {\n  return navigator.userAgent.includes('OPR');\n}\n\nexport function useEyeDropper() {\n  const [supported, setSupported] = useState(false);\n\n  useIsomorphicEffect(() => {\n    setSupported(typeof window !== 'undefined' && !isOpera() && 'EyeDropper' in window);\n  }, []);\n\n  const open = useCallback(\n    (options: EyeDropperOpenOptions = {}): Promise<EyeDropperOpenReturnType | undefined> => {\n      if (supported) {\n        const eyeDropper = new (window as any).EyeDropper();\n        return eyeDropper.open(options);\n      }\n\n      return Promise.resolve(undefined);\n    },\n    [supported]\n  );\n\n  return { supported, open };\n}\n", "import { useCallback, useRef, useState } from 'react';\n\nexport function useInViewport<T extends HTMLElement = any>() {\n  const observer = useRef<IntersectionObserver | null>(null);\n  const [inViewport, setInViewport] = useState(false);\n\n  const ref = useCallback((node: T | null) => {\n    if (typeof IntersectionObserver !== 'undefined') {\n      if (node && !observer.current) {\n        observer.current = new IntersectionObserver((entries) =>\n          setInViewport(entries.some((entry) => entry.isIntersecting))\n        );\n      } else {\n        observer.current?.disconnect();\n      }\n\n      if (node) {\n        observer.current?.observe(node);\n      } else {\n        setInViewport(false);\n      }\n    }\n  }, []);\n\n  return { ref, inViewport };\n}\n", "import { useEffect, useRef } from 'react';\n\nexport function useMutationObserver<T extends HTMLElement = any>(\n  callback: MutationCallback,\n  options: MutationObserverInit,\n  target?: HTMLElement | (() => HTMLElement) | null\n) {\n  const observer = useRef<MutationObserver>(null);\n  const ref = useRef<T>(null);\n\n  useEffect(() => {\n    const targetElement = typeof target === 'function' ? target() : target;\n\n    if (targetElement || ref.current) {\n      observer.current = new MutationObserver(callback);\n      observer.current.observe(targetElement || ref.current!, options);\n    }\n\n    return () => {\n      observer.current?.disconnect();\n    };\n  }, [callback, options]);\n\n  return ref;\n}\n", "import { useEffect, useState } from 'react';\n\nexport function useMounted() {\n  const [mounted, setMounted] = useState(false);\n  useEffect(() => setMounted(true), []);\n  return mounted;\n}\n", "import { useCallback, useMemo, useState } from 'react';\n\nexport interface UseStateHistoryHandlers<T> {\n  set: (value: T) => void;\n  back: (steps?: number) => void;\n  forward: (steps?: number) => void;\n  reset: () => void;\n}\n\nexport interface StateHistory<T> {\n  history: T[];\n  current: number;\n}\n\nexport function useStateHistory<T>(\n  initialValue: T\n): [T, UseStateHistoryHandlers<T>, StateHistory<T>] {\n  const [state, setState] = useState<StateHistory<T>>({\n    history: [initialValue],\n    current: 0,\n  });\n\n  const set = useCallback(\n    (val: T) =>\n      setState((currentState) => {\n        const nextState = [...currentState.history.slice(0, currentState.current + 1), val];\n        return {\n          history: nextState,\n          current: nextState.length - 1,\n        };\n      }),\n    []\n  );\n\n  const back = useCallback(\n    (steps = 1) =>\n      setState((currentState) => ({\n        history: currentState.history,\n        current: Math.max(0, currentState.current - steps),\n      })),\n    []\n  );\n\n  const forward = useCallback(\n    (steps = 1) =>\n      setState((currentState) => ({\n        history: currentState.history,\n        current: Math.min(currentState.history.length - 1, currentState.current + steps),\n      })),\n    []\n  );\n\n  const reset = useCallback(() => {\n    setState({ history: [initialValue], current: 0 });\n  }, [initialValue]);\n\n  const handlers = useMemo(() => ({ back, forward, reset, set }), [back, forward, reset, set]);\n\n  return [state.history[state.current], handlers, state];\n}\n", "import { useRef } from 'react';\nimport { useForceUpdate } from '../use-force-update/use-force-update';\n\nexport function useMap<T, V>(initialState?: [T, V][]): Map<T, V> {\n  const mapRef = useRef(new Map<T, V>(initialState));\n  const forceUpdate = useForceUpdate();\n\n  mapRef.current.set = (...args) => {\n    Map.prototype.set.apply(mapRef.current, args);\n    forceUpdate();\n    return mapRef.current;\n  };\n\n  mapRef.current.clear = (...args) => {\n    Map.prototype.clear.apply(mapRef.current, args);\n    forceUpdate();\n  };\n\n  mapRef.current.delete = (...args) => {\n    const res = Map.prototype.delete.apply(mapRef.current, args);\n    forceUpdate();\n\n    return res;\n  };\n\n  return mapRef.current;\n}\n", "import { useRef } from 'react';\nimport { useForceUpdate } from '../use-force-update/use-force-update';\n\nexport function useSet<T>(values?: T[]): Set<T> {\n  const setRef = useRef(new Set(values));\n  const forceUpdate = useForceUpdate();\n\n  setRef.current.add = (...args) => {\n    const res = Set.prototype.add.apply(setRef.current, args);\n    forceUpdate();\n\n    return res;\n  };\n\n  setRef.current.clear = (...args) => {\n    Set.prototype.clear.apply(setRef.current, args);\n    forceUpdate();\n  };\n\n  setRef.current.delete = (...args) => {\n    const res = Set.prototype.delete.apply(setRef.current, args);\n    forceUpdate();\n\n    return res;\n  };\n\n  return setRef.current;\n}\n", "import { useCallback, useEffect, useRef } from 'react';\nimport { useCallbackRef } from '../use-callback-ref/use-callback-ref';\n\nexport function useThrottledCallbackWithClearTimeout<T extends (...args: any[]) => any>(\n  callback: T,\n  wait: number\n) {\n  const handleCallback = useCallbackRef(callback);\n  const latestInArgsRef = useRef<Parameters<T>>(null);\n  const latestOutArgsRef = useRef<Parameters<T>>(null);\n  const active = useRef(true);\n  const waitRef = useRef(wait);\n  const timeoutRef = useRef<number>(-1);\n\n  const clearTimeout = () => window.clearTimeout(timeoutRef.current);\n\n  const callThrottledCallback = useCallback(\n    (...args: Parameters<T>) => {\n      handleCallback(...args);\n      latestInArgsRef.current = args;\n      latestOutArgsRef.current = args;\n      active.current = false;\n    },\n    [handleCallback]\n  );\n\n  const timerCallback = useCallback(() => {\n    if (latestInArgsRef.current && latestInArgsRef.current !== latestOutArgsRef.current) {\n      callThrottledCallback(...latestInArgsRef.current);\n\n      timeoutRef.current = window.setTimeout(timerCallback, waitRef.current);\n    } else {\n      active.current = true;\n    }\n  }, [callThrottledCallback]);\n\n  const throttled = useCallback(\n    (...args: Parameters<T>) => {\n      if (active.current) {\n        callThrottledCallback(...args);\n        timeoutRef.current = window.setTimeout(timerCallback, waitRef.current);\n      } else {\n        latestInArgsRef.current = args;\n      }\n    },\n    [callThrottledCallback, timerCallback]\n  );\n\n  useEffect(() => {\n    waitRef.current = wait;\n  }, [wait]);\n\n  return [throttled, clearTimeout] as const;\n}\n\nexport function useThrottledCallback<T extends (...args: any[]) => any>(callback: T, wait: number) {\n  return useThrottledCallbackWithClearTimeout(callback, wait)[0];\n}\n", "import { useEffect, useState } from 'react';\nimport { useThrottledCallbackWithClearTimeout } from '../use-throttled-callback/use-throttled-callback';\n\nexport function useThrottledState<T = any>(defaultValue: T, wait: number) {\n  const [value, setValue] = useState(defaultValue);\n\n  const [setThrottledValue, clearTimeout] = useThrottledCallbackWithClearTimeout(setValue, wait);\n\n  useEffect(() => clearTimeout, []);\n\n  return [value, setThrottledValue] as const;\n}\n", "import { useEffect, useRef, useState } from 'react';\nimport { useThrottledCallbackWithClearTimeout } from '../use-throttled-callback/use-throttled-callback';\n\nexport function useThrottledValue<T>(value: T, wait: number) {\n  const [throttledValue, setThrottledValue] = useState(value);\n  const valueRef = useRef(value);\n\n  const [throttledSetValue, clearTimeout] = useThrottledCallbackWithClearTimeout(\n    setThrottledValue,\n    wait\n  );\n\n  useEffect(() => {\n    if (value !== valueRef.current) {\n      valueRef.current = value;\n      throttledSetValue(value);\n    }\n  }, [throttledSetValue, value]);\n\n  useEffect(() => clearTimeout, []);\n\n  return throttledValue;\n}\n", "import { useRef } from 'react';\n\nexport function useIsFirstRender() {\n  const renderRef = useRef(true);\n\n  if (renderRef.current === true) {\n    renderRef.current = false;\n    return true;\n  }\n\n  return renderRef.current;\n}\n", "import { useState } from 'react';\nimport { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\n\ninterface UseOrientationOptions {\n  /** Default angle value, used until the real can be retrieved\n   * (during server side rendering and before js executes on the page)\n   * If not provided, the default value is `0`\n   * */\n  defaultAngle?: number;\n\n  /** Default angle value, used until the real can be retrieved\n   * (during server side rendering and before js executes on the page)\n   * If not provided, the default value is `'landscape-primary'`\n   * */\n  defaultType?: OrientationType;\n\n  /** If true, the initial value will be resolved in useEffect (ssr safe)\n   *  If false, the initial value will be resolved in useLayoutEffect (ssr unsafe)\n   *  True by default.\n   */\n  getInitialValueInEffect?: boolean;\n}\n\ninterface UseOrientationReturnType {\n  angle: number;\n  type: OrientationType;\n}\n\nfunction getInitialValue(\n  initialValue: UseOrientationReturnType,\n  getInitialValueInEffect: boolean\n): UseOrientationReturnType {\n  if (getInitialValueInEffect) {\n    return initialValue;\n  }\n\n  if (typeof window !== 'undefined' && 'screen' in window) {\n    return {\n      angle: window.screen.orientation?.angle ?? initialValue.angle,\n      type: window.screen.orientation?.type ?? initialValue.type,\n    };\n  }\n\n  return initialValue;\n}\n\nexport function useOrientation({\n  defaultAngle = 0,\n  defaultType = 'landscape-primary',\n  getInitialValueInEffect = true,\n}: UseOrientationOptions = {}): UseOrientationReturnType {\n  const [orientation, setOrientation] = useState<UseOrientationReturnType>(\n    getInitialValue(\n      {\n        angle: defaultAngle,\n        type: defaultType,\n      },\n      getInitialValueInEffect\n    )\n  );\n\n  const handleOrientationChange = (event: Event) => {\n    const target = event.currentTarget as ScreenOrientation;\n    setOrientation({ angle: target?.angle || 0, type: target?.type || 'landscape-primary' });\n  };\n\n  useIsomorphicEffect(() => {\n    window.screen.orientation?.addEventListener('change', handleOrientationChange);\n    return () => window.screen.orientation?.removeEventListener('change', handleOrientationChange);\n  }, []);\n\n  return orientation;\n}\n", "import { useCallback, useEffect, useRef, useState } from 'react';\n\nexport interface UseFetchOptions extends RequestInit {\n  autoInvoke?: boolean;\n}\n\nexport function useFetch<T>(url: string, { autoInvoke = true, ...options }: UseFetchOptions = {}) {\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n  const controller = useRef<AbortController | null>(null);\n\n  const refetch = useCallback(() => {\n    if (!url) {\n      return;\n    }\n\n    if (controller.current) {\n      controller.current.abort();\n    }\n\n    controller.current = new AbortController();\n\n    setLoading(true);\n\n    return fetch(url, { signal: controller.current.signal, ...options })\n      .then((res) => res.json())\n      .then((res) => {\n        setData(res);\n        setLoading(false);\n        return res as T;\n      })\n      .catch((err) => {\n        setLoading(false);\n\n        if (err.name !== 'AbortError') {\n          setError(err);\n        }\n\n        return err;\n      });\n  }, [url]);\n\n  const abort = useCallback(() => {\n    if (controller.current) {\n      controller.current?.abort('');\n    }\n  }, []);\n\n  useEffect(() => {\n    if (autoInvoke) {\n      refetch();\n    }\n\n    return () => {\n      if (controller.current) {\n        controller.current.abort('');\n      }\n    };\n  }, [refetch, autoInvoke]);\n\n  return { data, loading, error, refetch, abort };\n}\n", "import { useEffect, useRef, useState } from 'react';\nimport { clamp } from '../utils';\n\nfunction radiansToDegrees(radians: number) {\n  return radians * (180 / Math.PI);\n}\n\nfunction getElementCenter(element: HTMLElement) {\n  const rect = element.getBoundingClientRect();\n  return [rect.left + rect.width / 2, rect.top + rect.height / 2];\n}\n\nfunction getAngle(coordinates: [number, number], element: HTMLElement) {\n  const center = getElementCenter(element);\n  const x = coordinates[0] - center[0];\n  const y = coordinates[1] - center[1];\n  const deg = radiansToDegrees(Math.atan2(x, y)) + 180;\n  return 360 - deg;\n}\n\nfunction toFixed(value: number, digits: number) {\n  return parseFloat(value.toFixed(digits));\n}\n\nfunction getDigitsAfterDot(value: number) {\n  return value.toString().split('.')[1]?.length || 0;\n}\n\nexport function normalizeRadialValue(degree: number, step: number) {\n  const clamped = clamp(degree, 0, 360);\n  const high = Math.ceil(clamped / step);\n  const low = Math.round(clamped / step);\n  return toFixed(\n    high >= clamped / step ? (high * step === 360 ? 0 : high * step) : low * step,\n    getDigitsAfterDot(step)\n  );\n}\n\nexport interface UseRadialMoveOptions {\n  /** Number by which value is incremented/decremented with mouse and touch events, `0.01` by default */\n  step?: number;\n\n  /** Called in `onMouseUp` and `onTouchEnd` events with the current value */\n  onChangeEnd?: (value: number) => void;\n\n  /** Called in `onMouseDown` and `onTouchStart` events */\n  onScrubStart?: () => void;\n\n  /** Called in `onMouseUp` and `onTouchEnd` events */\n  onScrubEnd?: () => void;\n}\n\nexport function useRadialMove<T extends HTMLElement = any>(\n  onChange: (value: number) => void,\n  { step = 0.01, onChangeEnd, onScrubStart, onScrubEnd }: UseRadialMoveOptions = {}\n) {\n  const ref = useRef<T>(null);\n  const mounted = useRef<boolean>(false);\n  const [active, setActive] = useState(false);\n\n  useEffect(() => {\n    mounted.current = true;\n  }, []);\n\n  useEffect(() => {\n    const node = ref.current;\n\n    const update = (event: MouseEvent, done = false) => {\n      if (node) {\n        node.style.userSelect = 'none';\n        const deg = getAngle([event.clientX, event.clientY], node);\n        const newValue = normalizeRadialValue(deg, step || 1);\n\n        onChange(newValue);\n        done && onChangeEnd?.(newValue);\n      }\n    };\n\n    const beginTracking = () => {\n      onScrubStart?.();\n      setActive(true);\n      document.addEventListener('mousemove', handleMouseMove, false);\n      document.addEventListener('mouseup', handleMouseUp, false);\n      document.addEventListener('touchmove', handleTouchMove, { passive: false });\n      document.addEventListener('touchend', handleTouchEnd, false);\n    };\n\n    const endTracking = () => {\n      onScrubEnd?.();\n      setActive(false);\n      document.removeEventListener('mousemove', handleMouseMove, false);\n      document.removeEventListener('mouseup', handleMouseUp, false);\n      document.removeEventListener('touchmove', handleTouchMove, false);\n      document.removeEventListener('touchend', handleTouchEnd, false);\n    };\n\n    const onMouseDown = (event: MouseEvent) => {\n      beginTracking();\n      update(event);\n    };\n\n    const handleMouseMove = (event: MouseEvent) => {\n      update(event);\n    };\n\n    const handleMouseUp = (event: MouseEvent) => {\n      update(event, true);\n      endTracking();\n    };\n\n    const handleTouchMove = (event: TouchEvent) => {\n      event.preventDefault();\n      update(event.touches[0] as any);\n    };\n\n    const handleTouchEnd = (event: TouchEvent) => {\n      update(event.changedTouches[0] as any, true);\n      endTracking();\n    };\n\n    const handleTouchStart = (event: TouchEvent) => {\n      event.preventDefault();\n      beginTracking();\n      update(event.touches[0] as any);\n    };\n\n    node?.addEventListener('mousedown', onMouseDown);\n    node?.addEventListener('touchstart', handleTouchStart, { passive: false });\n\n    return () => {\n      if (node) {\n        node.removeEventListener('mousedown', onMouseDown);\n        node.removeEventListener('touchstart', handleTouchStart);\n      }\n    };\n  }, [onChange]);\n\n  return { ref, active };\n}\n", "import { useEffect, useRef, useState } from 'react';\nimport { randomId } from '../utils';\n\nfunction getHeadingsData(\n  headings: HTMLElement[],\n  getDepth: (element: HTMLElement) => number,\n  getValue: (element: HTMLElement) => string\n): UseScrollSpyHeadingData[] {\n  const result: UseScrollSpyHeadingData[] = [];\n\n  for (let i = 0; i < headings.length; i += 1) {\n    const heading = headings[i];\n    result.push({\n      depth: getDepth(heading),\n      value: getValue(heading),\n      id: heading.id || randomId(),\n      getNode: () => (heading.id ? document.getElementById(heading.id)! : heading),\n    });\n  }\n\n  return result;\n}\n\nfunction getActiveElement(rects: DOMRect[]) {\n  if (rects.length === 0) {\n    return -1;\n  }\n\n  const closest = rects.reduce(\n    (acc, item, index) => {\n      if (Math.abs(acc.position) < Math.abs(item.y)) {\n        return acc;\n      }\n\n      return {\n        index,\n        position: item.y,\n      };\n    },\n    { index: 0, position: rects[0].y }\n  );\n\n  return closest.index;\n}\n\nfunction getDefaultDepth(element: HTMLElement) {\n  return Number(element.tagName[1]);\n}\n\nfunction getDefaultValue(element: HTMLElement) {\n  return element.textContent || '';\n}\n\nexport interface UseScrollSpyHeadingData {\n  /** Heading depth, 1-6 */\n  depth: number;\n\n  /** Heading text content value */\n  value: string;\n\n  /** Heading id */\n  id: string;\n\n  /** Function to get heading node */\n  getNode: () => HTMLElement;\n}\n\nexport interface UseScrollSpyOptions {\n  /** Selector to get headings, `'h1, h2, h3, h4, h5, h6'` by default */\n  selector?: string;\n\n  /** A function to retrieve depth of heading, by default depth is calculated based on tag name */\n  getDepth?: (element: HTMLElement) => number;\n\n  /** A function to retrieve heading value, by default `element.textContent` is used */\n  getValue?: (element: HTMLElement) => string;\n}\n\nexport interface UseScrollSpyReturnType {\n  /** Index of the active heading in the `data` array */\n  active: number;\n\n  /** Headings data. If not initialize, data is represented by an empty array. */\n  data: UseScrollSpyHeadingData[];\n\n  /** True if headings value have been retrieved from the DOM. */\n  initialized: boolean;\n\n  /** Function to update headings values after the parent component has mounted. */\n  reinitialize: () => void;\n}\n\nexport function useScrollSpy({\n  selector = 'h1, h2, h3, h4, h5, h6',\n  getDepth = getDefaultDepth,\n  getValue = getDefaultValue,\n}: UseScrollSpyOptions = {}): UseScrollSpyReturnType {\n  const [active, setActive] = useState(-1);\n  const [initialized, setInitialized] = useState(false);\n  const [data, setData] = useState<UseScrollSpyHeadingData[]>([]);\n  const headingsRef = useRef<UseScrollSpyHeadingData[]>([]);\n\n  const handleScroll = () => {\n    setActive(\n      getActiveElement(headingsRef.current.map((d) => d.getNode().getBoundingClientRect()))\n    );\n  };\n\n  const initialize = () => {\n    const headings = getHeadingsData(\n      Array.from(document.querySelectorAll(selector)),\n      getDepth,\n      getValue\n    );\n    headingsRef.current = headings;\n    setInitialized(true);\n    setData(headings);\n    setActive(getActiveElement(headings.map((d) => d.getNode().getBoundingClientRect())));\n  };\n\n  useEffect(() => {\n    initialize();\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return {\n    reinitialize: initialize,\n    active,\n    initialized,\n    data,\n  };\n}\n", "import { useCallback, useRef, useState } from 'react';\nimport { useIsomorphicEffect } from '../use-isomorphic-effect/use-isomorphic-effect';\n\nexport interface UseFileDialogOptions {\n  /** Determines whether multiple files are allowed, `true` by default */\n  multiple?: boolean;\n\n  /** `accept` attribute of the file input, '*' by default */\n  accept?: string;\n\n  /** `capture` attribute of the file input */\n  capture?: string;\n\n  /** Determines whether the user can pick a directory instead of file, `false` by default */\n  directory?: boolean;\n\n  /** Determines whether the file input state should be reset when the file dialog is opened, `false` by default */\n  resetOnOpen?: boolean;\n\n  /** Initial selected files */\n  initialFiles?: FileList | File[];\n\n  /** Called when files are selected */\n  onChange?: (files: FileList | null) => void;\n\n  /** Called when file dialog is canceled */\n  onCancel?: () => void;\n}\n\nconst defaultOptions: UseFileDialogOptions = {\n  multiple: true,\n  accept: '*',\n};\n\nfunction getInitialFilesList(files: UseFileDialogOptions['initialFiles']): FileList | null {\n  if (!files) {\n    return null;\n  }\n\n  if (files instanceof FileList) {\n    return files;\n  }\n\n  const result = new DataTransfer();\n  for (const file of files) {\n    result.items.add(file);\n  }\n\n  return result.files;\n}\n\nfunction createInput(options: UseFileDialogOptions) {\n  if (typeof document === 'undefined') {\n    return null;\n  }\n\n  const input = document.createElement('input');\n  input.type = 'file';\n\n  if (options.accept) {\n    input.accept = options.accept;\n  }\n\n  if (options.multiple) {\n    input.multiple = options.multiple;\n  }\n\n  if (options.capture) {\n    input.capture = options.capture;\n  }\n\n  if (options.directory) {\n    input.webkitdirectory = options.directory;\n  }\n\n  input.style.display = 'none';\n  return input;\n}\n\nexport function useFileDialog(input: UseFileDialogOptions = {}) {\n  const options: UseFileDialogOptions = { ...defaultOptions, ...input };\n  const [files, setFiles] = useState<FileList | null>(getInitialFilesList(options.initialFiles));\n  const inputRef = useRef<HTMLInputElement | null>(null);\n\n  const handleChange = useCallback(\n    (event: Event) => {\n      const target = event.target as HTMLInputElement;\n      if (target?.files) {\n        setFiles(target.files);\n        options.onChange?.(target.files);\n      }\n    },\n    [options.onChange]\n  );\n\n  const createAndSetupInput = useCallback(() => {\n    inputRef.current?.remove();\n    inputRef.current = createInput(options);\n\n    if (inputRef.current) {\n      inputRef.current.addEventListener('change', handleChange, { once: true });\n      document.body.appendChild(inputRef.current);\n    }\n  }, [options, handleChange]);\n\n  useIsomorphicEffect(() => {\n    createAndSetupInput();\n    return () => inputRef.current?.remove();\n  }, []);\n\n  const reset = useCallback(() => {\n    setFiles(null);\n    options.onChange?.(null);\n  }, [options.onChange]);\n\n  const open = useCallback(() => {\n    if (options.resetOnOpen) {\n      reset();\n    }\n\n    createAndSetupInput();\n    inputRef.current?.click();\n  }, [options.resetOnOpen, reset, createAndSetupInput]);\n\n  return { files, open, reset };\n}\n"], "mappings": ";;;;;;;;AAAgB,SAAA,MAAM,OAAe,KAAyB,KAAyB;AACjF,MAAA,QAAQ,UAAa,QAAQ,QAAW;AACnC,WAAA;EAAA;AAGL,MAAA,QAAQ,UAAa,QAAQ,QAAW;AACnC,WAAA,KAAK,IAAI,OAAO,GAAG;EAAA;AAGxB,MAAA,QAAQ,UAAa,QAAQ,QAAW;AACnC,WAAA,KAAK,IAAI,OAAO,GAAG;EAAA;AAG5B,SAAO,KAAK,IAAI,KAAK,IAAI,OAAO,GAAI,GAAG,GAAI;AAC7C;;;ACdO,SAAS,WAAW,OAAe;AACxC,SAAO,OAAO,UAAU,WAAW,KAAK,MAAM,OAAO,CAAC,EAAE,YAAY,IAAI,MAAM,MAAM,CAAC;AACvF;;;ACFgB,SAAA,SAAS,SAAS,YAAoB;AACpD,SAAO,GAAG,MAAM,GAAG,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC;AAC5D;;;ACFgB,SAAA,MAAM,OAAe,KAAa;AAChD,QAAM,SAAS,KAAK,IAAI,MAAM,KAAK,IAAI;AACvC,QAAM,WAAW,QAAQ;AAEzB,MAAI,CAAC,UAAU;AACN,WAAA,MAAM,KAAK,EAAE,OAAA,GAAU,CAAC,GAAG,UAAU,QAAQ,KAAK;EAAA;AAGpD,SAAA,MAAM,KAAK,EAAE,OAAA,GAAU,CAAC,GAAG,UAAU,QAAQ,KAAK;AAC3D;;;ACTgB,SAAA,aAAa,GAAQ,GAAQ;AAC3C,MAAI,MAAM,GAAG;AACJ,WAAA;EAAA;AAGT,MAAI,OAAO,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,GAAG;AAC/B,WAAA;EAAA;AAGT,MAAI,EAAE,aAAa,WAAW,EAAE,aAAa,SAAS;AAC7C,WAAA;EAAA;AAGH,QAAA,OAAO,OAAO,KAAK,CAAC;AACpB,QAAA,EAAE,OAAA,IAAW;AAEnB,MAAI,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AAC7B,WAAA;EAAA;AAGT,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,UAAA,MAAM,KAAK,CAAC;AAEd,QAAA,EAAE,OAAO,IAAI;AACR,aAAA;IAAA;AAGT,QAAI,EAAE,GAAG,MAAM,EAAE,GAAG,KAAK,EAAE,OAAO,MAAM,EAAE,GAAG,CAAC,KAAK,OAAO,MAAM,EAAE,GAAG,CAAC,IAAI;AACjE,aAAA;IAAA;EACT;AAGK,SAAA;AACT;;;ACjCO,SAAS,WAAW,OAAe;AACxC,SAAO,OAAO,UAAU,WAAW,KAAK,MAAM,OAAO,CAAC,EAAE,YAAY,IAAI,MAAM,MAAM,CAAC;AACvF;;;;ACAO,SAAS,eAAkD,UAA4B;AACtF,QAAA,kBAAc,qBAAO,QAAQ;AAEnC,8BAAU,MAAM;AACd,gBAAY,UAAU;EAAA,CACvB;AAEM,aAAA,sBAAQ,MAAO,IAAI,SAAS;;AAAA,6BAAY,YAAZ,qCAAsB,GAAG;KAAa,CAAA,CAAE;AAC7E;;;;ACPgB,SAAA,qBACd,UACA,SACA;AACA,QAAM,QAAQ,OAAO,YAAY,WAAW,UAAU,QAAQ;AAC9D,QAAM,iBAAiB,OAAO,YAAY,WAAW,QAAQ,QAAQ;AAC/D,QAAA,iBAAiB,eAAe,QAAQ;AACxC,QAAA,uBAAmB,sBAAO,CAAC;AAC3B,QAAA,eAAW,sBAAO,MAAM;EAAA,CAAE;AAEhC,QAAM,eAAe,OAAO;QAC1B;MACE,IAAI,SAAwB;AACnB,eAAA,aAAa,iBAAiB,OAAO;AAC5C,cAAM,QAAQ,MAAM;AACd,cAAA,iBAAiB,YAAY,GAAG;AAClC,6BAAiB,UAAU;AAC3B,2BAAe,GAAG,IAAI;UAAA;QAE1B;AACA,iBAAS,UAAU;AACnB,qBAAa,QAAQ;AACrB,yBAAiB,UAAU,OAAO,WAAW,OAAO,KAAK;MAC3D;MACA,CAAC,gBAAgB,KAAK;IACxB;IACA,EAAE,OAAO,SAAS,QAAQ;EAC5B;AAEA;IACE,MAAM,MAAM;AACH,aAAA,aAAa,iBAAiB,OAAO;AAC5C,UAAI,gBAAgB;AAClB,qBAAa,MAAM;MAAA;IAEvB;IACA,CAAC,cAAc,cAAc;EAC/B;AAEO,SAAA;AACT;;;;ACzCA,IAAM,iBAAiB,CAAC,aAAa,YAAY;AAEjC,SAAA,gBACd,SACA,QACA,OACA;AACM,QAAA,UAAM,sBAAU,IAAI;AAE1B,+BAAU,MAAM;AACR,UAAA,WAAW,CAAC,UAAe;AAC/B,YAAM,EAAE,OAAA,IAAW,SAAS,CAAC;AACzB,UAAA,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAM,gBACJ,iCAAQ,aAAa,kCACpB,CAAC,SAAS,KAAK,SAAS,MAAM,KAAK,OAAO,YAAY;AACzD,cAAM,gBAAgB,MAAM,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,MAAM,aAAA,EAAe,SAAS,IAAI,CAAC;AACzE,yBAAA,CAAC,gBAAgB,QAAQ;MAAA,WACjC,IAAI,WAAW,CAAC,IAAI,QAAQ,SAAS,MAAM,GAAG;AAC/C,gBAAA;MAAA;IAEZ;AAEC,KAAA,UAAU,gBAAgB,QAAQ,CAAC,OAAO,SAAS,iBAAiB,IAAI,QAAQ,CAAC;AAElF,WAAO,MAAM;AACV,OAAA,UAAU,gBAAgB,QAAQ,CAAC,OAAO,SAAS,oBAAoB,IAAI,QAAQ,CAAC;IACvF;EACC,GAAA,CAAC,KAAK,SAAS,KAAK,CAAC;AAEjB,SAAA;AACT;;;;AC/BO,SAAS,aAAa,EAAE,UAAU,IAAK,IAAI,CAAA,GAAI;AACpD,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAuB,IAAI;AACrD,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAAS,KAAK;AAC1C,QAAM,CAAC,aAAa,cAAc,QAAI,wBAAwB,IAAI;AAE5D,QAAA,mBAAmB,CAAC,UAAmB;AAC3C,WAAO,aAAa,WAAY;AAChC,mBAAe,OAAO,WAAW,MAAM,UAAU,KAAK,GAAG,OAAO,CAAC;AACjE,cAAU,KAAK;EACjB;AAEM,QAAA,OAAO,CAAC,gBAAqB;AACjC,QAAI,eAAe,WAAW;AAC5B,gBAAU,UACP,UAAU,WAAW,EACrB,KAAK,MAAM,iBAAiB,IAAI,CAAC,EACjC,MAAM,CAAC,QAAQ,SAAS,GAAG,CAAC;IAAA,OAC1B;AACI,eAAA,IAAI,MAAM,oDAAoD,CAAC;IAAA;EAE5E;AAEA,QAAM,QAAQ,MAAM;AAClB,cAAU,KAAK;AACf,aAAS,IAAI;AACb,WAAO,aAAa,WAAY;EAClC;AAEA,SAAO,EAAE,MAAM,OAAO,OAAO,OAAO;AACtC;;;;ACnBA,SAAS,oBAAoB,OAAuB,UAA8B;AAC5E,MAAA;AACI,UAAA,iBAAiB,UAAU,QAAQ;AACzC,WAAO,MAAM,MAAM,oBAAoB,UAAU,QAAQ;EAAA,SAClD,GAAG;AACV,UAAM,YAAY,QAAQ;AACnB,WAAA,MAAM,MAAM,eAAe,QAAQ;EAAA;AAE9C;AAEA,SAAS,gBAAgB,OAAe,cAAwB;AAK9D,MAAI,OAAO,WAAW,eAAe,gBAAgB,QAAQ;AACpD,WAAA,OAAO,WAAW,KAAK,EAAE;EAAA;AAG3B,SAAA;AACT;AAEO,SAAS,cACd,OACA,cACA,EAAE,wBAAA,IAAkD;EAClD,yBAAyB;AAC3B,GACA;AACM,QAAA,CAAC,SAAS,UAAU,QAAI;IAC5B,0BAA0B,eAAe,gBAAgB,KAAK;EAChE;AACM,QAAA,eAAW,sBAAuB,IAAI;AAE5C,+BAAU,MAAM;AACd,QAAI,gBAAgB,QAAQ;AACjB,eAAA,UAAU,OAAO,WAAW,KAAK;AAC/B,iBAAA,SAAS,QAAQ,OAAO;AAC5B,aAAA,oBAAoB,SAAS,SAAS,CAAC,UAAU,WAAW,MAAM,OAAO,CAAC;IAAA;AAG5E,WAAA;EAAA,GACN,CAAC,KAAK,CAAC;AAEH,SAAA;AACT;;;ACvDgB,SAAA,eAAe,cAAiC,SAAgC;AAC9F,SAAO,cAAc,gCAAgC,iBAAiB,QAAQ,OAAO,IACjF,SACA;AACN;;;;ACHA,IAAM,kBAAkB;EACtB,KAAK;EACL,KAAK;AACP;AAEgB,SAAA,WAAW,eAAe,GAAG,SAAiD;AACtF,QAAA,EAAE,KAAK,IAAI,IAAI,EAAE,GAAG,iBAAiB,GAAG,QAAQ;AAChD,QAAA,CAAC,OAAO,QAAQ,QAAI,wBAAiB,MAAM,cAAc,KAAK,GAAG,CAAC;AAElE,QAAA,YAAY,MAAM,SAAS,CAAC,YAAY,MAAM,UAAU,GAAG,KAAK,GAAG,CAAC;AACpE,QAAA,YAAY,MAAM,SAAS,CAAC,YAAY,MAAM,UAAU,GAAG,KAAK,GAAG,CAAC;AACpE,QAAA,MAAM,CAAC,UAAkB,SAAS,MAAM,OAAO,KAAK,GAAG,CAAC;AAC9D,QAAM,QAAQ,MAAM,SAAS,MAAM,cAAc,KAAK,GAAG,CAAC;AAE1D,SAAO,CAAC,OAAO,EAAE,WAAW,WAAW,KAAK,MAAA,CAAO;AACrD;;;;AChBO,SAAS,kBACd,cACA,MACA,UAAU,EAAE,SAAS,MAAA,GACrB;AACA,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,YAAY;AACzC,QAAA,iBAAa,sBAAsB,IAAI;AACvC,QAAA,iBAAa,sBAAO,IAAI;AAE9B,QAAMA,gBAAe,MAAM,OAAO,aAAa,WAAW,OAAQ;AACxD,+BAAA,MAAMA,eAAc,CAAA,CAAE;AAEhC,QAAM,wBAAoB;IACxB,CAAC,aAAgC;AAClB,MAAAA,cAAA;AACT,UAAA,WAAW,WAAW,QAAQ,SAAS;AACzC,iBAAS,QAAQ;MAAA,OACZ;AACM,mBAAA,UAAU,OAAO,WAAW,MAAM;AAC3C,qBAAW,UAAU;AACrB,mBAAS,QAAQ;QAAA,GAChB,IAAI;MAAA;AAET,iBAAW,UAAU;IACvB;IACA,CAAC,QAAQ,OAAO;EAClB;AAEO,SAAA,CAAC,OAAO,iBAAiB;AAClC;;;;AC7BO,SAAS,kBAA2B,OAAU,MAAc,UAAU,EAAE,SAAS,MAAA,GAAS;AAC/F,QAAM,CAAC,QAAQ,QAAQ,QAAI,wBAAS,KAAK;AACnC,QAAA,iBAAa,sBAAO,KAAK;AACzB,QAAA,iBAAa,sBAAsB,IAAI;AACvC,QAAA,kBAAc,sBAAO,KAAK;AAEhC,QAAM,SAAS,MAAM,OAAO,aAAa,WAAW,OAAQ;AAE5D,+BAAU,MAAM;AACd,QAAI,WAAW,SAAS;AACtB,UAAI,CAAC,YAAY,WAAW,QAAQ,SAAS;AAC3C,oBAAY,UAAU;AACtB,iBAAS,KAAK;MAAA,OACT;AACE,eAAA;AACI,mBAAA,UAAU,OAAO,WAAW,MAAM;AAC3C,sBAAY,UAAU;AACtB,mBAAS,KAAK;QAAA,GACb,IAAI;MAAA;IACT;EACF,GACC,CAAC,OAAO,QAAQ,SAAS,IAAI,CAAC;AAEjC,+BAAU,MAAM;AACd,eAAW,UAAU;AACd,WAAA;EACT,GAAG,CAAA,CAAE;AAEE,SAAA,CAAC,QAAQ,MAAM;AACxB;;;;AC3BO,IAAM,sBAAsB,OAAO,aAAa,cAAc,gCAAkB;;;ACFhF,SAAS,iBAAiB,OAAe;AAC9C,sBAAoB,MAAM;AACxB,QAAI,OAAO,UAAU,YAAY,MAAM,KAAK,EAAE,SAAS,GAAG;AAC/C,eAAA,QAAQ,MAAM,KAAK;IAAA;EAC9B,GACC,CAAC,KAAK,CAAC;AACZ;;;;ACNO,SAAS,wBAAiD;AAC/D,QAAM,CAAC,oBAAoB,qBAAqB,QAAI,yBAAkC,SAAS;AAE/F,gCAAU,MAAM;AACd,UAAM,WAAW,MAAM,sBAAsB,SAAS,eAAe;AAC5D,aAAA,iBAAiB,oBAAoB,QAAQ;AACtD,WAAO,MAAM,SAAS,oBAAoB,oBAAoB,QAAQ;EACxE,GAAG,CAAA,CAAE;AAEE,SAAA;AACT;;;;;;;ACVgB,SAAA,aAAa,IAAoB,cAA+B;AACxE,QAAA,cAAU,uBAAO,KAAK;AAE5B;IACE,MAAM,MAAM;AACV,cAAQ,UAAU;IACpB;IACA,CAAA;EACF;AAEA,gCAAU,MAAM;AACd,QAAI,QAAQ,SAAS;AACnB,aAAO,GAAG;IAAA;AAGZ,YAAQ,UAAU;AACX,WAAA;EAAA,GACN,YAAY;AACjB;;;ACXO,SAAS,eAAe,EAAE,QAAQ,oBAAoB,KAAA,GAAwB;AAC7E,QAAA,wBAAoB,uBAAoB,IAAI;AAClD,QAAM,cAAc,MAAM;;AAEtB,QAAA,kBAAkB,WAClB,WAAW,kBAAkB,WAC7B,OAAO,kBAAkB,QAAQ,UAAU,YAC3C;AACA,8BAAkB,YAAlB,mBAA2B,MAAM,EAAE,eAAe,KAAA;IAAM;EAE5D;AAEA,eAAa,MAAM;AACjB,QAAI,UAAU;AAER,UAAA,oBAAoB,CAAC,UAAyB;AAC9C,UAAA,MAAM,QAAQ,OAAO;AACvB,eAAO,aAAa,OAAO;MAAA;IAE/B;AAES,aAAA,iBAAiB,WAAW,iBAAiB;AAEtD,QAAI,QAAQ;AACV,wBAAkB,UAAU,SAAS;IAAA,WAC5B,mBAAmB;AAClB,gBAAA,OAAO,WAAW,aAAa,EAAE;IAAA;AAG7C,WAAO,MAAM;AACX,aAAO,aAAa,OAAO;AAClB,eAAA,oBAAoB,WAAW,iBAAiB;IAC3D;EAAA,GACC,CAAC,QAAQ,iBAAiB,CAAC;AAEvB,SAAA;AACT;;;;;;AC7CA,IAAM,iBAAiB;AAChB,IAAM,iBAAiB;AAE9B,SAAS,OAAO,SAAsB;AAChC,MAAA,OAAiC;AAC5B,WAAA;EAAA;AAGF,SAAA,QAAQ,MAAM,YAAY;AACnC;AAEA,SAAS,QAAQ,SAAsB;AACrC,QAAM,WACJ,QAAQ,aAAa,aAAa,KAClC,QAAQ,aAAa,QAAQ,KAC7B,QAAQ,aAAa,MAAM,MAAM;AAEnC,MAAI,UAAU;AACL,WAAA;EAAA;AAGT,MAAI,gBAA6B;AACjC,SAAO,eAAe;AACpB,QAAI,kBAAkB,SAAS,QAAQ,cAAc,aAAa,IAAI;AACpE;IAAA;AAGE,QAAA,OAAO,aAAa,GAAG;AAClB,aAAA;IAAA;AAGT,oBAAgB,cAAc;EAAA;AAGzB,SAAA;AACT;AAEA,SAAS,mBAAmB,SAAsB;AAC5C,MAAA,WAAsC,QAAQ,aAAa,UAAU;AACzE,MAAI,aAAa,MAAM;AACV,eAAA;EAAA;AAEN,SAAA,SAAS,UAAoB,EAAE;AACxC;AAEO,SAAS,UAAU,SAAsB;AACxC,QAAA,WAAW,QAAQ,SAAS,YAAY;AAC9C,QAAM,mBAAmB,CAAC,OAAO,MAAM,mBAAmB,OAAO,CAAC;AAC5D,QAAA;;IAEH,eAAe,KAAK,QAAQ,KAAK,CAAC,QAAQ,aAC1C,mBAAmB,oBAAoB,QAAQ,QAAQ,mBAAmB;;AAEtE,SAAA,OAAO,QAAQ,OAAO;AAC/B;AAEO,SAAS,SAAS,SAAsB;AACvC,QAAA,WAAW,mBAAmB,OAAO;AACrC,QAAA,gBAAgB,OAAO,MAAM,QAAQ;AAC3C,UAAQ,iBAAiB,YAAY,MAAM,UAAU,OAAO;AAC9D;AAEO,SAAS,wBAAwB,SAAqC;AACpE,SAAA,MAAM,KAAK,QAAQ,iBAA8B,cAAc,CAAC,EAAE,OAAO,QAAQ;AAC1F;;;AC9DgB,SAAA,SAAS,MAAmB,OAAsB;AAC1D,QAAAC,YAAW,wBAAwB,IAAI;AACzC,MAAA,CAACA,UAAS,QAAQ;AACpB,UAAM,eAAe;AACrB;EAAA;AAEF,QAAM,gBAAgBA,UAAS,MAAM,WAAW,IAAIA,UAAS,SAAS,CAAC;AACjE,QAAA,OAAO,KAAK,YAAY;AAC9B,MAAI,uBAAuB,kBAAkB,KAAK,iBAAiB,SAAS,KAAK;AAEjF,QAAM,gBAAgB,KAAK;AAC3B,QAAM,uBACJ,cAAc,YAAY,WAAW,cAAc,aAAa,MAAM,MAAM;AAC9E,MAAI,sBAAsB;AACxB,UAAM,mBAAmBA,UAAS;MAChC,CAAC,YACC,QAAQ,aAAa,MAAM,MAAM,WACjC,QAAQ,aAAa,MAAM,MAAM,cAAc,aAAa,MAAM;IACtE;AACuB,2BAAA,iBAAiB,SAAS,aAAa;EAAA;AAGhE,MAAI,CAAC,sBAAsB;AACzB;EAAA;AAGF,QAAM,eAAe;AAErB,QAAM,SAASA,UAAS,MAAM,WAAWA,UAAS,SAAS,IAAI,CAAC;AAEhE,MAAI,QAAQ;AACV,WAAO,MAAM;EAAA;AAEjB;;;AC/BgB,SAAA,aAAa,SAAS,MAA8C;AAC5E,QAAA,UAAM,uBAAoB,IAAI;AAE9B,QAAA,YAAY,CAAC,SAAsB;AACnC,QAAA,eAAmC,KAAK,cAAc,kBAAkB;AAE5E,QAAI,CAAC,cAAc;AACjB,YAAM,WAAW,MAAM,KAAkB,KAAK,iBAAiB,cAAc,CAAC;AAC9E,qBAAe,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK;AACtE,UAAI,CAAC,gBAAgB,UAAU,IAAI,GAAG;AACrB,uBAAA;MAAA;IACjB;AAGF,QAAI,cAAc;AAChB,mBAAa,MAAM,EAAE,eAAe,KAAA,CAAM;IACjC,WAAA,MAAwC;AAEzC,cAAA;QACN;QACA;MACF;IAAA;EAEJ;AAEA,QAAM,aAAS;IACb,CAAC,SAA6B;AAC5B,UAAI,CAAC,QAAQ;AACX;MAAA;AAGF,UAAI,SAAS,MAAM;AACjB;MAAA;AAGE,UAAA,IAAI,YAAY,MAAM;AACxB;MAAA;AAGF,UAAI,MAAM;AAER,mBAAW,MAAM;AACX,cAAA,KAAK,YAAA,GAAe;AACtB,sBAAU,IAAI;UACL,WAAA,MAAwC;AAEzC,oBAAA,KAAK,mEAAmE,IAAI;UAAA;QACtF,CACD;AAED,YAAI,UAAU;MAAA,OACT;AACL,YAAI,UAAU;MAAA;IAElB;IACA,CAAC,MAAM;EACT;AAEA,gCAAU,MAAM;AACd,QAAI,CAAC,QAAQ;AACJ,aAAA;IAAA;AAGT,QAAI,WAAW,WAAW,MAAM,UAAU,IAAI,OAAQ,CAAC;AAEjD,UAAA,gBAAgB,CAAC,UAAyB;AAC9C,UAAI,MAAM,QAAQ,SAAS,IAAI,SAAS;AAC7B,iBAAA,IAAI,SAAS,KAAK;MAAA;IAE/B;AAES,aAAA,iBAAiB,WAAW,aAAa;AAClD,WAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;EAAA,GACjE,CAAC,MAAM,CAAC;AAEJ,SAAA;AACT;;;;AC9EA,IAAM,UAAU,CAAC,WAAmB,QAAQ,KAAK;AAE1C,SAAS,iBAA6B;AAC3C,QAAM,CAAG,EAAA,MAAM,QAAI,2BAAW,SAAS,CAAC;AACjC,SAAA;AACT;;;;;;;ACLA,IAAM,UAAqC,eAAAC,QAAc,QAAQ,SAAS,CAAC,MAAM,MAAM;AAEhF,SAAS,aAAa;AAC3B,QAAM,KAAK,QAAQ;AACnB,SAAO,KAAK,WAAW,GAAG,QAAQ,MAAM,EAAE,CAAC,KAAK;AAClD;;;ACFO,SAAS,MAAM,UAAmB;AACvC,QAAM,UAAU,WAAW;AAC3B,QAAM,CAAC,MAAM,OAAO,QAAI,yBAAS,OAAO;AAExC,sBAAoB,MAAM;AACxB,YAAQ,SAAA,CAAU;EACpB,GAAG,CAAA,CAAE;AAED,MAAA,OAAO,aAAa,UAAU;AACzB,WAAA;EAAA;AAGL,MAAA,OAAO,WAAW,aAAa;AAC1B,WAAA;EAAA;AAGF,SAAA;AACT;;;;ACpBA,IAAMC,kBAA6C;EACjD;EACA;EACA;EACA;EACA;EACA;AACF;AACA,IAAMC,mBAAkB;EACtB,QAAQD;EACR,cAAc;AAChB;AAEgB,SAAA,QACd,SACA,SACA;AACM,QAAA,EAAE,QAAQ,aAAa,IAAI,EAAE,GAAGC,kBAAiB,GAAG,QAAQ;AAClE,QAAM,CAAC,MAAM,OAAO,QAAI,yBAAkB,YAAY;AAChD,QAAA,YAAQ,uBAAe,EAAE;AAE/B,gCAAU,MAAM;AACd,UAAM,eAAe,MAAM;AACzB,cAAQ,KAAK;AAEb,UAAI,MAAM,SAAS;AACV,eAAA,aAAa,MAAM,OAAO;MAAA;AAG7B,YAAA,UAAU,OAAO,WAAW,MAAM;AACtC,gBAAQ,IAAI;MAAA,GACX,OAAO;IACZ;AAEA,WAAO,QAAQ,CAAC,UAAU,SAAS,iBAAiB,OAAO,YAAY,CAAC;AAGlE,UAAA,UAAU,OAAO,WAAW,MAAM;AACtC,cAAQ,IAAI;IAAA,GACX,OAAO;AAEV,WAAO,MAAM;AACX,aAAO,QAAQ,CAAC,UAAU,SAAS,oBAAoB,OAAO,YAAY,CAAC;AACpE,aAAA,aAAa,MAAM,OAAO;AACjC,YAAM,UAAU;IAClB;EAAA,GACC,CAAC,OAAO,CAAC;AAEL,SAAA;AACT;;;;AC5CgB,SAAA,YACd,IACA,UACA,EAAE,aAAa,MAAM,IAAwB,CAAA,GAC7C;AACA,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,KAAK;AACpC,QAAA,kBAAc,uBAAsB,IAAI;AACxC,QAAA,YAAQ,uBAAmB,IAAI;AAErC,QAAM,QAAQ,MAAM;AAClB,cAAU,CAAC,QAAQ;AACjB,UAAI,CAAC,QAAQ,CAAC,YAAY,WAAW,YAAY,YAAY,KAAK;AAChE,oBAAY,UAAU,OAAO,YAAY,MAAM,SAAU,QAAQ;MAAA;AAE5D,aAAA;IAAA,CACR;EACH;AAEA,QAAM,OAAO,MAAM;AACjB,cAAU,KAAK;AACR,WAAA,cAAc,YAAY,WAAW,EAAE;AAC9C,gBAAY,UAAU;EACxB;AAEA,QAAM,SAAS,MAAM;AACnB,QAAI,QAAQ;AACL,WAAA;IAAA,OACA;AACC,YAAA;IAAA;EAEV;AAEA,gCAAU,MAAM;AACd,UAAM,UAAU;AAChB,cAAU,MAAM;AACT,WAAA;EACN,GAAA,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAEzB,gCAAU,MAAM;AACd,QAAI,YAAY;AACR,YAAA;IAAA;EAEV,GAAG,CAAA,CAAE;AAEL,SAAO,EAAE,OAAO,MAAM,QAAQ,OAAO;AACvC;;;;AC5BgB,SAAA,aAAgB,eAAoB,CAAA,GAAqB;AACvE,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAS,YAAY;AAEzC,QAAA,SAAS,IAAI,UAAe,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS,GAAG,KAAK,CAAC;AACxE,QAAA,UAAU,IAAI,UAAe,SAAS,CAAC,YAAY,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC;AAEzE,QAAA,SAAS,CAAC,UAAkB,UAChC,SAAS,CAAC,YAAY,CAAC,GAAG,QAAQ,MAAM,GAAG,KAAK,GAAG,GAAG,OAAO,GAAG,QAAQ,MAAM,KAAK,CAAC,CAAC;AAEvF,QAAM,QAAQ,CAAC,OACb,SAAS,CAAC,YAAY,QAAQ,IAAI,CAAC,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,CAAC;AAErE,QAAM,SAAS,IAAI,YACjB,SAAS,CAAC,YAAY,QAAQ,OAAO,CAAC,GAAG,UAAU,CAAC,QAAQ,SAAS,KAAK,CAAC,CAAC;AAE9E,QAAM,MAAM,MACV,SAAS,CAAC,YAAY;AACd,UAAA,SAAS,CAAC,GAAG,OAAO;AAC1B,WAAO,IAAI;AACJ,WAAA;EAAA,CACR;AAEH,QAAM,QAAQ,MACZ,SAAS,CAAC,YAAY;AACd,UAAA,SAAS,CAAC,GAAG,OAAO;AAC1B,WAAO,MAAM;AACN,WAAA;EAAA,CACR;AAEG,QAAA,UAAU,CAAC,EAAE,MAAM,GAAA,MACvB,SAAS,CAAC,YAAY;AACd,UAAA,SAAS,CAAC,GAAG,OAAO;AACpB,UAAA,OAAO,QAAQ,IAAI;AAElB,WAAA,OAAO,MAAM,CAAC;AACd,WAAA,OAAO,IAAI,GAAG,IAAI;AAElB,WAAA;EAAA,CACR;AAEG,QAAA,OAAO,CAAC,EAAE,MAAM,GAAA,MACpB,SAAS,CAAC,YAAY;AACd,UAAA,SAAS,CAAC,GAAG,OAAO;AACpB,UAAA,WAAW,OAAO,IAAI;AACtB,UAAA,SAAS,OAAO,EAAE;AAEjB,WAAA,OAAO,IAAI,GAAG,QAAQ;AACtB,WAAA,OAAO,MAAM,GAAG,MAAM;AAEtB,WAAA;EAAA,CACR;AAEH,QAAM,UAAU,CAAC,OAAe,SAC9B,SAAS,CAAC,YAAY;AACd,UAAA,SAAS,CAAC,GAAG,OAAO;AAC1B,WAAO,KAAK,IAAI;AACT,WAAA;EAAA,CACR;AAEH,QAAM,cAAc,CAAoC,OAAe,MAAS,UAC9E,SAAS,CAAC,YAAY;AACd,UAAA,SAAS,CAAC,GAAG,OAAO;AACnB,WAAA,KAAK,IAAI,EAAE,GAAG,OAAO,KAAK,GAAG,CAAC,IAAI,GAAG,MAAM;AAC3C,WAAA;EAAA,CACR;AAEG,QAAA,aAAa,CACjB,WACA,OAEA;IAAS,CAAC,YACR,QAAQ,IAAI,CAAC,MAAM,UAAW,UAAU,MAAM,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,IAAK;EAChF;AAEI,QAAA,SAAS,CAAC,OAAwC;AACtD,aAAS,CAAC,YAAY,QAAQ,OAAO,EAAE,CAAC;EAC1C;AAEO,SAAA;IACL;IACA;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;EAEJ;AACF;;;;;;;ACvHgB,SAAA,eACd,MACA,UAGA,SACA;AACA,gCAAU,MAAM;AACP,WAAA,iBAAiB,MAAa,UAAU,OAAO;AACtD,WAAO,MAAM,OAAO,oBAAoB,MAAa,UAAU,OAAO;EAAA,GACrE,CAAC,MAAM,QAAQ,CAAC;AACrB;;;ACaA,SAAS,cAAiB,OAAU,WAAmB,qBAAqB;AACtE,MAAA;AACK,WAAA,KAAK,UAAU,KAAK;EAAA,SACpB,OAAO;AACd,UAAM,IAAI,MAAM,kBAAkB,QAAQ,iCAAiC;EAAA;AAE/E;AAEA,SAAS,gBAAgB,OAA2B;AAC9C,MAAA;AACK,WAAA,SAAS,KAAK,MAAM,KAAK;EAAA,QAC1B;AACC,WAAA;EAAA;AAEX;AAEA,SAAS,qBAAqB,MAAmB;AACzC,QAAA,UAAU,CAAC,QAAgB;AAC3B,QAAA;AACF,aAAO,OAAO,IAAI,EAAE,QAAQ,GAAG;IAAA,SACxB,OAAO;AACd,cAAQ,KAAK,8EAA8E;AACpF,aAAA;IAAA;EAEX;AAEM,QAAA,UAAU,CAAC,KAAa,UAAkB;AAC1C,QAAA;AACF,aAAO,IAAI,EAAE,QAAQ,KAAK,KAAK;IAAA,SACxB,OAAO;AACd,cAAQ,KAAK,4EAA4E;IAAA;EAE7F;AAEM,QAAA,aAAa,CAAC,QAAgB;AAC9B,QAAA;AACK,aAAA,IAAI,EAAE,WAAW,GAAG;IAAA,SACpB,OAAO;AACN,cAAA;QACN;MACF;IAAA;EAEJ;AAEO,SAAA,EAAE,SAAS,SAAS,WAAW;AACxC;AAEgB,SAAA,cAAiB,MAAmB,UAAkB;AAC9D,QAAA,YAAY,SAAS,iBAAiB,0BAA0B;AACtE,QAAM,EAAE,SAAS,SAAS,WAAW,IAAI,qBAAqB,IAAI;AAElE,SAAO,SAAS,WAAW;IACzB;IACA;IACA,0BAA0B;IAC1B,OAAO;IACP,cAAc;IACd,YAAY,CAAC,UAAa,cAAc,OAAO,QAAQ;EAAA,GAChC;AACvB,UAAM,uBAAmB;MACvB,CAAC,gBAA6B;AACxB,YAAA;AAEA,YAAA;AAEA,oCAAA,OAAO,WAAW,eAClB,EAAE,QAAQ,WACV,OAAO,IAAI,MAAM,QACjB,CAAC,CAAC;QAAA,SACG,IAAI;AACe,oCAAA;QAAA;AAG5B,YAAI,yBAAyB;AACpB,iBAAA;QAAA;AAGH,cAAA,eAAe,QAAQ,GAAG;AAChC,eAAO,iBAAiB,OAAO,YAAY,YAAY,IAAK;MAC9D;MACA,CAAC,KAAK,YAAY;IACpB;AAEA,UAAM,CAAC,OAAO,QAAQ,QAAI,yBAAY,iBAAiB,uBAAuB,CAAC;AAE/E,UAAM,sBAAkB;MACtB,CAAC,QAAmC;AAClC,YAAI,eAAe,UAAU;AAC3B,mBAAS,CAAC,YAAY;AACd,kBAAA,SAAS,IAAI,OAAO;AAClB,oBAAA,KAAK,UAAU,MAAM,CAAC;AACvB,mBAAA;cACL,IAAI,YAAY,WAAW,EAAE,QAAQ,EAAE,KAAK,OAAO,IAAI,OAAO,EAAE,EAAG,CAAA;YACrE;AACO,mBAAA;UAAA,CACR;QAAA,OACI;AACG,kBAAA,KAAK,UAAU,GAAG,CAAC;AAC3B,iBAAO,cAAc,IAAI,YAAY,WAAW,EAAE,QAAQ,EAAE,KAAK,OAAO,IAAA,EAAO,CAAA,CAAC;AAChF,mBAAS,GAAG;QAAA;MAEhB;MACA,CAAC,GAAG;IACN;AAEM,UAAA,yBAAqB,4BAAY,MAAM;AAC3C,iBAAW,GAAG;AACd,aAAO,cAAc,IAAI,YAAY,WAAW,EAAE,QAAQ,EAAE,KAAK,OAAO,aAAA,EAAgB,CAAA,CAAC;IAC3F,GAAG,CAAA,CAAE;AAEU,mBAAA,WAAW,CAAC,UAAU;AACnC,UAAI,MAAM;AACR,YAAI,MAAM,gBAAgB,OAAO,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC3D,mBAAS,YAAY,MAAM,YAAY,MAAS,CAAC;QAAA;MACnD;IACF,CACD;AAEc,mBAAA,WAAW,CAAC,UAAU;AACnC,UAAI,MAAM;AACJ,YAAA,MAAM,OAAO,QAAQ,KAAK;AACnB,mBAAA,MAAM,OAAO,KAAK;QAAA;MAC7B;IACF,CACD;AAED,kCAAU,MAAM;AACV,UAAA,iBAAiB,UAAa,UAAU,QAAW;AACrD,wBAAgB,YAAY;MAAA;IAE7B,GAAA,CAAC,cAAc,OAAO,eAAe,CAAC;AAEzC,kCAAU,MAAM;AACd,YAAM,MAAM,iBAAiB;AACrB,cAAA,UAAa,gBAAgB,GAAG;IAAA,GACvC,CAAC,GAAG,CAAC;AAER,WAAO,CAAC,UAAU,SAAY,eAAe,OAAO,iBAAiB,kBAAkB;EAKzF;AACF;AAEO,SAAS,UAAU,MAAmB;AAC3C,QAAM,EAAE,QAAA,IAAY,qBAAqB,IAAI;AAE7C,SAAO,SAAS,KAAQ;IACtB;IACA;IACA,cAAc;EAAA,GACS;AACnB,QAAA;AAEA,QAAA;AAEA,gCAAA,OAAO,WAAW,eAAe,EAAE,QAAQ,WAAW,OAAO,IAAI,MAAM;IAAA,SAClE,IAAI;AACe,gCAAA;IAAA;AAG5B,QAAI,yBAAyB;AACpB,aAAA;IAAA;AAGH,UAAA,eAAe,QAAQ,GAAG;AAChC,WAAO,iBAAiB,OAAO,YAAY,YAAY,IAAK;EAC9D;AACF;;;ACjMO,SAAS,gBAA4B,OAA6B;AACvE,SAAO,cAAiB,gBAAgB,mBAAmB,EAAE,KAAK;AACpE;AAEa,IAAA,wBAAwB,UAAU,cAAc;;;ACJtD,SAAS,kBAA8B,OAA6B;AACzE,SAAO,cAAiB,kBAAkB,qBAAqB,EAAE,KAAK;AACxE;AAEa,IAAA,0BAA0B,UAAU,gBAAgB;;;;ACAjD,SAAA,UAAa,KAAqB,OAAyB;AACrE,MAAA,OAAO,QAAQ,YAAY;AAC7B,WAAO,IAAI,KAAK;EAAA,WACP,OAAO,QAAQ,YAAY,QAAQ,QAAQ,aAAa,KAAK;AACtE,QAAI,UAAU;EAAA;AAElB;AAEO,SAAS,aAAgB,MAAwB;AAChD,QAAA,aAAA,oBAAiB,IAAkD;AAEzE,SAAO,CAAC,SAAmB;AACpB,SAAA,QAAQ,CAAC,QAAQ;AACd,YAAA,UAAU,UAAU,KAAK,IAAI;AACnC,UAAI,SAAS;AACA,mBAAA,IAAI,KAAK,OAAO;MAAA;IAC7B,CACD;AAEG,QAAA,WAAW,OAAO,GAAG;AACvB,aAAO,MAAM;AACN,aAAA,QAAQ,CAAC,QAAQ;AACd,gBAAA,UAAU,WAAW,IAAI,GAAG;AAClC,cAAI,SAAS;AACH,oBAAA;UAAA,OACH;AACL,sBAAU,KAAK,IAAI;UAAA;QACrB,CACD;AACD,mBAAW,MAAM;MACnB;IAAA;EAEJ;AACF;AAEO,SAAS,gBAAmB,MAAwB;AACzD,aAAO,4BAAY,UAAU,GAAG,IAAI,GAAG,IAAI;AAC7C;;;;ACzCO,SAAS,SACd,UAAqC,EAAE,aAAa,MAAA,GACpD;AACM,QAAA,CAAC,UAAU,WAAW,QAAI,yBAAS,EAAE,GAAG,GAAG,GAAG,EAAA,CAAG;AAEjD,QAAA,UAAM,uBAAU,IAAI;AAEpB,QAAA,mBAAmB,CAAC,UAAmC;AAC3D,QAAI,IAAI,SAAS;AACT,YAAA,OAAO,MAAM,cAAc,sBAAsB;AAEvD,YAAM,IAAI,KAAK;QACb;QACA,KAAK,MAAM,MAAM,QAAQ,KAAK,QAAQ,OAAO,eAAe,OAAO,QAAQ;MAC7E;AAEA,YAAM,IAAI,KAAK;QACb;QACA,KAAK,MAAM,MAAM,QAAQ,KAAK,OAAO,OAAO,eAAe,OAAO,QAAQ;MAC5E;AAEY,kBAAA,EAAE,GAAG,EAAA,CAAG;IAAA,OACf;AACL,kBAAY,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAA,CAAS;IAAA;EAEtD;AAEM,QAAA,qBAAqB,MAAM,YAAY,EAAE,GAAG,GAAG,GAAG,EAAA,CAAG;AAE3D,gCAAU,MAAM;AACd,UAAM,WAAU,2BAAK,WAAU,IAAI,UAAU;AACrC,YAAA,iBAAiB,aAAa,gBAAuB;AAC7D,QAAI,QAAQ,aAAa;AACf,cAAA,iBAAiB,cAAc,kBAAyB;IAAA;AAGlE,WAAO,MAAM;AACH,cAAA,oBAAoB,aAAa,gBAAuB;AAChE,UAAI,QAAQ,aAAa;AACf,gBAAA,oBAAoB,cAAc,kBAAyB;MAAA;IAEvE;EAAA,GACC,CAAC,IAAI,OAAO,CAAC;AAET,SAAA,EAAE,KAAK,GAAG,SAAS;AAC5B;;;;ACvCO,SAAS,qBAAqB,UAA2B;AACvD,SAAA;IACL,GAAG,MAAM,SAAS,GAAG,GAAG,CAAC;IACzB,GAAG,MAAM,SAAS,GAAG,GAAG,CAAC;EAC3B;AACF;AAOO,SAAS,QACd,UACA,UACA,MAAqB,OACrB;AACM,QAAA,UAAM,uBAAU,IAAI;AACpB,QAAA,cAAU,uBAAgB,KAAK;AAC/B,QAAA,gBAAY,uBAAO,KAAK;AACxB,QAAA,YAAQ,uBAAO,CAAC;AACtB,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,KAAK;AAE1C,gCAAU,MAAM;AACd,YAAQ,UAAU;EACpB,GAAG,CAAA,CAAE;AAEL,gCAAU,MAAM;AACd,UAAM,OAAO,IAAI;AAEjB,UAAM,UAAU,CAAC,EAAE,GAAG,EAAA,MAAyB;AAC7C,2BAAqB,MAAM,OAAO;AAE5B,YAAA,UAAU,sBAAsB,MAAM;AACtC,YAAA,QAAQ,WAAW,MAAM;AAC3B,eAAK,MAAM,aAAa;AAClB,gBAAA,OAAO,KAAK,sBAAsB;AAEpC,cAAA,KAAK,SAAS,KAAK,QAAQ;AACvB,kBAAA,KAAK,OAAO,IAAI,KAAK,QAAQ,KAAK,OAAO,GAAG,CAAC;AAC1C,qBAAA;cACP,GAAG,QAAQ,QAAQ,KAAK,IAAI;cAC5B,GAAG,OAAO,IAAI,KAAK,OAAO,KAAK,QAAQ,GAAG,CAAC;YAAA,CAC5C;UAAA;QACH;MACF,CACD;IACH;AAEA,UAAM,aAAa,MAAM;AACd,eAAA,iBAAiB,aAAa,WAAW;AACzC,eAAA,iBAAiB,WAAW,aAAa;AACzC,eAAA,iBAAiB,aAAa,WAAW;AACzC,eAAA,iBAAiB,YAAY,aAAa;IACrD;AAEA,UAAM,eAAe,MAAM;AAChB,eAAA,oBAAoB,aAAa,WAAW;AAC5C,eAAA,oBAAoB,WAAW,aAAa;AAC5C,eAAA,oBAAoB,aAAa,WAAW;AAC5C,eAAA,oBAAoB,YAAY,aAAa;IACxD;AAEA,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,UAAU,WAAW,QAAQ,SAAS;AACzC,kBAAU,UAAU;AACpB,gBAAO,qCAAU,kBAAiB,cAAc,SAAS,aAAa;AACtE,kBAAU,IAAI;AACH,mBAAA;MAAA;IAEf;AAEA,UAAM,gBAAgB,MAAM;AACtB,UAAA,UAAU,WAAW,QAAQ,SAAS;AACxC,kBAAU,UAAU;AACpB,kBAAU,KAAK;AACF,qBAAA;AACb,mBAAW,MAAM;AACf,kBAAO,qCAAU,gBAAe,cAAc,SAAS,WAAW;QAAA,GACjE,CAAC;MAAA;IAER;AAEM,UAAA,cAAc,CAAC,UAAsB;AAC1B,qBAAA;AACf,YAAM,eAAe;AACrB,kBAAY,KAAK;IACnB;AAEM,UAAA,cAAc,CAAC,UAAsB,QAAQ,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAA,CAAS;AAEnF,UAAA,eAAe,CAAC,UAAsB;AAC1C,UAAI,MAAM,YAAY;AACpB,cAAM,eAAe;MAAA;AAGR,qBAAA;AACf,kBAAY,KAAK;IACnB;AAEM,UAAA,cAAc,CAAC,UAAsB;AACzC,UAAI,MAAM,YAAY;AACpB,cAAM,eAAe;MAAA;AAGvB,cAAQ,EAAE,GAAG,MAAM,eAAe,CAAC,EAAE,SAAS,GAAG,MAAM,eAAe,CAAC,EAAE,QAAA,CAAS;IACpF;AAEM,iCAAA,iBAAiB,aAAa;AACpC,iCAAM,iBAAiB,cAAc,cAAc,EAAE,SAAS,MAAA;AAE9D,WAAO,MAAM;AACX,UAAI,MAAM;AACH,aAAA,oBAAoB,aAAa,WAAW;AAC5C,aAAA,oBAAoB,cAAc,YAAY;MAAA;IAEvD;EAAA,GACC,CAAC,KAAK,QAAQ,CAAC;AAEX,SAAA,EAAE,KAAK,OAAO;AACvB;;;;;;;AChHO,SAAS,gBAAmB;EACjC;EACA;EACA;EACA,WAAW,MAAM;EAAA;AACnB,GAAiF;AACzE,QAAA,CAAC,mBAAmB,oBAAoB,QAAI;IAChD,iBAAiB,SAAY,eAAe;EAC9C;AAEM,QAAA,2BAA2B,CAAC,QAAW,YAAmB;AAC9D,yBAAqB,GAAG;AACb,yCAAA,KAAK,GAAG;EACrB;AAEA,MAAI,UAAU,QAAW;AAChB,WAAA,CAAC,OAAY,UAAU,IAAI;EAAA;AAG7B,SAAA,CAAC,mBAAwB,0BAA0B,KAAK;AACjE;;;ACjCA,SAASC,OAAM,OAAe,KAAa;AACnC,QAAA,SAAS,MAAM,QAAQ;AACtB,SAAA,MAAM,KAAK,EAAE,OAAA,GAAU,CAAC,GAAG,UAAU,QAAQ,KAAK;AAC3D;AAEO,IAAM,OAAO;AAsBb,SAAS,cAAc;EAC5B;EACA,WAAW;EACX,aAAa;EACb;EACA,cAAc;EACd;AACF,GAAqB;AACnB,QAAM,SAAS,KAAK,IAAI,KAAK,MAAM,KAAK,GAAG,CAAC;AAC5C,QAAM,CAAC,YAAY,aAAa,IAAI,gBAAgB;IAClD,OAAO;IACP;IACA,cAAc;IACd,YAAY;EAAA,CACb;AAEK,QAAA,UAAU,CAAC,eAAuB;AACtC,QAAI,cAAc,GAAG;AACnB,oBAAc,CAAC;IAAA,WACN,aAAa,QAAQ;AAC9B,oBAAc,MAAM;IAAA,OACf;AACL,oBAAc,UAAU;IAAA;EAE5B;AAEA,QAAM,OAAO,MAAM,QAAQ,aAAa,CAAC;AACzC,QAAM,WAAW,MAAM,QAAQ,aAAa,CAAC;AACvC,QAAA,QAAQ,MAAM,QAAQ,CAAC;AACvB,QAAA,OAAO,MAAM,QAAQ,MAAM;AAE3B,QAAA,sBAAkB,wBAAQ,MAA2B;AACzD,UAAM,mBAAmB,WAAW,IAAI,IAAI,aAAa;AACzD,QAAI,oBAAoB,QAAQ;AACvB,aAAAA,OAAM,GAAG,MAAM;IAAA;AAGxB,UAAM,mBAAmB,KAAK,IAAI,aAAa,UAAU,UAAU;AACnE,UAAM,oBAAoB,KAAK,IAAI,aAAa,UAAU,SAAS,UAAU;AAEvE,UAAA,qBAAqB,mBAAmB,aAAa;AACrD,UAAA,sBAAsB,oBAAoB,UAAU,aAAa;AAEnE,QAAA,CAAC,sBAAsB,qBAAqB;AACxC,YAAA,gBAAgB,WAAW,IAAI,aAAa;AAClD,aAAO,CAAC,GAAGA,OAAM,GAAG,aAAa,GAAG,MAAM,GAAGA,OAAM,UAAU,aAAa,IAAI,MAAM,CAAC;IAAA;AAGnF,QAAA,sBAAsB,CAAC,qBAAqB;AACxC,YAAA,iBAAiB,aAAa,IAAI,IAAI;AAC5C,aAAO,CAAC,GAAGA,OAAM,GAAG,UAAU,GAAG,MAAM,GAAGA,OAAM,SAAS,gBAAgB,MAAM,CAAC;IAAA;AAG3E,WAAA;MACL,GAAGA,OAAM,GAAG,UAAU;MACtB;MACA,GAAGA,OAAM,kBAAkB,iBAAiB;MAC5C;MACA,GAAGA,OAAM,SAAS,aAAa,GAAG,MAAM;IAC1C;EACC,GAAA,CAAC,QAAQ,UAAU,UAAU,CAAC;AAE1B,SAAA;IACL,OAAO;IACP,QAAQ;IACR;IACA;IACA;IACA;IACA;EACF;AACF;;;;ACnGO,SAAS,SAAY,EAAE,gBAAgB,CAAC,GAAG,MAAA,GAAiD;AACjG,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAS;IACjC,OAAO,cAAc,MAAM,GAAG,KAAK;IACnC,OAAO,cAAc,MAAM,KAAK;EAAA,CACjC;AAED,QAAM,MAAM,IAAI,UACd,SAAS,CAAC,YAAY;AACd,UAAA,UAAU,CAAC,GAAG,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,KAAK;AAEtD,WAAA;MACL,OAAO,QAAQ,MAAM,GAAG,KAAK;MAC7B,OAAO,QAAQ,MAAM,KAAK;IAC5B;EAAA,CACD;AAEH,QAAM,SAAS,CAAC,OACd,SAAS,CAAC,YAAY;AACd,UAAA,UAAU,GAAG,CAAC,GAAG,QAAQ,OAAO,GAAG,QAAQ,KAAK,CAAC;AAEhD,WAAA;MACL,OAAO,QAAQ,MAAM,GAAG,KAAK;MAC7B,OAAO,QAAQ,MAAM,KAAK;IAC5B;EAAA,CACD;AAEH,QAAM,aAAa,MAAM,SAAS,CAAC,aAAa,EAAE,OAAO,QAAQ,OAAO,OAAO,CAAA,EAAK,EAAA;AAE7E,SAAA;IACL,OAAO,MAAM;IACb,OAAO,MAAM;IACb;IACA;IACA;EACF;AACF;;;;ACnCO,SAAS,aAAa,aAAyB;AACpD,gCAAU,MAAM;AACL,aAAA,gBAAgB,iBAAiB,cAAc,WAAW;AACnE,WAAO,MAAM,SAAS,gBAAgB,oBAAoB,cAAc,WAAW;EACrF,GAAG,CAAA,CAAE;AACP;;;ACLgB,SAAA,iBAAiB,cAAwB,SAAgC;AAChF,SAAA,cAAc,oCAAoC,cAAc,OAAO;AAChF;;;;;;ACJa,IAAA,gBAAgB,CAAC,MAAe,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK;;;ACA/E,IAAM,sBAAsB,CAAC;EAClC;EACA;EACA;EACA;EACA;EACA;AACF,MAAmB;AACjB,MAAI,CAAC,UAAW,CAAC,UAAU,OAAO,aAAa,aAAc;AACpD,WAAA;EAAA;AAEH,QAAA,iBAAiB,CAAC,CAAC;AACnB,QAAA,gBAAgB,UAAU,SAAS;AACnC,QAAA,iBAAiB,cAAc,sBAAsB;AACrD,QAAA,iBAAiB,OAAO,sBAAsB;AAEpD,QAAM,UAAU,CAAC,aACf,eAAe,QAAQ,IAAI,eAAe,QAAQ;AAEpD,MAAI,SAAS,KAAK;AACV,UAAA,OAAO,QAAQ,KAAK;AAE1B,QAAI,SAAS,GAAG;AACP,aAAA;IAAA;AAGT,QAAI,cAAc,SAAS;AACzB,YAAM,WAAW,OAAO;AACxB,YAAM,eAAe,YAAY,eAAe,UAAU,SAAS,IAAI,MAAM,CAAC;AAE9E,aAAO,eAAe,WAAW;IAAA;AAGnC,UAAM,eAAe,iBAAiB,eAAe,SAAS,OAAO;AAErE,QAAI,cAAc,OAAO;AACvB,YAAM,WAAW,OAAO,SAAS,eAAe,eAAe;AACzD,YAAA,eAAe,YAAY,CAAC,eAAe,UAAU,SAAS,IAAI,MAAM,CAAC;AAE/E,aAAO,eAAe,WAAW;IAAA;AAGnC,QAAI,cAAc,UAAU;AAC1B,aAAO,OAAO,eAAe,IAAI,eAAe,SAAS;IAAA;AAGpD,WAAA;EAAA;AAGT,MAAI,SAAS,KAAK;AACV,UAAA,OAAO,QAAQ,MAAM;AAE3B,QAAI,SAAS,GAAG;AACP,aAAA;IAAA;AAGT,QAAI,cAAc,SAAS;AACzB,YAAM,WAAW,OAAO;AACxB,YAAM,eAAe,YAAY,eAAe,SAAS,CAAC;AAE1D,aAAO,eAAe,WAAW;IAAA;AAGnC,UAAM,cAAc,iBAAiB,eAAe,QAAQ,OAAO;AAEnE,QAAI,cAAc,OAAO;AACvB,YAAM,WAAW,OAAO,SAAS,cAAc,eAAe;AAC9D,YAAM,eAAe,YAAY,CAAC,eAAe,SAAS,CAAC;AAE3D,aAAO,eAAe,WAAW;IAAA;AAGnC,QAAI,cAAc,UAAU;AAC1B,aAAO,OAAO,cAAc,IAAI,eAAe,QAAQ;IAAA;AAGlD,WAAA;EAAA;AAGF,SAAA;AACT;;;AChFO,IAAM,iBAAiB,CAAC,EAAE,MAAM,OAAA,MAAkB;AACvD,MAAI,CAAC,UAAU,OAAO,aAAa,aAAa;AACvC,WAAA;EAAA;AAGH,QAAA,SAAS,SAAS,MAAM,cAAc;AAE5C,MAAI,QAAQ;AACV,WAAO,OAAO,MAAM;EAAA;AAGhB,QAAA,EAAE,MAAM,gBAAA,IAAoB;AAGlC,SAAO,KAAK,MAAM,IAAI,gBAAgB,MAAM;AAC9C;;;ACfO,IAAM,iBAAiB,CAAC,EAAE,MAAM,QAAQ,SAAA,MAAoB;AACjE,MAAI,CAAC,UAAU,OAAO,aAAa,aAAa;AAC9C;EAAA;AAGI,QAAA,SAAS,SAAS,MAAM,cAAc;AAE5C,MAAI,QAAQ;AACV,WAAO,MAAM,IAAI;EAAA,OACZ;AACC,UAAA,EAAE,MAAM,gBAAA,IAAoB;AAClC,SAAK,MAAM,IAAI;AACf,oBAAgB,MAAM,IAAI;EAAA;AAE9B;;;ACgCO,SAAS,kBAGd;EACA,WAAW;EACX,OAAO;EACP;EACA,SAAS;EACT,SAAS;EACT,aAAa;EACb,SAAS;AACX,IAA0B,CAAA,GAAI;AACtB,QAAA,cAAU,uBAAO,CAAC;AAClB,QAAA,gBAAY,uBAAO,CAAC;AACpB,QAAA,iBAAa,uBAAO,KAAK;AAEzB,QAAA,oBAAgB,uBAAe,IAAI;AACnC,QAAA,gBAAY,uBAAe,IAAI;AAErC,QAAM,gBAAgB,iBAAiB;AAEvC,QAAM,SAAS,MAAY;AACzB,QAAI,QAAQ,SAAS;AACnB,2BAAqB,QAAQ,OAAO;IAAA;EAExC;AAEA,QAAM,qBAAiB;IACrB,CAAC,EAAE,YAAY,QAAQ,IAA6B,CAAA,MAAO;AACzD,iBAAW,UAAU;AAErB,UAAI,QAAQ,SAAS;AACZ,eAAA;MAAA;AAGH,YAAA,QAAQ,eAAe,EAAE,QAAQ,cAAc,SAAS,KAAM,CAAA,KAAK;AAEzE,YAAM,SACJ,oBAAoB;QAClB,QAAQ,cAAc;QACtB,QAAQ,UAAU;QAClB;QACA;QACA;QACA;MACD,CAAA,KAAK,cAAc,UAAU,IAAI;AAEpC,eAAS,gBAAgB;AACnB,YAAA,UAAU,YAAY,GAAG;AACjB,oBAAA,UAAU,YAAY,IAAI;QAAA;AAGhC,cAAA,MAAM,YAAY,IAAI;AACtB,cAAA,UAAU,MAAM,UAAU;AAGhC,cAAM,IAAI,iBAAiB,aAAa,IAAI,IAAI,UAAU;AAE1D,cAAM,WAAW,QAAQ,SAAS,OAAO,CAAC;AAE3B,uBAAA;UACb,QAAQ,cAAc;UACtB;UACA;QAAA,CACD;AAED,YAAI,CAAC,WAAW,WAAW,IAAI,GAAG;AACxB,kBAAA,UAAU,sBAAsB,aAAa;QAAA,OAChD;AACE,iBAAA,mBAAmB,cAAc,eAAe;AACvD,oBAAU,UAAU;AACpB,kBAAQ,UAAU;AACX,iBAAA;QAAA;MACT;AAEY,oBAAA;IAChB;IACA,CAAC,MAAM,UAAU,QAAQ,QAAQ,QAAQ,gBAAgB,aAAa;EACxE;AAEA,QAAM,aAAa,MAAM;AACvB,QAAI,YAAY;AACd,iBAAW,UAAU;IAAA;EAEzB;AAQA,iBAAe,SAAS,YAAY;IAClC,SAAS;EAAA,CACV;AAED,iBAAe,aAAa,YAAY;IACtC,SAAS;EAAA,CACV;AAGS,gCAAA,MAAM,QAAQ,CAAA,CAAE;AAEnB,SAAA;IACL;IACA;IACA;IACA;EACF;AACF;;;;ACvJA,IAAM,eAA6B;EACjC,GAAG;EACH,GAAG;EACH,OAAO;EACP,QAAQ;EACR,KAAK;EACL,MAAM;EACN,QAAQ;EACR,OAAO;AACT;AAEO,SAAS,kBAA+C,SAAiC;AACxF,QAAA,cAAU,uBAAO,CAAC;AAClB,QAAA,UAAM,uBAAU,IAAI;AAE1B,QAAM,CAAC,MAAM,OAAO,QAAI,yBAAuB,YAAY;AAE3D,QAAM,eAAW;IACf,MACE,OAAO,WAAW,cACd,IAAI,eAAe,CAAC,YAAY;AACxB,YAAA,QAAQ,QAAQ,CAAC;AAEvB,UAAI,OAAO;AACT,6BAAqB,QAAQ,OAAO;AAE5B,gBAAA,UAAU,sBAAsB,MAAM;;AAC5C,cAAI,IAAI,SAAS;AACf,kBAAM,YAAU,WAAM,kBAAN,mBAAsB,SAAM,WAAM,mBAAN,mBAAuB;AACnE,gBAAI,SAAS;AACX,oBAAM,QAAQ,QAAQ;AACtB,oBAAM,SAAS,QAAQ;AAEf,sBAAA;gBACN;gBACA;gBACA,GAAG,MAAM,YAAY;gBACrB,GAAG,MAAM,YAAY;gBACrB,KAAK,MAAM,YAAY;gBACvB,MAAM,MAAM,YAAY;gBACxB,QAAQ,MAAM,YAAY;gBAC1B,OAAO,MAAM,YAAY;cAAA,CAC1B;YAAA,OACI;AACL,sBAAQ,MAAM,WAAW;YAAA;UAC3B;QACF,CACD;MAAA;IAEJ,CAAA,IACD;IACN,CAAA;EACF;AAEA,gCAAU,MAAM;AACd,QAAI,IAAI,SAAS;AACL,2CAAA,QAAQ,IAAI,SAAS;IAAO;AAGxC,WAAO,MAAM;AACX,2CAAU;AAEV,UAAI,QAAQ,SAAS;AACnB,6BAAqB,QAAQ,OAAO;MAAA;IAExC;EAAA,GACC,CAAC,IAAI,OAAO,CAAC;AAET,SAAA,CAAC,KAAK,IAAI;AACnB;AAEO,SAAS,eAA4C,SAAiC;AACrF,QAAA,CAAC,KAAK,EAAE,OAAO,OAAA,CAAQ,IAAI,kBAAqB,OAAO;AACtD,SAAA,EAAE,KAAK,OAAO,OAAO;AAC9B;;;;AC3EA,SAAS,eAAe,WAAyC,WAAkC;AAC7F,MAAA,CAAC,aAAa,CAAC,WAAW;AACrB,WAAA;EAAA;AAGT,MAAI,cAAc,WAAW;AACpB,WAAA;EAAA;AAGL,MAAA,UAAU,WAAW,UAAU,QAAQ;AAClC,WAAA;EAAA;AAGT,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK,GAAG;AACxC,QAAA,CAAC,aAAa,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG;AACtC,aAAA;IAAA;EACT;AAGK,SAAA;AACT;AAEA,SAAS,kBAAkB,cAAqC;AACxD,QAAA,UAAM,uBAAgD,CAAA,CAAE;AACxD,QAAA,gBAAY,uBAAe,CAAC;AAElC,MAAI,CAAC,eAAe,IAAI,SAAS,YAAY,GAAG;AAC9C,QAAI,UAAU;AACd,cAAU,WAAW;EAAA;AAGhB,SAAA,CAAC,UAAU,OAAO;AAC3B;AAEgB,SAAA,iBAAiB,IAAgB,cAA2C;AAChF,gCAAA,IAAI,kBAAkB,YAAY,CAAC;AAC/C;;;;ACrCO,SAAS,UAAuB,UAAwB,CAAC,OAAO,IAAI,GAAU;AAC7E,QAAA,CAAC,CAAC,MAAM,GAAG,MAAM,QAAI,2BAAW,CAAC,OAAY,WAAoC;AACrF,UAAM,QAAQ,kBAAkB,WAAW,OAAO,MAAM,CAAC,CAAC,IAAI;AAC9D,UAAM,QAAQ,KAAK,IAAI,MAAM,QAAQ,KAAK,CAAC;AAEpC,WAAA,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM,MAAM,GAAG,KAAK,CAAC;EAAA,GACrD,OAAc;AAEV,SAAA,CAAC,QAAQ,MAAmD;AACrE;;;;ACRA,IAAM,qBAAqB;EACzB,SAAS;AACX;AAEO,SAAS,kBAAkB;AAChC,QAAM,CAAC,YAAY,aAAa,QAAI,yBAAS;IAC3C,OAAO;IACP,QAAQ;EAAA,CACT;AAEK,QAAA,cAAU,4BAAY,MAAM;AAClB,kBAAA,EAAE,OAAO,OAAO,cAAc,GAAG,QAAQ,OAAO,eAAe,EAAA,CAAG;EAClF,GAAG,CAAA,CAAE;AAEU,iBAAA,UAAU,SAAS,kBAAkB;AACrC,iBAAA,qBAAqB,SAAS,kBAAkB;AACrD,gCAAA,SAAS,CAAA,CAAE;AAEd,SAAA;AACT;;;;ACdA,SAAS,oBAAoC;AAC3C,SAAO,OAAO,WAAW,cACrB,EAAE,GAAG,OAAO,aAAa,GAAG,OAAO,YACnC,IAAA,EAAE,GAAG,GAAG,GAAG,EAAE;AACnB;AAEA,SAAS,SAAS,EAAE,GAAG,EAAA,GAA8B;AAC/C,MAAA,OAAO,WAAW,aAAa;AAC3B,UAAA,gBAAiC,EAAE,UAAU,SAAS;AAExD,QAAA,OAAO,MAAM,UAAU;AACzB,oBAAc,OAAO;IAAA;AAGnB,QAAA,OAAO,MAAM,UAAU;AACzB,oBAAc,MAAM;IAAA;AAGtB,WAAO,SAAS,aAAa;EAAA;AAEjC;AAEO,SAAS,kBAAkB;AAC1B,QAAA,CAAC,UAAU,WAAW,QAAI,yBAAyB,EAAE,GAAG,GAAG,GAAG,EAAA,CAAG;AAEvE,iBAAe,UAAU,MAAM,YAAY,kBAAmB,CAAA,CAAC;AAC/D,iBAAe,UAAU,MAAM,YAAY,kBAAmB,CAAA,CAAC;AAE/D,gCAAU,MAAM;AACd,gBAAY,kBAAA,CAAmB;EACjC,GAAG,CAAA,CAAE;AAEE,SAAA,CAAC,UAAU,QAAQ;AAC5B;;;;ACvCO,SAAS,gBACd,SACA;AACA,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAA2C,IAAI;AAEnE,QAAA,eAAW,uBAAoC,IAAI;AAEzD,QAAM,UAAM;IACV,CAAC,YAAsB;AACrB,UAAI,SAAS,SAAS;AACpB,iBAAS,QAAQ,WAAW;AAC5B,iBAAS,UAAU;MAAA;AAGrB,UAAI,YAAY,MAAM;AACpB,iBAAS,IAAI;AACb;MAAA;AAGF,eAAS,UAAU,IAAI,qBAAqB,CAAC,CAAC,MAAM,MAAM;AACxD,iBAAS,MAAM;MAAA,GACd,OAAO;AAED,eAAA,QAAQ,QAAQ,OAAO;IAClC;IACA,CAAC,mCAAS,YAAY,mCAAS,MAAM,mCAAS,SAAS;EACzD;AAEO,SAAA,EAAE,KAAK,MAAM;AACtB;;;;ACxBO,SAAS,QAAQ,EAAE,0BAA0B,KAAK,IAAoB,CAAA,GAAI;AACzE,QAAA,CAAC,MAAM,OAAO,QAAI;IACtB,0BAA0B,KAAK,OAAO,SAAS,QAAQ;EACzD;AAEM,QAAA,iBAAiB,CAAC,UAAkB;AACxC,UAAM,gBAAgB,MAAM,WAAW,GAAG,IAAI,QAAQ,IAAI,KAAK;AAC/D,WAAO,SAAS,OAAO;AACvB,YAAQ,aAAa;EACvB;AAEA,iBAAe,cAAc,MAAM;AAC3B,UAAA,UAAU,OAAO,SAAS;AAChC,QAAI,SAAS,SAAS;AACpB,cAAQ,OAAO;IAAA;EACjB,CACD;AAED,gCAAU,MAAM;AACd,QAAI,yBAAyB;AACnB,cAAA,OAAO,SAAS,IAAI;IAAA;EAEhC,GAAG,CAAA,CAAE;AAEE,SAAA,CAAC,MAAM,cAAc;AAC9B;;;;;;ACjBA,IAAM,aAAqC;EACzC,KAAK;EACL,WAAW;EACX,YAAY;EACZ,SAAS;EACT,WAAW;EACX,QAAQ;EACR,KAAK;EACL,OAAO;EACP,KAAK;EACL,WAAW;EACX,QAAQ;EACR,QAAQ;EACR,MAAM;EACN,KAAK;EACL,QAAQ;EACR,UAAU;EACV,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACP;AAEA,SAAS,aAAa,KAAqB;AACzC,QAAM,WAAW,IAAI,QAAQ,OAAO,EAAE,EAAE,YAAY;AAC7C,SAAA,WAAW,GAAG,KAAK;AAC5B;AAEO,SAAS,YAAY,QAAwB;AAClD,QAAM,OAAO,OACV,YAAY,EACZ,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,KAAA,CAAM;AAE5B,QAAM,YAA+B;IACnC,KAAK,KAAK,SAAS,KAAK;IACxB,MAAM,KAAK,SAAS,MAAM;IAC1B,MAAM,KAAK,SAAS,MAAM;IAC1B,KAAK,KAAK,SAAS,KAAK;IACxB,OAAO,KAAK,SAAS,OAAO;IAC5B,MAAM,KAAK,SAAS,QAAQ;EAC9B;AAEA,QAAM,eAAe,CAAC,OAAO,QAAQ,QAAQ,SAAS,KAAK;AAErD,QAAA,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa,SAAS,GAAG,CAAC;AAEvD,SAAA;IACL,GAAG;IACH,KAAK,YAAY,WAAW,MAAM;EACpC;AACF;AAEA,SAAS,cAAc,QAAgB,OAAsB,iBAAoC;AAC/F,QAAM,EAAE,KAAK,MAAM,MAAM,KAAK,OAAO,IAAA,IAAQ;AACvC,QAAA,EAAE,QAAQ,SAAS,SAAS,UAAU,KAAK,YAAY,MAAM,YAAA,IAAgB;AAEnF,MAAI,QAAQ,QAAQ;AACX,WAAA;EAAA;AAGT,MAAI,KAAK;AACH,QAAA,CAAC,WAAW,CAAC,SAAS;AACjB,aAAA;IAAA;EACT,OACK;AACL,QAAI,SAAS,SAAS;AACb,aAAA;IAAA;AAET,QAAI,SAAS,SAAS;AACb,aAAA;IAAA;EACT;AAEF,MAAI,UAAU,UAAU;AACf,WAAA;EAAA;AAGT,MACE,QACC,kBACG,aAAa,WAAW,MAAM,aAAa,GAAG,IAC9C,aAAa,cAAc,WAAW,MAAM,aAAa,GAAG,IAChE;AACO,WAAA;EAAA;AAGF,SAAA;AACT;AAEgB,SAAA,iBAAiB,QAAgB,iBAA6C;AAC5F,SAAO,CAAC,UAAU,cAAc,YAAY,MAAM,GAAG,OAAO,eAAe;AAC7E;AASO,SAAS,iBAAiB,SAAuB;AACtD,SAAO,CAAC,UAA4D;AAClE,UAAM,SAAS,iBAAiB,QAAQ,MAAM,cAAc;AACpD,YAAA;MACN,CAAC,CAAC,QAAQ,SAAS,UAAU,EAAE,gBAAgB,MAAM,iBAAiB,MAAM,CAAC,MAAM;AACjF,YAAI,iBAAiB,QAAQ,QAAQ,eAAe,EAAE,MAAM,GAAG;AAC7D,cAAI,QAAQ,gBAAgB;AAC1B,kBAAM,eAAe;UAAA;AAGvB,kBAAQ,MAAM;QAAA;MAChB;IAEJ;EACF;AACF;;;AC1HA,SAAS,gBACP,OACA,cACA,2BAA2B,OAC3B;AACI,MAAA,MAAM,kBAAkB,aAAa;AACvC,QAAI,0BAA0B;AAC5B,aAAO,CAAC,aAAa,SAAS,MAAM,OAAO,OAAO;IAAA;AAG7C,WAAA,CAAC,MAAM,OAAO,qBAAqB,CAAC,aAAa,SAAS,MAAM,OAAO,OAAO;EAAA;AAGhF,SAAA;AACT;AAEgB,SAAA,WACd,SACA,eAAyB,CAAC,SAAS,YAAY,QAAQ,GACvD,2BAA2B,OAC3B;AACA,gCAAU,MAAM;AACR,UAAA,kBAAkB,CAAC,UAAyB;AACxC,cAAA;QACN,CAAC,CAAC,QAAQ,SAAS,UAAU,EAAE,gBAAgB,MAAM,iBAAiB,MAAM,CAAC,MAAM;AAE/E,cAAA,iBAAiB,QAAQ,QAAQ,eAAe,EAAE,KAAK,KACvD,gBAAgB,OAAO,cAAc,wBAAwB,GAC7D;AACA,gBAAI,QAAQ,gBAAgB;AAC1B,oBAAM,eAAe;YAAA;AAGvB,oBAAQ,KAAK;UAAA;QACf;MAEJ;IACF;AAES,aAAA,gBAAgB,iBAAiB,WAAW,eAAe;AACpE,WAAO,MAAM,SAAS,gBAAgB,oBAAoB,WAAW,eAAe;EAAA,GACnF,CAAC,OAAO,CAAC;AACd;;;;AChDA,SAAS,uBAA2C;AAClD,QAAM,YAAY,OAAO;AAEzB,QAAM,oBACJ,UAAU,qBACV,UAAU,2BACV,UAAU,wBACV,UAAU;AAEL,SAAA;AACT;AAEA,SAAS,iBAAiB;AACxB,QAAM,YAAY,OAAO;AAErB,MAAA,OAAO,UAAU,mBAAmB,YAAY;AAClD,WAAO,UAAU,eAAe;EAAA;AAE9B,MAAA,OAAO,UAAU,qBAAqB,YAAY;AACpD,WAAO,UAAU,iBAAiB;EAAA;AAEhC,MAAA,OAAO,UAAU,yBAAyB,YAAY;AACxD,WAAO,UAAU,qBAAqB;EAAA;AAEpC,MAAA,OAAO,UAAU,wBAAwB,YAAY;AACvD,WAAO,UAAU,oBAAoB;EAAA;AAGhC,SAAA;AACT;AAEA,SAAS,gBAAgB,SAAsB;;AAC7C,QAAM,WAAW;AAEjB,WACE,cAAS,sBAAT,wCACA,cAAS,wBAAT,wCACA,cAAS,0BAAT,wCACA,cAAS,4BAAT,wCACA,cAAS,yBAAT;AAEJ;AAEA,IAAM,WAAW,CAAC,IAAI,UAAU,OAAO,IAAI;AAE3C,SAAS,UACP,SACA;EACE;EACA;AACF,GACA;AACS,WAAA,QAAQ,CAAC,WAAW;AAC3B,YAAQ,iBAAiB,GAAG,MAAM,oBAAoB,YAAY;AAClE,YAAQ,iBAAiB,GAAG,MAAM,mBAAmB,OAAO;EAAA,CAC7D;AAED,SAAO,MAAM;AACF,aAAA,QAAQ,CAAC,WAAW;AAC3B,cAAQ,oBAAoB,GAAG,MAAM,oBAAoB,YAAY;AACrE,cAAQ,oBAAoB,GAAG,MAAM,mBAAmB,OAAO;IAAA,CAChE;EACH;AACF;AAEO,SAAS,gBAA6C;AAC3D,QAAM,CAAC,YAAY,aAAa,QAAI,yBAAkB,KAAK;AAErD,QAAA,WAAO,uBAAU,IAAI;AAE3B,QAAM,6BAAyB;IAC7B,CAAC,UAAiB;AACF,oBAAA,MAAM,WAAW,qBAAA,CAAsB;IACvD;IACA,CAAC,aAAa;EAChB;AAEA,QAAM,4BAAwB;IAC5B,CAAC,UAAiB;AAChB,oBAAc,KAAK;AAEX,cAAA;QACN,8EAA8E,KAAK,KAAK,MAAM,MAAM;MACtG;IACF;IACA,CAAC,aAAa;EAChB;AAEM,QAAA,aAAS,4BAAY,YAAY;AACjC,QAAA,CAAC,qBAAA,GAAwB;AACrB,YAAA,gBAAgB,KAAK,OAAQ;IAAA,OAC9B;AACL,YAAM,eAAe;IAAA;EAEzB,GAAG,CAAA,CAAE;AAEC,QAAA,UAAM,4BAAY,CAAC,YAAsB;AAC7C,QAAI,YAAY,MAAM;AACf,WAAA,UAAU,OAAO,SAAS;IAAA,OAC1B;AACL,WAAK,UAAU;IAAA;EAEnB,GAAG,CAAA,CAAE;AAEL,gCAAU,MAAM;AACd,QAAI,CAAC,KAAK,WAAW,OAAO,UAAU;AAC/B,WAAA,UAAU,OAAO,SAAS;AACxB,aAAA,UAAU,KAAK,SAAS;QAC7B,cAAc;QACd,SAAS;MAAA,CACV;IAAA;AAGH,QAAI,KAAK,SAAS;AACT,aAAA,UAAU,KAAK,SAAS;QAC7B,cAAc;QACd,SAAS;MAAA,CACV;IAAA;AAGI,WAAA;EAAA,GACN,CAAC,KAAK,OAAO,CAAC;AAEV,SAAA,EAAE,KAAK,QAAQ,WAAW;AACnC;;;;AC1HgB,SAAA,UAAU,eAAuB,OAAc;AAC7D,gCAAU,MAAM;AACd,YAAQ,IAAI,GAAG,aAAa,YAAY,GAAG,KAAK;AAChD,WAAO,MAAM,QAAQ,IAAI,GAAG,aAAa,YAAY;EACvD,GAAG,CAAA,CAAE;AAEL,eAAa,MAAM;AACjB,YAAQ,IAAI,GAAG,aAAa,YAAY,GAAG,KAAK;EAAA,GAC/C,KAAK;AAED,SAAA;AACT;;;;ACbO,SAAS,WAAwC;AACtD,QAAM,CAAC,SAAS,UAAU,QAAI,yBAAS,KAAK;AACtC,QAAA,UAAM,uBAAU,IAAI;AAC1B,QAAM,mBAAe,4BAAY,MAAM,WAAW,IAAI,GAAG,CAAA,CAAE;AAC3D,QAAM,mBAAe,4BAAY,MAAM,WAAW,KAAK,GAAG,CAAA,CAAE;AAE5D,gCAAU,MAAM;AACd,UAAM,OAAO,IAAI;AAEjB,QAAI,MAAM;AACH,WAAA,iBAAiB,cAAc,YAAY;AAC3C,WAAA,iBAAiB,cAAc,YAAY;AAEhD,aAAO,MAAM;AACL,qCAAA,oBAAoB,cAAc;AAClC,qCAAA,oBAAoB,cAAc;MAC1C;IAAA;AAGK,WAAA;EAAA,GACN,CAAC,IAAI,OAAO,CAAC;AAET,SAAA,EAAE,KAAK,QAAQ;AACxB;;;;ACvBgB,SAAA,kBACd,cACA,YACA,wBACA;AACA,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAY,YAAY;AAC5C,QAAA,CAAC,gBAAgB,iBAAiB,QAAI;IAC1C,WAAW,YAAY,IAAI,eAAe;EAC5C;AACM,QAAA,CAAC,OAAO,QAAQ,QAAI;IACxB,OAAO,2BAA2B,YAAY,yBAAyB,WAAW,YAAY;EAChG;AAEM,QAAA,WAAW,CAAC,QAAW;AACvB,QAAA,WAAW,GAAG,GAAG;AACnB,wBAAkB,GAAG;AACrB,eAAS,IAAI;IAAA,OACR;AACL,eAAS,KAAK;IAAA;AAGhB,aAAS,GAAG;EACd;AAEA,SAAO,CAAC,EAAE,OAAO,gBAAgB,MAAA,GAAS,QAAQ;AACpD;;;;ACtBA,SAAS,QAAQ,WAA4B;AAC3C,QAAM,eAAe;AAEd,SAAA,aAAa,KAAK,SAAS;AACpC;AAEA,SAAS,MAAM,WAA4B;AACzC,QAAM,aAAa;AAEZ,SAAA,WAAW,KAAK,SAAS;AAClC;AAEA,SAAS,UAAU,WAA4B;AAC7C,QAAM,iBAAiB;AAEhB,SAAA,eAAe,KAAK,SAAS;AACtC;AAEA,SAAS,UAAU,WAA4B;AAC7C,QAAM,iBAAiB;AAEhB,SAAA,eAAe,KAAK,SAAS;AACtC;AAEA,SAAS,QAAQ,WAA4B;AAC3C,QAAM,eAAe;AAEd,SAAA,aAAa,KAAK,SAAS;AACpC;AAEA,SAAS,QAAY;AACf,MAAA,OAAO,WAAW,aAAa;AAC1B,WAAA;EAAA;AAGH,QAAA,EAAE,UAAU,IAAI,OAAO;AAE7B,MAAI,MAAM,SAAS,KAAM,QAAQ,SAAS,KAAK,gBAAgB,UAAW;AACjE,WAAA;EAAA;AAEL,MAAA,QAAQ,SAAS,GAAG;AACf,WAAA;EAAA;AAEL,MAAA,UAAU,SAAS,GAAG;AACjB,WAAA;EAAA;AAEL,MAAA,UAAU,SAAS,GAAG;AACjB,WAAA;EAAA;AAEL,MAAA,QAAQ,SAAS,GAAG;AACf,WAAA;EAAA;AAGF,SAAA;AACT;AAMO,SAAS,MAAM,UAAwB,EAAE,kBAAkB,KAAA,GAAY;AACtE,QAAA,CAAC,OAAO,QAAQ,QAAI,yBAAa,QAAQ,mBAAmB,iBAAiB,MAAA,CAAO;AAE1F,sBAAoB,MAAM;AACxB,QAAI,QAAQ,kBAAkB;AAC5B,eAAS,KAAK;IAAA;EAElB,GAAG,CAAA,CAAE;AAEE,SAAA;AACT;;;;ACzEO,SAAS,YAA2C,cAAiB;AAC1E,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAS,YAAY;AAE/C,QAAM,gBAAY;IAChB,CAAC,iBACC,SAAS,CAAC,aAAa;MACrB,GAAG;MACH,GAAI,OAAO,iBAAiB,aAAa,aAAa,OAAO,IAAI;IAAA,EACjE;IACJ,CAAA;EACF;AAEO,SAAA,CAAC,OAAO,SAAS;AAC1B;;;;ACbO,SAAS,iBACd,UACA;AACA,SAAO,CAAC,QAA6E;AACnF,QAAI,CAAC,KAAK;AACR,eAAS,GAAQ;IAAA,WACR,OAAO,QAAQ,YAAY;AACpC,eAAS,GAAG;IACH,WAAA,OAAO,QAAQ,YAAY,iBAAiB,KAAK;AACpD,YAAA,EAAE,cAAA,IAAkB;AAEtB,UAAA,cAAc,SAAS,YAAY;AACrC,iBAAU,cAAsB,OAAc;MAAA,OACzC;AACL,iBAAS,cAAc,KAAY;MAAA;IACrC,OACK;AACL,eAAS,GAAG;IAAA;EAEhB;AACF;AAEO,SAAS,cAAiB,cAAiB;AAChD,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAY,YAAY;AAClD,SAAO,CAAC,OAAO,iBAAoB,QAAe,CAAC;AAIrD;;;;AC5BgB,SAAA,iBACd,MACA,UACA,SACA;AACM,QAAA,UAAM,uBAAU,IAAI;AAE1B,gCAAU,MAAM;AACd,UAAM,OAAO,IAAI;AAEjB,QAAI,MAAM;AACH,WAAA,iBAAiB,MAAM,UAAiB,OAAO;AACpD,aAAO,MAAM,6BAAM,oBAAoB,MAAM,UAAiB;IAAO;AAEhE,WAAA;EAAA,GACN,CAAC,UAAU,OAAO,CAAC;AAEf,SAAA;AACT;;;;AClBgB,SAAA,cACd,eAAe,OACf,WACA;AACA,QAAM,EAAE,QAAQ,QAAQ,IAAI,aAAa,CAAC;AAC1C,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,YAAY;AAE3C,QAAA,WAAO,4BAAY,MAAM;AAC7B,cAAU,CAAC,aAAa;AACtB,UAAI,CAAC,UAAU;AACJ;AACF,eAAA;MAAA;AAEF,aAAA;IAAA,CACR;EAAA,GACA,CAAC,MAAM,CAAC;AAEL,QAAA,YAAQ,4BAAY,MAAM;AAC9B,cAAU,CAAC,aAAa;AACtB,UAAI,UAAU;AACF;AACH,eAAA;MAAA;AAEF,aAAA;IAAA,CACR;EAAA,GACA,CAAC,OAAO,CAAC;AAEN,QAAA,aAAS,4BAAY,MAAM;AACtB,aAAA,MAAA,IAAU,KAAK;EACvB,GAAA,CAAC,OAAO,MAAM,MAAM,CAAC;AAExB,SAAO,CAAC,QAAQ,EAAE,MAAM,OAAO,OAAA,CAAQ;AACzC;;;;AC3BA,SAAS,sBAAsB,OAAmB;AAChD,MAAI,MAAM,yBAAyB,eAAe,MAAM,yBAAyB,aAAa;AAC5F,WAAO,MAAM,cAAc,SAAS,MAAM,aAAa;EAAA;AAGlD,SAAA;AACT;AAEO,SAAS,eAA4C;EAC1D;EACA;AACF,IAA2B,CAAA,GAAmD;AACtE,QAAA,UAAM,uBAAU,IAAI;AAC1B,QAAM,CAAC,SAAS,UAAU,QAAI,yBAAS,KAAK;AACtC,QAAA,iBAAa,uBAAO,KAAK;AAEzB,QAAA,cAAc,CAAC,UAAmB;AACtC,eAAW,KAAK;AAChB,eAAW,UAAU;EACvB;AAEM,QAAA,gBAAgB,CAAC,UAAsB;AACvC,QAAA,CAAC,WAAW,SAAS;AACvB,kBAAY,IAAI;AAChB,yCAAU;IAAK;EAEnB;AAEM,QAAA,iBAAiB,CAAC,UAAsB;AAC5C,QAAI,WAAW,WAAW,CAAC,sBAAsB,KAAK,GAAG;AACvD,kBAAY,KAAK;AACjB,uCAAS;IAAK;EAElB;AAEA,gCAAU,MAAM;AACd,UAAM,OAAO,IAAI;AAEjB,QAAI,MAAM;AACH,WAAA,iBAAiB,WAAW,aAAa;AACzC,WAAA,iBAAiB,YAAY,cAAc;AAEhD,aAAO,MAAM;AACL,qCAAA,oBAAoB,WAAW;AAC/B,qCAAA,oBAAoB,YAAY;MACxC;IAAA;AAGK,WAAA;EAAA,GACN,CAAC,eAAe,cAAc,CAAC;AAE3B,SAAA,EAAE,KAAgC,QAAQ;AACnD;;;;AC/CA,SAAS,gBAA+B;AAClC,MAAA,OAAO,cAAc,aAAa;AACpC,WAAO,CAAC;EAAA;AAGV,QAAM,aAAa;AACnB,QAAM,aACJ,WAAW,cAAc,WAAW,iBAAiB,WAAW;AAElE,MAAI,CAAC,YAAY;AACf,WAAO,CAAC;EAAA;AAGH,SAAA;IACL,UAAU,yCAAY;IACtB,aAAa,yCAAY;IACzB,eAAe,yCAAY;IAC3B,KAAK,yCAAY;IACjB,UAAU,yCAAY;IACtB,MAAM,yCAAY;EACpB;AACF;AAEO,SAAS,aAAa;AAC3B,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAA8C;IACxE,QAAQ;EAAA,CACT;AACD,QAAM,6BAAyB;IAC7B,MAAM,UAAU,CAAC,aAAa,EAAE,GAAG,SAAS,GAAG,cAAc,EAAA,EAAI;IACjE,CAAA;EACF;AAEe,iBAAA,UAAU,MAAM,UAAU,EAAE,QAAQ,MAAM,GAAG,cAAgB,EAAA,CAAC,CAAC;AAC/D,iBAAA,WAAW,MAAM,UAAU,EAAE,QAAQ,OAAO,GAAG,cAAgB,EAAA,CAAC,CAAC;AAEhF,gCAAU,MAAM;AACd,UAAM,aAAa;AAEnB,QAAI,WAAW,YAAY;AACzB,gBAAU,EAAE,QAAQ,WAAW,QAAQ,GAAG,cAAA,EAAA,CAAiB;AAChD,iBAAA,WAAW,iBAAiB,UAAU,sBAAsB;AACvE,aAAO,MAAM,WAAW,WAAW,oBAAoB,UAAU,sBAAsB;IAAA;AAGrF,QAAA,OAAO,WAAW,WAAW,WAAW;AAEhC,gBAAA,CAAC,aAAa,EAAE,GAAG,SAAS,QAAQ,WAAW,OAAA,EAAS;IAAA;AAG7D,WAAA;EACT,GAAG,CAAA,CAAE;AAEE,SAAA;AACT;;;;AC/DO,SAAS,WACd,UACA,OACA,UAAmC,EAAE,YAAY,MAAA,GACjD;AACM,QAAA,iBAAa,uBAAsB,IAAI;AAE7C,QAAM,YAAQ;IACZ,IAAI,mBAA0B;AACxB,UAAA,CAAC,WAAW,SAAS;AACZ,mBAAA,UAAU,OAAO,WAAW,MAAM;AAC3C,mBAAS,cAAc;AACvB,qBAAW,UAAU;QAAA,GACpB,KAAK;MAAA;IAEZ;IACA,CAAC,KAAK;EACR;AAEM,QAAA,YAAQ,4BAAY,MAAM;AAC9B,QAAI,WAAW,SAAS;AACf,aAAA,aAAa,WAAW,OAAO;AACtC,iBAAW,UAAU;IAAA;EAEzB,GAAG,CAAA,CAAE;AAEL,gCAAU,MAAM;AACd,QAAI,QAAQ,YAAY;AAChB,YAAA;IAAA;AAGD,WAAA;EAAA,GACN,CAAC,OAAO,KAAK,CAAC;AAEV,SAAA,EAAE,OAAO,MAAM;AACxB;;;;AClCO,SAAS,mBAAqC;AACnD,QAAM,cAAc,eAAe;AACnC,QAAM,CAAC,WAAW,YAAY,QAAI,yBAA2B,IAAI;AAEjE,QAAM,wBAAwB,MAAM;AACrB,iBAAA,SAAS,aAAA,CAAc;AACxB,gBAAA;EACd;AAEA,gCAAU,MAAM;AACD,iBAAA,SAAS,aAAA,CAAc;AAC3B,aAAA,iBAAiB,mBAAmB,qBAAqB;AAClE,WAAO,MAAM,SAAS,oBAAoB,mBAAmB,qBAAqB;EACpF,GAAG,CAAA,CAAE;AAEE,SAAA;AACT;;;;ACjBO,SAAS,YAAe,OAAyB;AAChD,QAAA,UAAM,uBAAU,MAAS;AAE/B,gCAAU,MAAM;AACd,QAAI,UAAU;EAAA,GACb,CAAC,KAAK,CAAC;AAEV,SAAO,IAAI;AACb;;;;ACPA,IAAM,aAAqC;EACzC,KAAK;EACL,KAAK;EACL,KAAK;EACL,KAAK;AACP;AAEO,SAAS,WAAW,KAAa;AAChC,QAAA,WAAO,uBAAwB,IAAI;AAEzC,sBAAoB,MAAM;AACxB,QAAI,CAAC,KAAK;AACR;IAAA;AAGE,QAAA,CAAC,KAAK,SAAS;AACX,YAAA,mBAAmB,SAAS,iBAAkC,mBAAmB;AACvF,uBAAiB,QAAQ,CAACC,aAAY,SAAS,KAAK,YAAYA,QAAO,CAAC;AAElE,YAAA,UAAU,SAAS,cAAc,MAAM;AAC7C,cAAQ,MAAM;AACd,WAAK,UAAU;AACf,eAAS,cAAc,MAAM,EAAG,YAAY,OAAO;IAAA;AAG/C,UAAA,cAAc,IAAI,MAAM,GAAG;AACjC,SAAK,QAAQ;MACX;MACA,WAAW,YAAY,YAAY,SAAS,CAAC,EAAE,YAAa,CAAA;IAC9D;AACK,SAAA,QAAQ,aAAa,QAAQ,GAAG;EAAA,GACpC,CAAC,GAAG,CAAC;AACV;;;;AC/BO,IAAM,UAAU,CAAC,SAAiB,YAAoB,WAAW;AAKjE,IAAM,qBAAqB,CAChC,SACA,SACA,sBACA,eACA,OACA,cACG;AACG,QAAA,oBAAoB,QAAQ,SAAS,OAAO;AAC9C,MAAA,qBAAqB,CAAC,qBAAqB,SAAS;AACtD,yBAAqB,UAAU;AACvB;EAAA,WACC,CAAC,qBAAqB,iBAAiB,CAAC,qBAAqB,SAAS;AAC/E,yBAAqB,UAAU;AACvB;EACC,WAAA,CAAC,qBAAqB,qBAAqB,SAAS;AAC7D,yBAAqB,UAAU;AACnB;EAAA;AAEhB;AAEO,IAAM,qBAAqB,MAAM;AACtC,QAAM,CAAC,eAAe,gBAAgB,QAAI,yBAAS,CAAC;AACpD,QAAM,CAAC,eAAe,gBAAgB,QAAI,yBAAS,KAAK;AACxD,QAAM,CAAC,YAAY,aAAa,QAAI,yBAAS,KAAK;AAElD,gCAAU,MAAM;AACV,QAAA;AAEJ,UAAM,WAAW,MAAM;AACrB,oBAAc,IAAI;AAClB,mBAAa,WAAW;AACxB,oBAAc,WAAW,MAAM;AAC7B,sBAAc,KAAK;MAAA,GAClB,GAAG;IACR;AAEA,UAAM,WAAW,MAAM;AACrB,UAAI,YAAY;AACd;MAAA;AAEF,YAAM,mBAAmB,OAAO,eAAe,SAAS,gBAAgB;AACxE,uBAAiB,mBAAmB,aAAa;AACjD,uBAAiB,gBAAgB;IACnC;AAEO,WAAA,iBAAiB,UAAU,QAAQ;AACnC,WAAA,iBAAiB,UAAU,QAAQ;AAE1C,WAAO,MAAM;AACJ,aAAA,oBAAoB,UAAU,QAAQ;AACtC,aAAA,oBAAoB,UAAU,QAAQ;IAC/C;EAAA,GACC,CAAC,eAAe,UAAU,CAAC;AAEvB,SAAA;AACT;AAgBgB,SAAA,YAAY,EAAE,UAAU,GAAG,OAAO,OAAO,UAAgC,IAAA,CAAA,GAAI;AACrF,QAAA,2BAAuB,uBAAO,KAAK;AACzC,QAAM,gBAAgB,mBAAmB;AACzC,QAAM,CAAC,EAAE,GAAG,eAAgB,CAAA,IAAI,gBAAgB;AAEhD,sBAAoB,MAAM;AACxB;MACE;MACA;MACA;MACA;MACA;MACA;IACF;EAAA,GACC,CAAC,cAAc,CAAC;AAEnB,sBAAoB,MAAM;AACpB,QAAA,QAAQ,gBAAgB,OAAO,GAAG;AAC5B;IAAA;EAET,GAAA,CAAC,gBAAgB,SAAS,KAAK,CAAC;AAEnC,MAAI,QAAQ,gBAAgB,OAAO,KAAK,eAAe;AAC9C,WAAA;EAAA;AAGF,SAAA;AACT;;;;ACjGA,SAAS,UAAU;AACV,SAAA,UAAU,UAAU,SAAS,KAAK;AAC3C;AAEO,SAAS,gBAAgB;AAC9B,QAAM,CAAC,WAAW,YAAY,QAAI,yBAAS,KAAK;AAEhD,sBAAoB,MAAM;AACxB,iBAAa,OAAO,WAAW,eAAe,CAAC,QAAQ,KAAK,gBAAgB,MAAM;EACpF,GAAG,CAAA,CAAE;AAEL,QAAM,WAAO;IACX,CAAC,UAAiC,CAAA,MAAsD;AACtF,UAAI,WAAW;AACP,cAAA,aAAa,IAAK,OAAe,WAAW;AAC3C,eAAA,WAAW,KAAK,OAAO;MAAA;AAGzB,aAAA,QAAQ,QAAQ,MAAS;IAClC;IACA,CAAC,SAAS;EACZ;AAEO,SAAA,EAAE,WAAW,KAAK;AAC3B;;;;ACjCO,SAAS,gBAA6C;AACrD,QAAA,eAAW,uBAAoC,IAAI;AACzD,QAAM,CAAC,YAAY,aAAa,QAAI,yBAAS,KAAK;AAE5C,QAAA,UAAM,4BAAY,CAAC,SAAmB;;AACtC,QAAA,OAAO,yBAAyB,aAAa;AAC3C,UAAA,QAAQ,CAAC,SAAS,SAAS;AAC7B,iBAAS,UAAU,IAAI;UAAqB,CAAC,YAC3C,cAAc,QAAQ,KAAK,CAAC,UAAU,MAAM,cAAc,CAAC;QAC7D;MAAA,OACK;AACL,uBAAS,YAAT,mBAAkB;MAAW;AAG/B,UAAI,MAAM;AACC,uBAAA,YAAA,mBAAS,QAAQ;MAAI,OACzB;AACL,sBAAc,KAAK;MAAA;IACrB;EAEJ,GAAG,CAAA,CAAE;AAEE,SAAA,EAAE,KAAK,WAAW;AAC3B;;;;ACvBgB,SAAA,oBACd,UACA,SACA,QACA;AACM,QAAA,eAAW,uBAAyB,IAAI;AACxC,QAAA,UAAM,uBAAU,IAAI;AAE1B,gCAAU,MAAM;AACd,UAAM,gBAAgB,OAAO,WAAW,aAAa,OAAW,IAAA;AAE5D,QAAA,iBAAiB,IAAI,SAAS;AACvB,eAAA,UAAU,IAAI,iBAAiB,QAAQ;AAChD,eAAS,QAAQ,QAAQ,iBAAiB,IAAI,SAAU,OAAO;IAAA;AAGjE,WAAO,MAAM;;AACX,qBAAS,YAAT,mBAAkB;IACpB;EAAA,GACC,CAAC,UAAU,OAAO,CAAC;AAEf,SAAA;AACT;;;;ACtBO,SAAS,aAAa;AAC3B,QAAM,CAAC,SAAS,UAAU,QAAI,yBAAS,KAAK;AAC5C,gCAAU,MAAM,WAAW,IAAI,GAAG,CAAA,CAAE;AAC7B,SAAA;AACT;;;;ACQO,SAAS,gBACd,cACkD;AAClD,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAA0B;IAClD,SAAS,CAAC,YAAY;IACtB,SAAS;EAAA,CACV;AAED,QAAM,UAAM;IACV,CAAC,QACC,SAAS,CAAC,iBAAiB;AACnB,YAAA,YAAY,CAAC,GAAG,aAAa,QAAQ,MAAM,GAAG,aAAa,UAAU,CAAC,GAAG,GAAG;AAC3E,aAAA;QACL,SAAS;QACT,SAAS,UAAU,SAAS;MAC9B;IAAA,CACD;IACH,CAAA;EACF;AAEA,QAAM,WAAO;IACX,CAAC,QAAQ,MACP,SAAS,CAAC,kBAAkB;MAC1B,SAAS,aAAa;MACtB,SAAS,KAAK,IAAI,GAAG,aAAa,UAAU,KAAK;IAAA,EACjD;IACJ,CAAA;EACF;AAEA,QAAM,cAAU;IACd,CAAC,QAAQ,MACP,SAAS,CAAC,kBAAkB;MAC1B,SAAS,aAAa;MACtB,SAAS,KAAK,IAAI,aAAa,QAAQ,SAAS,GAAG,aAAa,UAAU,KAAK;IAAA,EAC/E;IACJ,CAAA;EACF;AAEM,QAAA,YAAQ,4BAAY,MAAM;AAC9B,aAAS,EAAE,SAAS,CAAC,YAAY,GAAG,SAAS,EAAA,CAAG;EAAA,GAC/C,CAAC,YAAY,CAAC;AAEjB,QAAM,eAAW,wBAAQ,OAAO,EAAE,MAAM,SAAS,OAAO,IAAI,IAAI,CAAC,MAAM,SAAS,OAAO,GAAG,CAAC;AAE3F,SAAO,CAAC,MAAM,QAAQ,MAAM,OAAO,GAAG,UAAU,KAAK;AACvD;;;;ACxDO,SAAS,OAAa,cAAoC;AAC/D,QAAM,aAAS,uBAAO,IAAI,IAAU,YAAY,CAAC;AACjD,QAAM,cAAc,eAAe;AAE5B,SAAA,QAAQ,MAAM,IAAI,SAAS;AAChC,QAAI,UAAU,IAAI,MAAM,OAAO,SAAS,IAAI;AAChC,gBAAA;AACZ,WAAO,OAAO;EAChB;AAEO,SAAA,QAAQ,QAAQ,IAAI,SAAS;AAClC,QAAI,UAAU,MAAM,MAAM,OAAO,SAAS,IAAI;AAClC,gBAAA;EACd;AAEO,SAAA,QAAQ,SAAS,IAAI,SAAS;AACnC,UAAM,MAAM,IAAI,UAAU,OAAO,MAAM,OAAO,SAAS,IAAI;AAC/C,gBAAA;AAEL,WAAA;EACT;AAEA,SAAO,OAAO;AAChB;;;;ACvBO,SAAS,OAAU,QAAsB;AAC9C,QAAM,aAAS,uBAAO,IAAI,IAAI,MAAM,CAAC;AACrC,QAAM,cAAc,eAAe;AAE5B,SAAA,QAAQ,MAAM,IAAI,SAAS;AAChC,UAAM,MAAM,IAAI,UAAU,IAAI,MAAM,OAAO,SAAS,IAAI;AAC5C,gBAAA;AAEL,WAAA;EACT;AAEO,SAAA,QAAQ,QAAQ,IAAI,SAAS;AAClC,QAAI,UAAU,MAAM,MAAM,OAAO,SAAS,IAAI;AAClC,gBAAA;EACd;AAEO,SAAA,QAAQ,SAAS,IAAI,SAAS;AACnC,UAAM,MAAM,IAAI,UAAU,OAAO,MAAM,OAAO,SAAS,IAAI;AAC/C,gBAAA;AAEL,WAAA;EACT;AAEA,SAAO,OAAO;AAChB;;;;ACxBgB,SAAA,qCACd,UACA,MACA;AACM,QAAA,iBAAiB,eAAe,QAAQ;AACxC,QAAA,sBAAkB,uBAAsB,IAAI;AAC5C,QAAA,uBAAmB,uBAAsB,IAAI;AAC7C,QAAA,aAAS,uBAAO,IAAI;AACpB,QAAA,cAAU,uBAAO,IAAI;AACrB,QAAA,iBAAa,uBAAe,EAAE;AAEpC,QAAMC,gBAAe,MAAM,OAAO,aAAa,WAAW,OAAO;AAEjE,QAAM,4BAAwB;IAC5B,IAAI,SAAwB;AAC1B,qBAAe,GAAG,IAAI;AACtB,sBAAgB,UAAU;AAC1B,uBAAiB,UAAU;AAC3B,aAAO,UAAU;IACnB;IACA,CAAC,cAAc;EACjB;AAEM,QAAA,oBAAgB,4BAAY,MAAM;AACtC,QAAI,gBAAgB,WAAW,gBAAgB,YAAY,iBAAiB,SAAS;AAC7D,4BAAA,GAAG,gBAAgB,OAAO;AAEhD,iBAAW,UAAU,OAAO,WAAW,eAAe,QAAQ,OAAO;IAAA,OAChE;AACL,aAAO,UAAU;IAAA;EACnB,GACC,CAAC,qBAAqB,CAAC;AAE1B,QAAM,gBAAY;IAChB,IAAI,SAAwB;AAC1B,UAAI,OAAO,SAAS;AAClB,8BAAsB,GAAG,IAAI;AAC7B,mBAAW,UAAU,OAAO,WAAW,eAAe,QAAQ,OAAO;MAAA,OAChE;AACL,wBAAgB,UAAU;MAAA;IAE9B;IACA,CAAC,uBAAuB,aAAa;EACvC;AAEA,gCAAU,MAAM;AACd,YAAQ,UAAU;EAAA,GACjB,CAAC,IAAI,CAAC;AAEF,SAAA,CAAC,WAAWA,aAAY;AACjC;AAEgB,SAAA,qBAAwD,UAAa,MAAc;AACjG,SAAO,qCAAqC,UAAU,IAAI,EAAE,CAAC;AAC/D;;;;ACtDgB,SAAA,kBAA2B,cAAiB,MAAc;AACxE,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAS,YAAY;AAE/C,QAAM,CAAC,mBAAmBC,aAAY,IAAI,qCAAqC,UAAU,IAAI;AAEnF,gCAAA,MAAMA,eAAc,CAAA,CAAE;AAEzB,SAAA,CAAC,OAAO,iBAAiB;AAClC;;;;ACRgB,SAAA,kBAAqB,OAAU,MAAc;AAC3D,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,yBAAS,KAAK;AACpD,QAAA,eAAW,uBAAO,KAAK;AAEvB,QAAA,CAAC,mBAAmBC,aAAY,IAAI;IACxC;IACA;EACF;AAEA,gCAAU,MAAM;AACV,QAAA,UAAU,SAAS,SAAS;AAC9B,eAAS,UAAU;AACnB,wBAAkB,KAAK;IAAA;EACzB,GACC,CAAC,mBAAmB,KAAK,CAAC;AAEnB,gCAAA,MAAMA,eAAc,CAAA,CAAE;AAEzB,SAAA;AACT;;;;ACpBO,SAAS,mBAAmB;AAC3B,QAAA,gBAAY,uBAAO,IAAI;AAEzB,MAAA,UAAU,YAAY,MAAM;AAC9B,cAAU,UAAU;AACb,WAAA;EAAA;AAGT,SAAO,UAAU;AACnB;;;;ACiBA,SAASC,iBACP,cACA,yBAC0B;;AAC1B,MAAI,yBAAyB;AACpB,WAAA;EAAA;AAGT,MAAI,OAAO,WAAW,eAAe,YAAY,QAAQ;AAChD,WAAA;MACL,SAAO,YAAO,OAAO,gBAAd,mBAA2B,UAAS,aAAa;MACxD,QAAM,YAAO,OAAO,gBAAd,mBAA2B,SAAQ,aAAa;IACxD;EAAA;AAGK,SAAA;AACT;AAEO,SAAS,eAAe;EAC7B,eAAe;EACf,cAAc;EACd,0BAA0B;AAC5B,IAA2B,CAAA,GAA8B;AACjD,QAAA,CAAC,aAAa,cAAc,QAAI;IACpCA;MACE;QACE,OAAO;QACP,MAAM;MACR;MACA;IAAA;EAEJ;AAEM,QAAA,0BAA0B,CAAC,UAAiB;AAChD,UAAM,SAAS,MAAM;AACN,mBAAA,EAAE,QAAO,iCAAQ,UAAS,GAAG,OAAM,iCAAQ,SAAQ,oBAAA,CAAqB;EACzF;AAEA,sBAAoB,MAAM;;AACxB,iBAAO,OAAO,gBAAd,mBAA2B,iBAAiB,UAAU;AACtD,WAAO,MAAA;;AAAM,cAAAC,MAAA,OAAO,OAAO,gBAAd,gBAAAA,IAA2B,oBAAoB,UAAU;;EACxE,GAAG,CAAA,CAAE;AAEE,SAAA;AACT;;;;AClEgB,SAAA,SAAY,KAAa,EAAE,aAAa,MAAM,GAAG,QAA6B,IAAA,CAAA,GAAI;AAChG,QAAM,CAAC,MAAM,OAAO,QAAI,yBAAmB,IAAI;AAC/C,QAAM,CAAC,SAAS,UAAU,QAAI,yBAAS,KAAK;AAC5C,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAuB,IAAI;AAC/C,QAAA,iBAAa,uBAA+B,IAAI;AAEhD,QAAA,cAAU,4BAAY,MAAM;AAChC,QAAI,CAAC,KAAK;AACR;IAAA;AAGF,QAAI,WAAW,SAAS;AACtB,iBAAW,QAAQ,MAAM;IAAA;AAGhB,eAAA,UAAU,IAAI,gBAAgB;AAEzC,eAAW,IAAI;AAER,WAAA,MAAM,KAAK,EAAE,QAAQ,WAAW,QAAQ,QAAQ,GAAG,QAAQ,CAAC,EAChE,KAAK,CAAC,QAAQ,IAAI,KAAA,CAAM,EACxB,KAAK,CAAC,QAAQ;AACb,cAAQ,GAAG;AACX,iBAAW,KAAK;AACT,aAAA;IAAA,CACR,EACA,MAAM,CAAC,QAAQ;AACd,iBAAW,KAAK;AAEZ,UAAA,IAAI,SAAS,cAAc;AAC7B,iBAAS,GAAG;MAAA;AAGP,aAAA;IAAA,CACR;EAAA,GACF,CAAC,GAAG,CAAC;AAEF,QAAA,YAAQ,4BAAY,MAAM;;AAC9B,QAAI,WAAW,SAAS;AACX,uBAAA,YAAA,mBAAS,MAAM;IAAE;EAEhC,GAAG,CAAA,CAAE;AAEL,gCAAU,MAAM;AACd,QAAI,YAAY;AACN,cAAA;IAAA;AAGV,WAAO,MAAM;AACX,UAAI,WAAW,SAAS;AACX,mBAAA,QAAQ,MAAM,EAAE;MAAA;IAE/B;EAAA,GACC,CAAC,SAAS,UAAU,CAAC;AAExB,SAAO,EAAE,MAAM,SAAS,OAAO,SAAS,MAAM;AAChD;;;;AC3DA,SAAS,iBAAiB,SAAiB;AAClC,SAAA,WAAW,MAAM,KAAK;AAC/B;AAEA,SAAS,iBAAiB,SAAsB;AACxC,QAAA,OAAO,QAAQ,sBAAsB;AACpC,SAAA,CAAC,KAAK,OAAO,KAAK,QAAQ,GAAG,KAAK,MAAM,KAAK,SAAS,CAAC;AAChE;AAEA,SAAS,SAAS,aAA+B,SAAsB;AAC/D,QAAA,SAAS,iBAAiB,OAAO;AACvC,QAAM,IAAI,YAAY,CAAC,IAAI,OAAO,CAAC;AACnC,QAAM,IAAI,YAAY,CAAC,IAAI,OAAO,CAAC;AACnC,QAAM,MAAM,iBAAiB,KAAK,MAAM,GAAG,CAAC,CAAC,IAAI;AACjD,SAAO,MAAM;AACf;AAEA,SAAS,QAAQ,OAAe,QAAgB;AAC9C,SAAO,WAAW,MAAM,QAAQ,MAAM,CAAC;AACzC;AAEA,SAAS,kBAAkB,OAAe;;AACjC,WAAA,WAAM,SAAA,EAAW,MAAM,GAAG,EAAE,CAAC,MAA7B,mBAAgC,WAAU;AACnD;AAEgB,SAAA,qBAAqB,QAAgB,MAAc;AACjE,QAAM,UAAU,MAAM,QAAQ,GAAG,GAAG;AACpC,QAAM,OAAO,KAAK,KAAK,UAAU,IAAI;AACrC,QAAM,MAAM,KAAK,MAAM,UAAU,IAAI;AAC9B,SAAA;IACL,QAAQ,UAAU,OAAQ,OAAO,SAAS,MAAM,IAAI,OAAO,OAAQ,MAAM;IACzE,kBAAkB,IAAI;EACxB;AACF;AAgBgB,SAAA,cACd,UACA,EAAE,OAAO,MAAM,aAAa,cAAc,WAAqC,IAAA,CAAA,GAC/E;AACM,QAAA,UAAM,uBAAU,IAAI;AACpB,QAAA,cAAU,uBAAgB,KAAK;AACrC,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,KAAK;AAE1C,gCAAU,MAAM;AACd,YAAQ,UAAU;EACpB,GAAG,CAAA,CAAE;AAEL,gCAAU,MAAM;AACd,UAAM,OAAO,IAAI;AAEjB,UAAM,SAAS,CAAC,OAAmB,OAAO,UAAU;AAClD,UAAI,MAAM;AACR,aAAK,MAAM,aAAa;AAClB,cAAA,MAAM,SAAS,CAAC,MAAM,SAAS,MAAM,OAAO,GAAG,IAAI;AACzD,cAAM,WAAW,qBAAqB,KAAK,QAAQ,CAAC;AAEpD,iBAAS,QAAQ;AACjB,iBAAQ,2CAAc;MAAQ;IAElC;AAEA,UAAM,gBAAgB,MAAM;AACX;AACf,gBAAU,IAAI;AACL,eAAA,iBAAiB,aAAa,iBAAiB,KAAK;AACpD,eAAA,iBAAiB,WAAW,eAAe,KAAK;AACzD,eAAS,iBAAiB,aAAa,iBAAiB,EAAE,SAAS,MAAA,CAAO;AACjE,eAAA,iBAAiB,YAAY,gBAAgB,KAAK;IAC7D;AAEA,UAAM,cAAc,MAAM;AACX;AACb,gBAAU,KAAK;AACN,eAAA,oBAAoB,aAAa,iBAAiB,KAAK;AACvD,eAAA,oBAAoB,WAAW,eAAe,KAAK;AACnD,eAAA,oBAAoB,aAAa,iBAAiB,KAAK;AACvD,eAAA,oBAAoB,YAAY,gBAAgB,KAAK;IAChE;AAEM,UAAA,cAAc,CAAC,UAAsB;AAC3B,oBAAA;AACd,aAAO,KAAK;IACd;AAEM,UAAA,kBAAkB,CAAC,UAAsB;AAC7C,aAAO,KAAK;IACd;AAEM,UAAA,gBAAgB,CAAC,UAAsB;AAC3C,aAAO,OAAO,IAAI;AACN,kBAAA;IACd;AAEM,UAAA,kBAAkB,CAAC,UAAsB;AAC7C,YAAM,eAAe;AACd,aAAA,MAAM,QAAQ,CAAC,CAAQ;IAChC;AAEM,UAAA,iBAAiB,CAAC,UAAsB;AAC5C,aAAO,MAAM,eAAe,CAAC,GAAU,IAAI;AAC/B,kBAAA;IACd;AAEM,UAAA,mBAAmB,CAAC,UAAsB;AAC9C,YAAM,eAAe;AACP,oBAAA;AACP,aAAA,MAAM,QAAQ,CAAC,CAAQ;IAChC;AAEM,iCAAA,iBAAiB,aAAa;AACpC,iCAAM,iBAAiB,cAAc,kBAAkB,EAAE,SAAS,MAAA;AAElE,WAAO,MAAM;AACX,UAAI,MAAM;AACH,aAAA,oBAAoB,aAAa,WAAW;AAC5C,aAAA,oBAAoB,cAAc,gBAAgB;MAAA;IAE3D;EAAA,GACC,CAAC,QAAQ,CAAC;AAEN,SAAA,EAAE,KAAK,OAAO;AACvB;;;;ACvIA,SAAS,gBACP,UACA,UACA,UAC2B;AAC3B,QAAM,SAAoC,CAAC;AAE3C,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AACrC,UAAA,UAAU,SAAS,CAAC;AAC1B,WAAO,KAAK;MACV,OAAO,SAAS,OAAO;MACvB,OAAO,SAAS,OAAO;MACvB,IAAI,QAAQ,MAAM,SAAS;MAC3B,SAAS,MAAO,QAAQ,KAAK,SAAS,eAAe,QAAQ,EAAE,IAAK;IAAA,CACrE;EAAA;AAGI,SAAA;AACT;AAEA,SAAS,iBAAiB,OAAkB;AACtC,MAAA,MAAM,WAAW,GAAG;AACf,WAAA;EAAA;AAGT,QAAM,UAAU,MAAM;IACpB,CAAC,KAAK,MAAM,UAAU;AAChB,UAAA,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,GAAG;AACtC,eAAA;MAAA;AAGF,aAAA;QACL;QACA,UAAU,KAAK;MACjB;IACF;IACA,EAAE,OAAO,GAAG,UAAU,MAAM,CAAC,EAAE,EAAE;EACnC;AAEA,SAAO,QAAQ;AACjB;AAEA,SAAS,gBAAgB,SAAsB;AAC7C,SAAO,OAAO,QAAQ,QAAQ,CAAC,CAAC;AAClC;AAEA,SAAS,gBAAgB,SAAsB;AAC7C,SAAO,QAAQ,eAAe;AAChC;AAyCO,SAAS,aAAa;EAC3B,WAAW;EACX,WAAW;EACX,WAAW;AACb,IAAyB,CAAA,GAA4B;AACnD,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,EAAE;AACvC,QAAM,CAAC,aAAa,cAAc,QAAI,yBAAS,KAAK;AACpD,QAAM,CAAC,MAAM,OAAO,QAAI,yBAAoC,CAAA,CAAE;AACxD,QAAA,kBAAc,uBAAkC,CAAA,CAAE;AAExD,QAAM,eAAe,MAAM;AACzB;MACE,iBAAiB,YAAY,QAAQ,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IACtF;EACF;AAEA,QAAM,aAAa,MAAM;AACvB,UAAM,WAAW;MACf,MAAM,KAAK,SAAS,iBAAiB,QAAQ,CAAC;MAC9C;MACA;IACF;AACA,gBAAY,UAAU;AACtB,mBAAe,IAAI;AACnB,YAAQ,QAAQ;AACN,cAAA,iBAAiB,SAAS,IAAI,CAAC,MAAM,EAAE,QAAU,EAAA,sBAAuB,CAAA,CAAC,CAAC;EACtF;AAEA,gCAAU,MAAM;AACH,eAAA;AACJ,WAAA,iBAAiB,UAAU,YAAY;AAC9C,WAAO,MAAM,OAAO,oBAAoB,UAAU,YAAY;EAChE,GAAG,CAAA,CAAE;AAEE,SAAA;IACL,cAAc;IACd;IACA;IACA;EACF;AACF;;;;ACvGA,IAAM,iBAAuC;EAC3C,UAAU;EACV,QAAQ;AACV;AAEA,SAAS,oBAAoB,OAA8D;AACzF,MAAI,CAAC,OAAO;AACH,WAAA;EAAA;AAGT,MAAI,iBAAiB,UAAU;AACtB,WAAA;EAAA;AAGH,QAAA,SAAS,IAAI,aAAa;AAChC,aAAW,QAAQ,OAAO;AACjB,WAAA,MAAM,IAAI,IAAI;EAAA;AAGvB,SAAO,OAAO;AAChB;AAEA,SAAS,YAAY,SAA+B;AAC9C,MAAA,OAAO,aAAa,aAAa;AAC5B,WAAA;EAAA;AAGH,QAAA,QAAQ,SAAS,cAAc,OAAO;AAC5C,QAAM,OAAO;AAEb,MAAI,QAAQ,QAAQ;AAClB,UAAM,SAAS,QAAQ;EAAA;AAGzB,MAAI,QAAQ,UAAU;AACpB,UAAM,WAAW,QAAQ;EAAA;AAG3B,MAAI,QAAQ,SAAS;AACnB,UAAM,UAAU,QAAQ;EAAA;AAG1B,MAAI,QAAQ,WAAW;AACrB,UAAM,kBAAkB,QAAQ;EAAA;AAGlC,QAAM,MAAM,UAAU;AACf,SAAA;AACT;AAEgB,SAAA,cAAc,QAA8B,CAAA,GAAI;AAC9D,QAAM,UAAgC,EAAE,GAAG,gBAAgB,GAAG,MAAM;AAC9D,QAAA,CAAC,OAAO,QAAQ,QAAI,yBAA0B,oBAAoB,QAAQ,YAAY,CAAC;AACvF,QAAA,eAAW,uBAAgC,IAAI;AAErD,QAAM,mBAAe;IACnB,CAAC,UAAiB;;AAChB,YAAM,SAAS,MAAM;AACrB,UAAI,iCAAQ,OAAO;AACjB,iBAAS,OAAO,KAAK;AACb,sBAAA,aAAA,iCAAW,OAAO;MAAK;IAEnC;IACA,CAAC,QAAQ,QAAQ;EACnB;AAEM,QAAA,0BAAsB,4BAAY,MAAM;;AAC5C,mBAAS,YAAT,mBAAkB;AACT,aAAA,UAAU,YAAY,OAAO;AAEtC,QAAI,SAAS,SAAS;AACpB,eAAS,QAAQ,iBAAiB,UAAU,cAAc,EAAE,MAAM,KAAA,CAAM;AAC/D,eAAA,KAAK,YAAY,SAAS,OAAO;IAAA;EAC5C,GACC,CAAC,SAAS,YAAY,CAAC;AAE1B,sBAAoB,MAAM;AACJ,wBAAA;AACb,WAAA,MAAM;;AAAA,4BAAS,YAAT,mBAAkB;;EACjC,GAAG,CAAA,CAAE;AAEC,QAAA,YAAQ,4BAAY,MAAM;;AAC9B,aAAS,IAAI;AACb,kBAAQ,aAAR,iCAAmB;EAAI,GACtB,CAAC,QAAQ,QAAQ,CAAC;AAEf,QAAA,WAAO,4BAAY,MAAM;;AAC7B,QAAI,QAAQ,aAAa;AACjB,YAAA;IAAA;AAGY,wBAAA;AACpB,mBAAS,YAAT,mBAAkB;EAAM,GACvB,CAAC,QAAQ,aAAa,OAAO,mBAAmB,CAAC;AAE7C,SAAA,EAAE,OAAO,MAAM,MAAM;AAC9B;", "names": ["clearTimeout", "tabbable", "React", "DEFAULT_EVENTS", "DEFAULT_OPTIONS", "range", "element", "clearTimeout", "clearTimeout", "clearTimeout", "getInitialValue", "_a"]}