{"hash": "f5be509d", "configHash": "cf513eb5", "lockfileHash": "019acedb", "browserHash": "dd1c8a78", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "f251d49b", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "542271c7", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "27706726", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "dc42c28a", "needsInterop": true}, "@mantine/core": {"src": "../../@mantine/core/esm/index.mjs", "file": "@mantine_core.js", "fileHash": "6440dcda", "needsInterop": false}, "@mantine/dates": {"src": "../../@mantine/dates/esm/index.mjs", "file": "@mantine_dates.js", "fileHash": "a93b49c8", "needsInterop": false}, "@mantine/hooks": {"src": "../../@mantine/hooks/esm/index.mjs", "file": "@mantine_hooks.js", "fileHash": "d69f8911", "needsInterop": false}, "@mantine/modals": {"src": "../../@mantine/modals/esm/index.mjs", "file": "@mantine_modals.js", "fileHash": "06768b6b", "needsInterop": false}, "@mantine/notifications": {"src": "../../@mantine/notifications/esm/index.mjs", "file": "@mantine_notifications.js", "fileHash": "541ede85", "needsInterop": false}, "@tabler/icons-react": {"src": "../../@tabler/icons-react/dist/esm/tabler-icons-react.js", "file": "@tabler_icons-react.js", "fileHash": "7acc0516", "needsInterop": false}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "0b09b000", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "67e39450", "needsInterop": true}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "2f4c75b9", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "0f9b7a23", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "409358f1", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "c0df3e25", "needsInterop": false}}, "chunks": {"chunk-ZOS4QECH": {"file": "chunk-ZOS4QECH.js"}, "chunk-3XLQFOK2": {"file": "chunk-3XLQFOK2.js"}, "chunk-EQCCHGRT": {"file": "chunk-EQCCHGRT.js"}, "chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-WLVB5OIP": {"file": "chunk-WLVB5OIP.js"}, "chunk-GQZSMCNZ": {"file": "chunk-GQZSMCNZ.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}